// Security Hardening Configuration for L&D Apps
// This file contains security middleware and configurations

import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { Request, Response, NextFunction } from 'express';
import { createHash, randomBytes } from 'crypto';
import { promisify } from 'util';

// Security Headers Configuration
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://api.ld-apps.company.com'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  permittedCrossDomainPolicies: false,
  crossOriginEmbedderPolicy: false, // Disable for development
});

// Rate Limiting Configuration
export const createRateLimiter = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: 'Too many requests from this IP address',
        retryAfter: Math.ceil(windowMs / 1000),
      });
    },
  });
};

// Authentication Rate Limiting
export const authRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts
  'Too many authentication attempts'
);

// API Rate Limiting
export const apiRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests
  'API rate limit exceeded'
);

// Speed Limiting (Progressive Delay)
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 5000, // Maximum delay of 5 seconds
});

// Account Lockout Mechanism
class AccountLockoutManager {
  private attempts: Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }> = new Map();
  private readonly maxAttempts = 5;
  private readonly lockoutDuration = 15 * 60 * 1000; // 15 minutes
  private readonly attemptWindow = 15 * 60 * 1000; // 15 minutes

  isLocked(identifier: string): boolean {
    const record = this.attempts.get(identifier);
    if (!record?.lockedUntil) return false;
    
    if (record.lockedUntil > new Date()) {
      return true;
    }
    
    // Lockout expired, reset
    this.attempts.delete(identifier);
    return false;
  }

  recordAttempt(identifier: string, success: boolean): void {
    if (success) {
      this.attempts.delete(identifier);
      return;
    }

    const now = new Date();
    const record = this.attempts.get(identifier);

    if (!record) {
      this.attempts.set(identifier, { count: 1, lastAttempt: now });
      return;
    }

    // Reset if outside attempt window
    if (now.getTime() - record.lastAttempt.getTime() > this.attemptWindow) {
      this.attempts.set(identifier, { count: 1, lastAttempt: now });
      return;
    }

    record.count++;
    record.lastAttempt = now;

    if (record.count >= this.maxAttempts) {
      record.lockedUntil = new Date(now.getTime() + this.lockoutDuration);
    }
  }

  getRemainingLockout(identifier: string): number {
    const record = this.attempts.get(identifier);
    if (!record?.lockedUntil) return 0;
    
    const remaining = record.lockedUntil.getTime() - Date.now();
    return Math.max(0, Math.ceil(remaining / 1000)); // seconds
  }
}

export const accountLockout = new AccountLockoutManager();

// Input Validation and Sanitization
export const validateAndSanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Remove potentially dangerous characters
  const sanitizeString = (str: string): string => {
    return str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  };

  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return sanitizeString(obj);
    }
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }

  next();
};

// Request Size Limiting
export const requestSizeLimit = (maxSize: number = 10 * 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0', 10);
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        error: 'Request too large',
        message: `Request size exceeds ${maxSize} bytes`,
      });
    }

    next();
  };
};

// CSRF Token Generation and Validation
export class CSRFProtection {
  private tokens: Map<string, { token: string; expires: Date }> = new Map();

  generateToken(sessionId: string): string {
    const token = randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    
    this.tokens.set(sessionId, { token, expires });
    return token;
  }

  validateToken(sessionId: string, token: string): boolean {
    const record = this.tokens.get(sessionId);
    if (!record) return false;
    
    if (record.expires < new Date()) {
      this.tokens.delete(sessionId);
      return false;
    }
    
    return record.token === token;
  }

  middleware = (req: Request, res: Response, next: NextFunction) => {
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next();
    }

    const sessionId = req.session?.id || req.headers['x-session-id'];
    const token = req.headers['x-csrf-token'] || req.body.csrfToken;

    if (!sessionId || !token || !this.validateToken(sessionId as string, token as string)) {
      return res.status(403).json({
        error: 'Invalid CSRF token',
        message: 'CSRF token validation failed',
      });
    }

    next();
  };
}

export const csrfProtection = new CSRFProtection();

// Audit Logging Middleware
export const auditLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  const originalEnd = res.end;

  // Capture request details
  const requestDetails = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    userId: req.user?.id || 'anonymous',
    sessionId: req.session?.id,
    requestId: req.headers['x-request-id'] || randomBytes(16).toString('hex'),
  };

  // Override res.end to capture response details
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - start;
    const responseDetails = {
      ...requestDetails,
      statusCode: res.statusCode,
      duration,
      responseSize: res.get('content-length') || 0,
    };

    // Log security-relevant events
    if (res.statusCode >= 400 || req.url.includes('/auth/') || req.url.includes('/admin/')) {
      console.log(JSON.stringify({
        type: 'security_audit',
        event: 'request_completed',
        ...responseDetails,
      }));
    }

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Secure Session Configuration
export const secureSessionConfig = {
  secret: process.env.SESSION_SECRET || randomBytes(32).toString('hex'),
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: 'strict' as const,
  },
  name: 'sessionId',
  rolling: true, // Reset expiry on activity
};

// Security Middleware Stack
export const securityMiddleware = [
  securityHeaders,
  speedLimiter,
  apiRateLimit,
  requestSizeLimit(),
  validateAndSanitizeInput,
  auditLogger,
];

// Environment-specific Security Configuration
export const getSecurityConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    cors: {
      origin: isProduction 
        ? ['https://ld-apps.company.com', 'https://api.ld-apps.company.com']
        : ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3003'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Session-ID'],
      exposedHeaders: ['X-CSRF-Token'],
    },
    rateLimit: {
      auth: { windowMs: 15 * 60 * 1000, max: isProduction ? 5 : 10 },
      api: { windowMs: 15 * 60 * 1000, max: isProduction ? 100 : 1000 },
    },
    session: {
      ...secureSessionConfig,
      cookie: {
        ...secureSessionConfig.cookie,
        secure: isProduction,
        maxAge: isProduction ? 4 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 4h prod, 24h dev
      },
    },
  };
};

// Security Health Check
export const securityHealthCheck = () => {
  const checks = {
    helmet: !!helmet,
    rateLimit: !!rateLimit,
    sessionSecret: !!process.env.SESSION_SECRET,
    jwtSecret: !!process.env.JWT_SECRET,
    databaseEncryption: !!process.env.DATABASE_URL?.includes('ssl=true'),
    httpsOnly: process.env.NODE_ENV === 'production' ? 
      !!process.env.HTTPS_ONLY : true,
  };

  const passed = Object.values(checks).every(Boolean);
  
  return {
    status: passed ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString(),
  };
};