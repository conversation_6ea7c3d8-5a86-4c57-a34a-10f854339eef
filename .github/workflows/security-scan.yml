name: Security Scanning

on:
  schedule:
    - cron: '0 3 * * 1'  # Weekly on Monday at 3 AM
  workflow_dispatch:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd Command-Center && npm ci
        cd ../shared-ui && npm ci
        cd ../training-need-analysis && npm ci
        cd ../vendors && npm ci
        cd ../wins-of-week && npm ci
    
    - name: Run npm audit
      run: |
        echo "## Backend Dependencies" > security-report.md
        cd Command-Center
        npm audit --audit-level=moderate --format=json > ../backend-audit.json || true
        npm audit --audit-level=moderate >> ../security-report.md || true
        
        echo -e "\n## Frontend Dependencies" >> ../security-report.md
        cd ../shared-ui
        npm audit --audit-level=moderate >> ../security-report.md || true
        
        cd ../training-need-analysis
        npm audit --audit-level=moderate >> ../security-report.md || true
        
        cd ../vendors
        npm audit --audit-level=moderate >> ../security-report.md || true
        
        cd ../wins-of-week
        npm audit --audit-level=moderate >> ../security-report.md || true
    
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --all-projects --severity-threshold=medium
        command: test
    
    - name: Upload security report
      uses: actions/upload-artifact@v4
      with:
        name: security-report-${{ github.run_number }}
        path: |
          security-report.md
          backend-audit.json
        retention-days: 30
  
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Build Docker images
      run: |
        docker build -t ld-backend:scan ./Command-Center
        docker build -t ld-training:scan ./training-need-analysis
        docker build -t ld-vendors:scan ./vendors
        docker build -t ld-wins:scan ./wins-of-week
    
    - name: Run Trivy vulnerability scanner - Backend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ld-backend:scan'
        format: 'sarif'
        output: 'trivy-backend.sarif'
    
    - name: Run Trivy vulnerability scanner - Training
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ld-training:scan'
        format: 'sarif'
        output: 'trivy-training.sarif'
    
    - name: Run Trivy vulnerability scanner - Vendors
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ld-vendors:scan'
        format: 'sarif'
        output: 'trivy-vendors.sarif'
    
    - name: Run Trivy vulnerability scanner - Wins
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ld-wins:scan'
        format: 'sarif'
        output: 'trivy-wins.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-backend.sarif'
        category: 'backend-container'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-training.sarif'
        category: 'training-container'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-vendors.sarif'
        category: 'vendors-container'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-wins.sarif'
        category: 'wins-container'
  
  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: javascript
        queries: security-and-quality
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:javascript"
    
    - name: Run ESLint security scan
      run: |
        cd Command-Center
        npm ci
        npx eslint . --ext .js,.ts --format json --output-file ../eslint-results.json || true
        npx eslint . --ext .js,.ts --format checkstyle --output-file ../eslint-checkstyle.xml || true
    
    - name: Upload ESLint results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'eslint-checkstyle.xml'
        category: 'eslint-security'
  
  secrets-scan:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified
    
    - name: Run GitLeaks
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Check for hardcoded secrets
      run: |
        echo "Checking for potential hardcoded secrets..."
        
        # Check for common patterns
        if grep -r "password.*=" . --include="*.js" --include="*.ts" --include="*.json" --exclude-dir=node_modules; then
          echo "WARNING: Found potential hardcoded passwords"
        fi
        
        if grep -r "api[_-]key.*=" . --include="*.js" --include="*.ts" --include="*.json" --exclude-dir=node_modules; then
          echo "WARNING: Found potential hardcoded API keys"
        fi
        
        if grep -r "secret.*=" . --include="*.js" --include="*.ts" --include="*.json" --exclude-dir=node_modules; then
          echo "WARNING: Found potential hardcoded secrets"
        fi
        
        echo "Secrets scan completed"
  
  infrastructure-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: k8s/
        framework: kubernetes
        output_format: sarif
        output_file_path: checkov-k8s.sarif
    
    - name: Upload Checkov scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'checkov-k8s.sarif'
        category: 'infrastructure-security'
    
    - name: Run kube-score
      run: |
        curl -L https://github.com/zegl/kube-score/releases/download/v1.16.1/kube-score_1.16.1_linux_amd64.tar.gz | tar xz
        chmod +x kube-score
        
        echo "## Kubernetes Security Analysis" > k8s-security-report.md
        ./kube-score score k8s/base/*.yaml >> k8s-security-report.md || true
        ./kube-score score k8s/overlays/production/*.yaml >> k8s-security-report.md || true
    
    - name: Upload K8s security report
      uses: actions/upload-artifact@v4
      with:
        name: k8s-security-report-${{ github.run_number }}
        path: k8s-security-report.md
        retention-days: 30
  
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, container-scan, sast-scan, secrets-scan, infrastructure-scan]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
    
    - name: Generate comprehensive security report
      run: |
        echo "# L&D Apps Security Report" > comprehensive-security-report.md
        echo "Generated on: $(date)" >> comprehensive-security-report.md
        echo "Commit: ${{ github.sha }}" >> comprehensive-security-report.md
        echo "" >> comprehensive-security-report.md
        
        echo "## Summary" >> comprehensive-security-report.md
        echo "- **Dependency Scan**: $(ls security-report-*/security-report.md 2>/dev/null && echo 'Completed' || echo 'Failed')" >> comprehensive-security-report.md
        echo "- **Container Scan**: $(ls trivy-*.sarif 2>/dev/null && echo 'Completed' || echo 'Failed')" >> comprehensive-security-report.md
        echo "- **SAST Scan**: $(ls eslint-*.json 2>/dev/null && echo 'Completed' || echo 'Failed')" >> comprehensive-security-report.md
        echo "- **Secrets Scan**: Completed" >> comprehensive-security-report.md
        echo "- **Infrastructure Scan**: $(ls k8s-security-report-*/k8s-security-report.md 2>/dev/null && echo 'Completed' || echo 'Failed')" >> comprehensive-security-report.md
        echo "" >> comprehensive-security-report.md
        
        # Include dependency scan results if available
        if [ -f security-report-*/security-report.md ]; then
          echo "## Dependency Vulnerabilities" >> comprehensive-security-report.md
          cat security-report-*/security-report.md >> comprehensive-security-report.md
          echo "" >> comprehensive-security-report.md
        fi
        
        # Include K8s security results if available
        if [ -f k8s-security-report-*/k8s-security-report.md ]; then
          cat k8s-security-report-*/k8s-security-report.md >> comprehensive-security-report.md
          echo "" >> comprehensive-security-report.md
        fi
        
        echo "## Recommendations" >> comprehensive-security-report.md
        echo "1. Regularly update dependencies to latest secure versions" >> comprehensive-security-report.md
        echo "2. Review and address any high/critical vulnerabilities immediately" >> comprehensive-security-report.md
        echo "3. Implement secrets management solution (e.g., HashiCorp Vault)" >> comprehensive-security-report.md
        echo "4. Enable security monitoring and alerting" >> comprehensive-security-report.md
        echo "5. Conduct regular security audits and penetration testing" >> comprehensive-security-report.md
    
    - name: Upload comprehensive security report
      uses: actions/upload-artifact@v4
      with:
        name: comprehensive-security-report-${{ github.run_number }}
        path: comprehensive-security-report.md
        retention-days: 90
    
    - name: Send security alert
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#ld-apps-security'
        text: |
          🔒 Security scan failed for L&D Apps
          
          Please review the security report: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          
          Immediate action may be required for critical vulnerabilities.
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}