name: Performance Testing

on:
  schedule:
    - cron: '0 2 * * 1'  # Weekly on Monday at 2 AM
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      duration:
        description: 'Test duration'
        required: true
        default: '10m'
        type: string
      virtual_users:
        description: 'Number of virtual users'
        required: true
        default: '50'
        type: number

jobs:
  performance-test:
    name: Run Performance Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup k6
      uses: grafana/setup-k6-action@v1
    
    - name: Set environment variables
      run: |
        if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
          echo "BASE_URL=https://api.ld-apps.company.com" >> $GITHUB_ENV
        else
          echo "BASE_URL=https://api.staging.ld-apps.company.com" >> $GITHUB_ENV
        fi
        echo "DURATION=${{ github.event.inputs.duration || '10m' }}" >> $GITHUB_ENV
        echo "VUS=${{ github.event.inputs.virtual_users || '50' }}" >> $GITHUB_ENV
    
    - name: Run comprehensive load test
      run: |
        k6 run \
          --vus ${{ env.VUS }} \
          --duration ${{ env.DURATION }} \
          --env BASE_URL=${{ env.BASE_URL }} \
          --out csv=performance-results.csv \
          --out json=performance-results.json \
          Command-Center/performance/integrated.load.js
    
    - name: Run spike test
      run: |
        k6 run \
          --stage 2m:10 \
          --stage 1m:100 \
          --stage 2m:10 \
          --stage 1m:0 \
          --env BASE_URL=${{ env.BASE_URL }} \
          --out csv=spike-results.csv \
          Command-Center/performance/spike.load.js
    
    - name: Run stress test
      run: |
        k6 run \
          --stage 5m:10 \
          --stage 5m:50 \
          --stage 10m:100 \
          --stage 5m:50 \
          --stage 5m:10 \
          --stage 5m:0 \
          --env BASE_URL=${{ env.BASE_URL }} \
          --out csv=stress-results.csv \
          Command-Center/performance/stress.load.js
    
    - name: Parse results
      run: |
        echo "## Performance Test Results" > performance-report.md
        echo "Environment: ${{ github.event.inputs.environment || 'staging' }}" >> performance-report.md
        echo "Duration: ${{ env.DURATION }}" >> performance-report.md
        echo "Virtual Users: ${{ env.VUS }}" >> performance-report.md
        echo "" >> performance-report.md
        
        # Extract key metrics from JSON
        if [ -f performance-results.json ]; then
          echo "### Load Test Results" >> performance-report.md
          echo "- Average Response Time: $(jq -r '.metrics.http_req_duration.avg' performance-results.json)ms" >> performance-report.md
          echo "- 95th Percentile Response Time: $(jq -r '.metrics.http_req_duration.p95' performance-results.json)ms" >> performance-report.md
          echo "- Request Rate: $(jq -r '.metrics.http_reqs.rate' performance-results.json) req/s" >> performance-report.md
          echo "- Error Rate: $(jq -r '.metrics.http_req_failed.rate' performance-results.json)" >> performance-report.md
        fi
    
    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: performance-results-${{ github.event.inputs.environment || 'staging' }}-${{ github.run_number }}
        path: |
          performance-results.csv
          performance-results.json
          spike-results.csv
          stress-results.csv
          performance-report.md
        retention-days: 30
    
    - name: Comment on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('performance-report.md', 'utf8');
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: report
          });
    
    - name: Check performance thresholds
      run: |
        # Check if response times are within acceptable limits
        AVG_RESPONSE_TIME=$(jq -r '.metrics.http_req_duration.avg' performance-results.json)
        P95_RESPONSE_TIME=$(jq -r '.metrics.http_req_duration.p95' performance-results.json)
        ERROR_RATE=$(jq -r '.metrics.http_req_failed.rate' performance-results.json)
        
        echo "Average Response Time: ${AVG_RESPONSE_TIME}ms"
        echo "95th Percentile Response Time: ${P95_RESPONSE_TIME}ms"
        echo "Error Rate: ${ERROR_RATE}"
        
        # Fail if thresholds are exceeded
        if (( $(echo "${AVG_RESPONSE_TIME} > 1000" | bc -l) )); then
          echo "ERROR: Average response time (${AVG_RESPONSE_TIME}ms) exceeds 1000ms threshold"
          exit 1
        fi
        
        if (( $(echo "${P95_RESPONSE_TIME} > 2000" | bc -l) )); then
          echo "ERROR: 95th percentile response time (${P95_RESPONSE_TIME}ms) exceeds 2000ms threshold"
          exit 1
        fi
        
        if (( $(echo "${ERROR_RATE} > 0.01" | bc -l) )); then
          echo "ERROR: Error rate (${ERROR_RATE}) exceeds 1% threshold"
          exit 1
        fi
        
        echo "All performance thresholds passed!"
    
    - name: Send Slack notification
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#ld-apps-alerts'
        text: |
          🚨 Performance test failed for ${{ github.event.inputs.environment || 'staging' }} environment
          
          Check the results: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}