name: L&D Apps CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: ld_apps_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies - Backend
      working-directory: ./Command-Center
      run: |
        npm ci
        npx prisma generate
    
    - name: Install dependencies - Frontend Apps
      run: |
        cd shared-ui && npm ci
        cd ../training-need-analysis && npm ci
        cd ../vendors && npm ci
        cd ../wins-of-week && npm ci
    
    - name: Run database migrations
      working-directory: ./Command-Center
      run: npx prisma migrate deploy
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/ld_apps_test
    
    - name: Run backend unit tests
      working-directory: ./Command-Center
      run: npm run test
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/ld_apps_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
        JWT_REFRESH_SECRET: test-refresh-secret
    
    - name: Run backend integration tests
      working-directory: ./Command-Center
      run: npm run test:e2e
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/ld_apps_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
        JWT_REFRESH_SECRET: test-refresh-secret
    
    - name: Run frontend unit tests
      run: |
        cd shared-ui && npm test
        cd ../training-need-analysis && npm test
        cd ../vendors && npm test
        cd ../wins-of-week && npm test
    
    - name: Build backend
      working-directory: ./Command-Center
      run: npm run build
    
    - name: Build frontend apps
      run: |
        cd training-need-analysis && npm run build
        cd ../vendors && npm run build
        cd ../wins-of-week && npm run build
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./Command-Center/coverage/lcov.info
        flags: backend
        name: backend-coverage
  
  e2e-test:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      working-directory: ./Command-Center
      run: |
        npm ci
        npx prisma generate
    
    - name: Install Playwright
      working-directory: ./Command-Center
      run: npx playwright install --with-deps
    
    - name: Start services with Docker Compose
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30
    
    - name: Run E2E tests
      working-directory: ./Command-Center
      run: npx playwright test
      env:
        BASE_URL: http://localhost:3000
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: ./Command-Center/playwright-report/
        retention-days: 30
    
    - name: Stop services
      if: always()
      run: docker-compose -f docker-compose.test.yml down
  
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run npm audit
      run: |
        cd Command-Center && npm audit --audit-level=high
        cd ../shared-ui && npm audit --audit-level=high
        cd ../training-need-analysis && npm audit --audit-level=high
        cd ../vendors && npm audit --audit-level=high
        cd ../wins-of-week && npm audit --audit-level=high
  
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, e2e-test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    strategy:
      matrix:
        service: [backend, training, vendors, wins]
        include:
          - service: backend
            context: ./Command-Center
            image: ld-backend
          - service: training
            context: ./training-need-analysis
            image: ld-training
          - service: vendors
            context: ./vendors
            image: ld-vendors
          - service: wins
            context: ./wins-of-week
            image: ld-wins
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.image }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.context }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
  
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > ~/.kube/config
        chmod 600 ~/.kube/config
    
    - name: Deploy to staging
      run: |
        cd k8s/overlays/staging
        kustomize edit set image \
          registry.company.com/ld-apps/backend=${{ env.REGISTRY }}/${{ github.repository }}/ld-backend:develop \
          registry.company.com/ld-apps/training=${{ env.REGISTRY }}/${{ github.repository }}/ld-training:develop \
          registry.company.com/ld-apps/vendors=${{ env.REGISTRY }}/${{ github.repository }}/ld-vendors:develop \
          registry.company.com/ld-apps/wins=${{ env.REGISTRY }}/${{ github.repository }}/ld-wins:develop
        kubectl apply -k .
    
    - name: Wait for deployment
      run: |
        kubectl rollout status deployment/staging-backend -n ld-apps-staging --timeout=300s
        kubectl rollout status deployment/staging-training-frontend -n ld-apps-staging --timeout=300s
        kubectl rollout status deployment/staging-vendors-frontend -n ld-apps-staging --timeout=300s
        kubectl rollout status deployment/staging-wins-frontend -n ld-apps-staging --timeout=300s
    
    - name: Run smoke tests
      run: |
        kubectl run smoke-test --image=curlimages/curl:latest --rm -it --restart=Never -- \
          curl -f http://staging-backend-service.ld-apps-staging.svc.cluster.local:3000/health
  
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > ~/.kube/config
        chmod 600 ~/.kube/config
    
    - name: Deploy to production
      run: |
        cd k8s/overlays/production
        kustomize edit set image \
          registry.company.com/ld-apps/backend=${{ env.REGISTRY }}/${{ github.repository }}/ld-backend:main \
          registry.company.com/ld-apps/training=${{ env.REGISTRY }}/${{ github.repository }}/ld-training:main \
          registry.company.com/ld-apps/vendors=${{ env.REGISTRY }}/${{ github.repository }}/ld-vendors:main \
          registry.company.com/ld-apps/wins=${{ env.REGISTRY }}/${{ github.repository }}/ld-wins:main
        kubectl apply -k .
    
    - name: Wait for deployment
      run: |
        kubectl rollout status deployment/prod-backend -n ld-apps-prod --timeout=600s
        kubectl rollout status deployment/prod-training-frontend -n ld-apps-prod --timeout=600s
        kubectl rollout status deployment/prod-vendors-frontend -n ld-apps-prod --timeout=600s
        kubectl rollout status deployment/prod-wins-frontend -n ld-apps-prod --timeout=600s
    
    - name: Run smoke tests
      run: |
        kubectl run smoke-test --image=curlimages/curl:latest --rm -it --restart=Never -- \
          curl -f http://prod-backend-service.ld-apps-prod.svc.cluster.local:3000/health
    
    - name: Run load tests
      run: |
        kubectl run load-test --image=grafana/k6:latest --rm -it --restart=Never -- \
          k6 run --vus 10 --duration 60s - <<EOF
        import http from 'k6/http';
        import { check } from 'k6';
        
        export default function() {
          let response = http.get('http://prod-backend-service.ld-apps-prod.svc.cluster.local:3000/health');
          check(response, {
            'status is 200': (r) => r.status === 200,
            'response time < 500ms': (r) => r.timings.duration < 500,
          });
        }
        EOF
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          Automated release from commit ${{ github.sha }}
          
          ## Changes
          ${{ github.event.head_commit.message }}
          
          ## Docker Images
          - Backend: ${{ env.REGISTRY }}/${{ github.repository }}/ld-backend:main
          - Training: ${{ env.REGISTRY }}/${{ github.repository }}/ld-training:main
          - Vendors: ${{ env.REGISTRY }}/${{ github.repository }}/ld-vendors:main
          - Wins: ${{ env.REGISTRY }}/${{ github.repository }}/ld-wins:main
        draft: false
        prerelease: false
  
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Delete old images
      uses: actions/delete-package-versions@v4
      with:
        package-name: ld-backend
        package-type: container
        min-versions-to-keep: 5
        delete-only-untagged-versions: true