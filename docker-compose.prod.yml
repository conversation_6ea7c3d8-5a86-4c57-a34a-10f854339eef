version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ld-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-command_center}
      POSTGRES_USER: ${DATABASE_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-postgres}
      PGDATA: /var/lib/postgresql/data
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./Command-Center/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-postgres} -d ${DATABASE_NAME:-command_center}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ld-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ld-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis-password} --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ld-network

  # Backend API (Command Center)
  backend:
    build:
      context: ./Command-Center
      dockerfile: Dockerfile
    container_name: ld-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: ${DATABASE_USERNAME:-postgres}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-postgres}
      DATABASE_NAME: ${DATABASE_NAME:-command_center}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis-password}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-15m}
      JWT_REFRESH_EXPIRES_IN: ${JWT_REFRESH_EXPIRES_IN:-30d}
      SESSION_SECRET: ${SESSION_SECRET}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3001,http://localhost:3002,http://localhost:3003}
    volumes:
      - ./Command-Center/uploads:/app/uploads
      - ./Command-Center/logs:/app/logs
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ld-network

  # Training Frontend
  training-frontend:
    build:
      context: ./Training-need-analysis
      dockerfile: Dockerfile
    container_name: ld-training-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      NODE_ENV: production
      VITE_API_URL: ${VITE_API_URL:-http://backend:3000}
      VITE_APP_NAME: "Training Need Analysis"
    ports:
      - "3001:3001"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-network

  # Vendors Frontend
  vendors-frontend:
    build:
      context: ./Vendors
      dockerfile: Dockerfile
    container_name: ld-vendors-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      NODE_ENV: production
      VITE_API_URL: ${VITE_API_URL:-http://backend:3000}
      VITE_APP_NAME: "Vendor Management"
    ports:
      - "3002:3002"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-network

  # Wins Frontend
  wins-frontend:
    build:
      context: ./Wins-of-Week
      dockerfile: Dockerfile
    container_name: ld-wins-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      NODE_ENV: production
      VITE_API_URL: ${VITE_API_URL:-http://backend:3000}
      VITE_APP_NAME: "Wins of the Week"
    ports:
      - "3003:3003"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ld-nginx
    restart: unless-stopped
    depends_on:
      - backend
      - training-frontend
      - vendors-frontend
      - wins-frontend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    ports:
      - "80:80"
      - "443:443"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-network

volumes:
  postgres_data:
  redis_data:
  nginx_cache:

networks:
  ld-network:
    driver: bridge