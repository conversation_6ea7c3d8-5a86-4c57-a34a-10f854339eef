#!/bin/bash

# Generate self-signed SSL certificate for development

# Create certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout key.pem \
    -out cert.pem \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Set permissions
chmod 600 key.pem
chmod 644 cert.pem

echo "Self-signed certificate generated successfully!"
echo "Files created:"
echo "  - cert.pem (certificate)"
echo "  - key.pem (private key)"
echo ""
echo "Note: This is a self-signed certificate for development only."
echo "For production, use certificates from a trusted CA (e.g., Let's Encrypt)."