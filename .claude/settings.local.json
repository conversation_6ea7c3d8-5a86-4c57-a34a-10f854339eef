{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:mcp.sentry.dev)", "WebFetch(domain:github.com)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npx @magicuidesign/cli@latest install:*)", "mcp__serena__initial_instructions", "Bash(find:*)", "Bash(ls:*)", "mcp__filesystem__read_file", "Bash(claude code config --help)", "<PERSON><PERSON>(mkdir:*)", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "Bash(npm install:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__list_memories", "mcp__filesystem__read_multiple_files", "mcp__serena__write_memory", "mcp__filesystem__directory_tree", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__find_file", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "Bash(npm audit:*)", "mcp__serena__think_about_collected_information", "mcp__serena__read_memory", "Bash(npm run test:cov:*)"], "deny": []}}