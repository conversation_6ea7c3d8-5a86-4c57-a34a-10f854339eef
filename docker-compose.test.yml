version: '3.8'

services:
  postgres-test:
    image: postgres:15-alpine
    container_name: ld-postgres-test
    environment:
      POSTGRES_DB: ld_apps_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d ld_apps_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ld-test-network

  redis-test:
    image: redis:7-alpine
    container_name: ld-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ld-test-network

  backend-test:
    build:
      context: ./Command-Center
      dockerfile: Dockerfile
      target: development
    container_name: ld-backend-test
    environment:
      NODE_ENV: test
      DATABASE_URL: *******************************************************/ld_apps_test
      REDIS_URL: redis://redis-test:6379
      JWT_SECRET: test-jwt-secret-key-for-testing-only
      JWT_REFRESH_SECRET: test-jwt-refresh-secret-key-for-testing-only
      CORS_ORIGIN: http://localhost:3001,http://localhost:3002,http://localhost:3003
      PORT: 3000
    ports:
      - "3000:3000"
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    volumes:
      - ./Command-Center:/app
      - /app/node_modules
      - ./Command-Center/uploads:/app/uploads
    command: npm run start:dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-test-network

  training-frontend-test:
    build:
      context: ./training-need-analysis
      dockerfile: Dockerfile
      target: development
    container_name: ld-training-test
    environment:
      NODE_ENV: test
      VITE_API_URL: http://backend-test:3000
      VITE_APP_NAME: "Training Need Analysis (Test)"
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      backend-test:
        condition: service_healthy
    volumes:
      - ./training-need-analysis:/app
      - /app/node_modules
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-test-network

  vendors-frontend-test:
    build:
      context: ./vendors
      dockerfile: Dockerfile
      target: development
    container_name: ld-vendors-test
    environment:
      NODE_ENV: test
      VITE_API_URL: http://backend-test:3000
      VITE_APP_NAME: "Vendor Management (Test)"
      PORT: 3002
    ports:
      - "3002:3002"
    depends_on:
      backend-test:
        condition: service_healthy
    volumes:
      - ./vendors:/app
      - /app/node_modules
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-test-network

  wins-frontend-test:
    build:
      context: ./wins-of-week
      dockerfile: Dockerfile
      target: development
    container_name: ld-wins-test
    environment:
      NODE_ENV: test
      VITE_API_URL: http://backend-test:3000
      VITE_APP_NAME: "Wins of the Week (Test)"
      PORT: 3003
    ports:
      - "3003:3003"
    depends_on:
      backend-test:
        condition: service_healthy
    volumes:
      - ./wins-of-week:/app
      - /app/node_modules
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ld-test-network

volumes:
  postgres_test_data:
  redis_test_data:

networks:
  ld-test-network:
    driver: bridge