import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { of, throwError } from 'rxjs';
import { LearningPlatformsService } from '../Command-Center/src/common/external-apis/learning-platforms.service';
import { VendorIntelligenceService } from '../Command-Center/src/common/external-apis/vendor-intelligence.service';
import { CommunicationPlatformsService } from '../Command-Center/src/common/external-apis/communication-platforms.service';
import { DataSynchronizationService } from '../Command-Center/src/common/external-apis/data-synchronization.service';
import { ErrorHandlingService } from '../Command-Center/src/common/external-apis/error-handling.service';
import { RedisService } from '../Command-Center/src/common/cache/redis.service';
import { LoggerService } from '../Command-Center/src/common/logger/logger.service';
import { PrismaService } from '../Command-Center/src/common/database/prisma.service';

describe('External APIs Integration', () => {
  let learningPlatformsService: LearningPlatformsService;
  let vendorIntelligenceService: VendorIntelligenceService;
  let communicationPlatformsService: CommunicationPlatformsService;
  let dataSynchronizationService: DataSynchronizationService;
  let errorHandlingService: ErrorHandlingService;
  let httpService: jest.Mocked<HttpService>;
  let redisService: jest.Mocked<RedisService>;
  let prismaService: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LearningPlatformsService,
        VendorIntelligenceService,
        CommunicationPlatformsService,
        DataSynchronizationService,
        ErrorHandlingService,
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
            post: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                COURSERA_API_URL: 'https://api.coursera.org/api/rest/v1',
                COURSERA_API_KEY: 'test-coursera-key',
                UDEMY_API_URL: 'https://www.udemy.com/api-2.0',
                UDEMY_API_KEY: 'test-udemy-key',
                SENDGRID_API_KEY: 'test-sendgrid-key',
                SLACK_BOT_TOKEN: 'test-slack-token',
                DUN_BRADSTREET_API_URL: 'https://api.dnb.com/v1',
                DUN_BRADSTREET_API_KEY: 'test-dnb-key',
                FROM_EMAIL: '<EMAIL>',
                EXTERNAL_API_TOKEN: 'test-external-token'
              };
              return config[key];
            }),
          },
        },
        {
          provide: RedisService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            keys: jest.fn(),
            incr: jest.fn(),
            expire: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
        {
          provide: PrismaService,
          useValue: {
            course: {
              upsert: jest.fn(),
              findMany: jest.fn(),
            },
            enrollment: {
              upsert: jest.fn(),
            },
            vendor: {
              findMany: jest.fn(),
              update: jest.fn(),
            },
            winsSubmission: {
              findMany: jest.fn(),
              update: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    learningPlatformsService = module.get<LearningPlatformsService>(LearningPlatformsService);
    vendorIntelligenceService = module.get<VendorIntelligenceService>(VendorIntelligenceService);
    communicationPlatformsService = module.get<CommunicationPlatformsService>(CommunicationPlatformsService);
    dataSynchronizationService = module.get<DataSynchronizationService>(DataSynchronizationService);
    errorHandlingService = module.get<ErrorHandlingService>(ErrorHandlingService);
    httpService = module.get(HttpService);
    redisService = module.get(RedisService);
    prismaService = module.get(PrismaService);
  });

  describe('Learning Platforms Service', () => {
    it('should search courses across multiple platforms', async () => {
      const mockCourseraResponse = {
        data: {
          elements: [
            {
              id: '123',
              name: 'Introduction to Machine Learning',
              description: 'Learn the basics of ML',
              domainTypes: ['technology'],
              difficultyLevel: 'beginner',
              workload: 40,
              averageRating: 4.5,
              enrollmentCount: 10000,
              skills: ['Python', 'Data Science'],
              prerequisites: [],
              primaryLanguages: ['en']
            }
          ]
        }
      };

      const mockUdemyResponse = {
        data: {
          results: [
            {
              id: '456',
              title: 'Complete Python Bootcamp',
              headline: 'From Zero to Hero in Python',
              primary_category: { title: 'Development' },
              instructional_level: 'beginner level',
              content_info: { length_video_hours: 22 },
              price_detail: { amount: 199.99, currency: 'USD' },
              rating: 4.6,
              num_subscribers: 25000,
              objective_items: ['Python Programming', 'Web Development'],
              prerequisites: [],
              locale: { title: 'English' },
              has_certificate: true,
              last_update_date: '2023-12-01'
            }
          ]
        }
      };

      httpService.get
        .mockReturnValueOnce(of({ data: mockCourseraResponse.data } as any))
        .mockReturnValueOnce(of({ data: mockUdemyResponse.data } as any));

      redisService.get.mockResolvedValue(null);
      redisService.set.mockResolvedValue(undefined);

      const result = await learningPlatformsService.searchCourses('machine learning', {
        category: 'technology',
        level: 'beginner'
      });

      expect(result).toHaveLength(2);
      expect(result[0].provider).toBe('Coursera');
      expect(result[1].provider).toBe('Udemy');
      expect(httpService.get).toHaveBeenCalledTimes(2);
      expect(redisService.set).toHaveBeenCalledWith(
        expect.stringContaining('courses:search:'),
        expect.any(Array),
        { ttl: 3600 }
      );
    });

    it('should handle course search errors gracefully', async () => {
      const error = new Error('API timeout');
      httpService.get.mockReturnValue(throwError(() => error));
      redisService.get.mockResolvedValue(null);

      const result = await learningPlatformsService.searchCourses('invalid query');

      expect(result).toEqual([]);
    });

    it('should enroll user in external course', async () => {
      const enrollmentRequest = {
        userId: 'user-123',
        courseId: 'coursera-456',
        startDate: '2024-01-01'
      };

      redisService.set.mockResolvedValue(undefined);

      const result = await learningPlatformsService.enrollInCourse(enrollmentRequest);

      expect(result.status).toBe('enrolled');
      expect(result.accessUrl).toContain('coursera.com');
      expect(redisService.set).toHaveBeenCalledWith(
        `enrollment:${enrollmentRequest.userId}:${enrollmentRequest.courseId}`,
        expect.any(Object),
        { ttl: 86400 }
      );
    });
  });

  describe('Vendor Intelligence Service', () => {
    it('should get vendor risk assessment', async () => {
      redisService.get.mockResolvedValue(null);
      redisService.set.mockResolvedValue(undefined);

      const result = await vendorIntelligenceService.getVendorRiskAssessment(
        'vendor-123',
        '12-3456789',
        'Test Vendor Corp'
      );

      expect(result.vendorId).toBe('vendor-123');
      expect(result.riskScore).toBeGreaterThanOrEqual(0);
      expect(result.riskLevel).toMatch(/^(low|medium|high|critical)$/);
      expect(result.riskFactors).toHaveProperty('financial');
      expect(result.riskFactors).toHaveProperty('operational');
      expect(result.riskFactors).toHaveProperty('compliance');
      expect(result.riskFactors).toHaveProperty('security');
      expect(result.riskFactors).toHaveProperty('reputation');
    });

    it('should cache vendor risk data', async () => {
      const cachedRiskData = {
        vendorId: 'vendor-123',
        riskScore: 45,
        riskLevel: 'medium' as const,
        riskFactors: {
          financial: 30,
          operational: 20,
          compliance: 10,
          security: 15,
          reputation: 25
        },
        lastUpdated: new Date().toISOString(),
        alerts: []
      };

      redisService.get.mockResolvedValue(cachedRiskData);

      const result = await vendorIntelligenceService.getVendorRiskAssessment('vendor-123');

      expect(result).toEqual(cachedRiskData);
      expect(redisService.set).not.toHaveBeenCalled();
    });

    it('should get vendor financial data', async () => {
      redisService.get.mockResolvedValue(null);
      redisService.set.mockResolvedValue(undefined);

      const result = await vendorIntelligenceService.getVendorFinancialData('vendor-123');

      expect(result.vendorId).toBe('vendor-123');
      expect(result.creditScore).toBeGreaterThanOrEqual(700);
      expect(result.creditRating).toBe('A');
      expect(result.revenue).toBeGreaterThan(0);
      expect(result.netIncome).toBeGreaterThan(0);
    });
  });

  describe('Communication Platforms Service', () => {
    it('should send email notification', async () => {
      const notificationRequest = {
        recipients: ['<EMAIL>'],
        channel: 'email' as const,
        subject: 'Test Notification',
        message: 'This is a test message',
        priority: 'medium' as const
      };

      httpService.post.mockReturnValue(of({ data: { message_id: 'msg-123' } } as any));
      redisService.set.mockResolvedValue(undefined);

      const result = await communicationPlatformsService.sendNotification(notificationRequest);

      expect(result.status).toBe('sent');
      expect(result.deliveredTo).toEqual(['<EMAIL>']);
      expect(result.failedTo).toEqual([]);
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('sendgrid'),
        expect.objectContaining({
          personalizations: expect.any(Array),
          from: expect.any(Object),
          content: expect.any(Array)
        }),
        expect.any(Object)
      );
    });

    it('should send Slack message', async () => {
      const notificationRequest = {
        recipients: ['#general'],
        channel: 'slack' as const,
        message: 'Test slack message',
        priority: 'low' as const
      };

      httpService.post.mockReturnValue(of({ 
        data: { ok: true, ts: '1234567890.123456' } 
      } as any));
      redisService.set.mockResolvedValue(undefined);

      const result = await communicationPlatformsService.sendNotification(notificationRequest);

      expect(result.status).toBe('sent');
      expect(result.id).toBe('1234567890.123456');
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('slack'),
        expect.objectContaining({
          channel: '#general',
          text: 'Test slack message'
        }),
        expect.any(Object)
      );
    });

    it('should announce wins', async () => {
      const announcement = {
        title: 'New Sales Record',
        description: 'Our team achieved a new sales record this quarter',
        author: 'John Doe',
        category: 'Sales',
        achievements: ['20% increase in revenue', '50 new clients acquired']
      };

      httpService.post.mockReturnValue(of({ 
        data: { ok: true, ts: '1234567890.123456' } 
      } as any));
      redisService.set.mockResolvedValue(undefined);

      const result = await communicationPlatformsService.announceWin(announcement, ['slack']);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('sent');
    });
  });

  describe('Data Synchronization Service', () => {
    it('should run course sync job', async () => {
      const mockCourses = [
        {
          id: 'ext-123',
          title: 'Test Course',
          description: 'A test course',
          category: 'Technology',
          level: 'intermediate',
          duration: 20,
          updatedAt: new Date().toISOString()
        }
      ];

      httpService.get.mockReturnValue(of({ 
        data: { courses: mockCourses } 
      } as any));

      prismaService.course.upsert.mockResolvedValue({
        id: 'course-123',
        externalId: 'ext-123',
        title: 'Test Course',
        description: 'A test course',
        category: 'Technology',
        level: 'intermediate',
        duration: 20,
        createdAt: new Date(),
        updatedAt: new Date()
      } as any);

      redisService.set.mockResolvedValue(undefined);

      const result = await dataSynchronizationService.runSyncJob('training-courses-sync');

      expect(result.status).toBe('success');
      expect(result.recordsProcessed).toBe(1);
      expect(result.recordsSuccessful).toBe(1);
      expect(result.recordsFailed).toBe(0);
      expect(prismaService.course.upsert).toHaveBeenCalledWith({
        where: { externalId: 'ext-123' },
        create: expect.any(Object),
        update: expect.any(Object)
      });
    });

    it('should handle sync job errors', async () => {
      const error = new Error('Database connection failed');
      httpService.get.mockReturnValue(throwError(() => error));

      const result = await dataSynchronizationService.runSyncJob('training-courses-sync');

      expect(result.status).toBe('failed');
      expect(result.errors).toContain('Database connection failed');
    });

    it('should get sync job status', async () => {
      const syncJobs = await dataSynchronizationService.getSyncJobs();

      expect(syncJobs).toHaveLength(5);
      expect(syncJobs[0].id).toBe('training-courses-sync');
      expect(syncJobs[0].status).toMatch(/^(active|paused|error|completed)$/);
    });
  });

  describe('Error Handling Service', () => {
    it('should handle API errors', async () => {
      const error = new Error('API rate limit exceeded');
      error['response'] = { status: 429 };

      redisService.set.mockResolvedValue(undefined);
      redisService.incr.mockResolvedValue(1);
      redisService.expire.mockResolvedValue(true);

      const apiError = await errorHandlingService.handleApiError(
        'learning-platforms',
        '/api/courses',
        'GET',
        error
      );

      expect(apiError.service).toBe('learning-platforms');
      expect(apiError.endpoint).toBe('/api/courses');
      expect(apiError.statusCode).toBe(429);
      expect(apiError.error).toBe('API rate limit exceeded');
      expect(apiError.impact).toMatch(/^(low|medium|high|critical)$/);
    });

    it('should retry operations with backoff', async () => {
      let attemptCount = 0;
      const operation = jest.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Temporary failure');
        }
        return Promise.resolve('success');
      });

      const result = await errorHandlingService.retryWithBackoff(
        operation,
        'test-service',
        '/test-endpoint',
        { maxRetries: 3, initialDelay: 100, maxDelay: 1000, exponentialBackoff: true }
      );

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it('should get service health status', async () => {
      redisService.get.mockResolvedValue(5);
      redisService.keys.mockResolvedValue(['api-error:test-service-123']);
      redisService.get
        .mockResolvedValueOnce(5)
        .mockResolvedValueOnce({
          id: 'error-123',
          service: 'test-service',
          error: 'Test error',
          timestamp: new Date().toISOString()
        });

      const health = await errorHandlingService.getServiceHealth('test-service');

      expect(health.status).toMatch(/^(healthy|degraded|unhealthy)$/);
      expect(health.errorCount).toBe(5);
      expect(health.circuitBreakerOpen).toBe(false);
    });

    it('should resolve errors', async () => {
      const errorData = {
        id: 'error-123',
        service: 'test-service',
        error: 'Test error',
        resolved: false,
        timestamp: new Date().toISOString()
      };

      redisService.get.mockResolvedValue(errorData);
      redisService.set.mockResolvedValue(undefined);

      await errorHandlingService.resolveError('error-123');

      expect(redisService.set).toHaveBeenCalledWith(
        'api-error:error-123',
        expect.objectContaining({ resolved: true })
      );
    });
  });

  describe('Circuit Breaker', () => {
    it('should open circuit breaker after failures', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Service unavailable'));

      // Trigger multiple failures to open circuit breaker
      for (let i = 0; i < 6; i++) {
        try {
          await errorHandlingService.retryWithBackoff(
            operation,
            'unreliable-service',
            '/test',
            { maxRetries: 0, initialDelay: 10, maxDelay: 10, exponentialBackoff: false }
          );
        } catch (error) {
          // Expected to fail
        }
      }

      // Next call should fail immediately due to open circuit breaker
      await expect(
        errorHandlingService.retryWithBackoff(
          operation,
          'unreliable-service',
          '/test',
          { maxRetries: 1, initialDelay: 10, maxDelay: 10, exponentialBackoff: false }
        )
      ).rejects.toThrow('Circuit breaker open');
    });
  });
});

describe('External APIs Controller Integration', () => {
  let app: any;
  let httpService: jest.Mocked<HttpService>;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Import modules needed for controller testing
      ],
      providers: [
        // Mock providers
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    httpService = moduleFixture.get(HttpService);
  });

  afterEach(async () => {
    await app.close();
  });

  it('should handle concurrent API requests', async () => {
    // Test concurrent requests to external APIs
    const promises = Array.from({ length: 10 }, (_, i) => 
      learningPlatformsService.searchCourses(`query-${i}`)
    );

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    
    expect(successful).toBeGreaterThan(0);
  });

  it('should handle rate limiting', async () => {
    // Simulate rate limiting
    httpService.get.mockReturnValue(throwError(() => ({
      response: { status: 429 },
      message: 'Rate limit exceeded'
    })));

    await expect(
      learningPlatformsService.searchCourses('test query')
    ).rejects.toThrow();
  });
});