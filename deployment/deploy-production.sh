#!/bin/bash

# Production Deployment Script for L&D Apps
# This script automates the deployment of all L&D applications to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="ld-apps-prod"
MONITORING_NAMESPACE="monitoring"
BACKUP_NAMESPACE="backup"
DOMAIN="ld-apps.company.com"
API_DOMAIN="api.ld-apps.company.com"
REGISTRY="ghcr.io/company/ld-apps"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        error "helm is not installed"
    fi
    
    # Check if kustomize is installed
    if ! command -v kustomize &> /dev/null; then
        error "kustomize is not installed"
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    log "Prerequisites check passed"
}

# Create namespaces
create_namespaces() {
    log "Creating namespaces..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $MONITORING_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $BACKUP_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespaces for security
    kubectl label namespace $NAMESPACE \
        pod-security.kubernetes.io/enforce=restricted \
        pod-security.kubernetes.io/audit=restricted \
        pod-security.kubernetes.io/warn=restricted \
        --overwrite
    
    log "Namespaces created successfully"
}

# Deploy secrets
deploy_secrets() {
    log "Deploying secrets..."
    
    # Check if secrets exist
    if kubectl get secret ld-secrets -n $NAMESPACE &> /dev/null; then
        warn "Secrets already exist, skipping creation"
        return
    fi
    
    # Create secrets from environment variables or prompt
    if [ -z "$DATABASE_PASSWORD" ]; then
        read -s -p "Enter database password: " DATABASE_PASSWORD
        echo
    fi
    
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(openssl rand -base64 32)
        info "Generated JWT secret"
    fi
    
    if [ -z "$JWT_REFRESH_SECRET" ]; then
        JWT_REFRESH_SECRET=$(openssl rand -base64 32)
        info "Generated JWT refresh secret"
    fi
    
    if [ -z "$REDIS_PASSWORD" ]; then
        REDIS_PASSWORD=$(openssl rand -base64 16)
        info "Generated Redis password"
    fi
    
    if [ -z "$ENCRYPTION_KEY" ]; then
        ENCRYPTION_KEY=$(openssl rand -base64 32)
        info "Generated encryption key"
    fi
    
    # Create secret
    kubectl create secret generic ld-secrets \
        --from-literal=DATABASE_USER=ld_admin \
        --from-literal=DATABASE_PASSWORD="$DATABASE_PASSWORD" \
        --from-literal=JWT_SECRET="$JWT_SECRET" \
        --from-literal=JWT_REFRESH_SECRET="$JWT_REFRESH_SECRET" \
        --from-literal=REDIS_PASSWORD="$REDIS_PASSWORD" \
        --from-literal=ENCRYPTION_KEY="$ENCRYPTION_KEY" \
        --namespace=$NAMESPACE
    
    log "Secrets deployed successfully"
}

# Deploy database
deploy_database() {
    log "Deploying database..."
    
    # Deploy PostgreSQL
    kubectl apply -f k8s/base/postgres.yaml
    
    # Wait for PostgreSQL to be ready
    log "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    # Deploy Redis
    kubectl apply -f k8s/base/redis.yaml
    
    # Wait for Redis to be ready
    log "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    log "Database deployment completed"
}

# Deploy applications
deploy_applications() {
    log "Deploying applications..."
    
    # Deploy using kustomize
    cd k8s/overlays/production
    kustomize build . | kubectl apply -f -
    cd ../../..
    
    # Wait for backend to be ready
    log "Waiting for backend to be ready..."
    kubectl wait --for=condition=ready pod -l app=backend -n $NAMESPACE --timeout=600s
    
    # Wait for frontends to be ready
    log "Waiting for frontend applications to be ready..."
    kubectl wait --for=condition=ready pod -l app=training-frontend -n $NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app=vendors-frontend -n $NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app=wins-frontend -n $NAMESPACE --timeout=300s
    
    log "Applications deployed successfully"
}

# Deploy monitoring
deploy_monitoring() {
    log "Deploying monitoring stack..."
    
    # Check if monitoring is already deployed
    if kubectl get deployment prometheus-kube-prometheus-prometheus -n $MONITORING_NAMESPACE &> /dev/null; then
        warn "Monitoring stack already deployed, skipping"
        return
    fi
    
    # Install monitoring stack
    cd k8s/monitoring
    if [ -f "install-monitoring.sh" ]; then
        chmod +x install-monitoring.sh
        ./install-monitoring.sh
    else
        # Manual installation
        helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
        helm repo update
        
        helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
            --namespace $MONITORING_NAMESPACE \
            --values prometheus-values.yaml \
            --wait
        
        # Deploy ELK stack
        kubectl apply -f elk-stack.yaml
        
        # Deploy Filebeat
        kubectl apply -f filebeat-daemonset.yaml
    fi
    cd ../..
    
    log "Monitoring stack deployed successfully"
}

# Deploy ingress
deploy_ingress() {
    log "Deploying ingress..."
    
    # Install cert-manager if not present
    if ! kubectl get deployment cert-manager -n cert-manager &> /dev/null; then
        log "Installing cert-manager..."
        kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.13.0/cert-manager.yaml
        
        # Wait for cert-manager to be ready
        kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
        
        # Create ClusterIssuer
        kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
    fi
    
    # Deploy ingress
    kubectl apply -f k8s/base/ingress.yaml
    
    log "Ingress deployed successfully"
}

# Deploy backup system
deploy_backup_system() {
    log "Deploying backup system..."
    
    # Deploy database backup CronJob
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: $NAMESPACE
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/sh
            - -c
            - |
              BACKUP_FILE="/backup/ld_apps_\$(date +%Y%m%d_%H%M%S).sql"
              pg_dump -h postgres-service -U ld_admin ld_apps > \$BACKUP_FILE
              echo "Backup created: \$BACKUP_FILE"
              # Add cloud storage upload here if needed
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: ld-secrets
                  key: DATABASE_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            emptyDir: {}
          restartPolicy: OnFailure
EOF
    
    log "Backup system deployed successfully"
}

# Run health checks
run_health_checks() {
    log "Running health checks..."
    
    # Check pod status
    kubectl get pods -n $NAMESPACE
    
    # Test API endpoints
    local backend_service=$(kubectl get svc -n $NAMESPACE -o jsonpath='{.items[?(@.metadata.name=="backend-service")].spec.clusterIP}')
    
    if [ -n "$backend_service" ]; then
        # Test from within cluster
        kubectl run health-check --image=curlimages/curl:latest --rm -i --restart=Never -- \
            curl -f http://$backend_service:3000/health || warn "Backend health check failed"
    else
        warn "Backend service not found"
    fi
    
    # Check ingress
    kubectl get ingress -n $NAMESPACE
    
    log "Health checks completed"
}

# Configure monitoring alerts
configure_alerts() {
    log "Configuring monitoring alerts..."
    
    # Apply ServiceMonitors
    kubectl apply -f k8s/base/service-monitors.yaml
    
    # Reload Prometheus configuration
    kubectl exec -n $MONITORING_NAMESPACE deployment/prometheus-kube-prometheus-prometheus -- \
        curl -X POST http://localhost:9090/-/reload || warn "Failed to reload Prometheus"
    
    log "Monitoring alerts configured"
}

# Main deployment function
main() {
    log "Starting L&D Apps production deployment..."
    
    # Pre-deployment checks
    check_prerequisites
    
    # Deployment steps
    create_namespaces
    deploy_secrets
    deploy_database
    deploy_applications
    deploy_monitoring
    deploy_ingress
    deploy_backup_system
    
    # Post-deployment
    run_health_checks
    configure_alerts
    
    log "Deployment completed successfully!"
    
    # Display access information
    info "Access Information:"
    info "Main Application: https://$DOMAIN"
    info "API: https://$API_DOMAIN"
    info "Monitoring: Check your monitoring ingress configuration"
    info ""
    info "To check deployment status:"
    info "kubectl get pods -n $NAMESPACE"
    info "kubectl get svc -n $NAMESPACE"
    info "kubectl get ingress -n $NAMESPACE"
    info ""
    info "To view logs:"
    info "kubectl logs -f deployment/prod-backend -n $NAMESPACE"
    info ""
    info "To access monitoring:"
    info "kubectl port-forward -n $MONITORING_NAMESPACE svc/prometheus-kube-prometheus-prometheus 9090:9090"
    info "kubectl port-forward -n $MONITORING_NAMESPACE svc/prometheus-grafana 3000:80"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "health-check")
        run_health_checks
        ;;
    "rollback")
        log "Rolling back deployment..."
        kubectl rollout undo deployment/prod-backend -n $NAMESPACE
        kubectl rollout undo deployment/prod-training-frontend -n $NAMESPACE
        kubectl rollout undo deployment/prod-vendors-frontend -n $NAMESPACE
        kubectl rollout undo deployment/prod-wins-frontend -n $NAMESPACE
        log "Rollback completed"
        ;;
    "clean")
        warn "This will delete all resources in namespace $NAMESPACE"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kubectl delete namespace $NAMESPACE
            log "Cleanup completed"
        else
            info "Cleanup cancelled"
        fi
        ;;
    *)
        echo "Usage: $0 {deploy|health-check|rollback|clean}"
        echo "  deploy      - Deploy applications to production"
        echo "  health-check - Run health checks"
        echo "  rollback    - Rollback to previous version"
        echo "  clean       - Clean up all resources"
        exit 1
        ;;
esac