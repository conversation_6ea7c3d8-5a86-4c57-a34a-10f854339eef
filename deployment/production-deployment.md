# L&D Apps Production Deployment Guide

## Overview
This document provides a comprehensive guide for deploying the L&D Applications (Training-need-analysis, Vendors, and Wins-of-Week) to production environment with monitoring, logging, and backup systems.

## Pre-deployment Checklist

### Infrastructure Requirements
- [x] Kubernetes cluster (1.24+)
- [x] PostgreSQL database (15+)
- [x] Redis cache (7+)
- [x] Container registry (Docker Hub/ECR/GCR)
- [x] SSL certificates
- [x] Domain name and DNS configuration
- [x] Monitoring tools (Prometheus, Grafana, ELK)
- [x] Backup storage (S3/GCS/Azure Blob)

### Security Requirements
- [x] Secrets management (Kubernetes Secrets/Vault)
- [x] Network policies configured
- [x] Pod security standards enabled
- [x] Image vulnerability scanning
- [x] SSL/TLS encryption
- [x] API security (rate limiting, authentication)

### Application Requirements
- [x] All tests passing (unit, integration, E2E)
- [x] Performance benchmarks met
- [x] Security audit completed
- [x] Docker images built and pushed
- [x] Database migrations ready
- [x] Configuration files prepared

## Deployment Steps

### 1. Environment Setup

#### Create Namespaces
```bash
kubectl create namespace ld-apps-prod
kubectl create namespace monitoring
kubectl create namespace backup
```

#### Configure Secrets
```bash
# Database secrets
kubectl create secret generic ld-secrets \
  --from-literal=DATABASE_USER=ld_admin \
  --from-literal=DATABASE_PASSWORD=<secure-password> \
  --from-literal=JWT_SECRET=<jwt-secret> \
  --from-literal=JWT_REFRESH_SECRET=<jwt-refresh-secret> \
  --from-literal=REDIS_PASSWORD=<redis-password> \
  --from-literal=ENCRYPTION_KEY=<encryption-key> \
  --namespace=ld-apps-prod

# TLS certificates
kubectl create secret tls ld-apps-prod-tls \
  --cert=path/to/cert.crt \
  --key=path/to/cert.key \
  --namespace=ld-apps-prod
```

### 2. Database Deployment

#### Deploy PostgreSQL
```bash
# Apply PostgreSQL configuration
kubectl apply -f k8s/base/postgres.yaml

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n ld-apps-prod --timeout=300s

# Run database migrations
kubectl exec -it deployment/postgres -n ld-apps-prod -- \
  psql -U ld_admin -d ld_apps -c "SELECT version();"
```

#### Deploy Redis
```bash
# Apply Redis configuration
kubectl apply -f k8s/base/redis.yaml

# Wait for Redis to be ready
kubectl wait --for=condition=ready pod -l app=redis -n ld-apps-prod --timeout=300s
```

### 3. Application Deployment

#### Deploy Backend Services
```bash
# Apply backend configuration
kubectl apply -f k8s/overlays/production/

# Wait for backend to be ready
kubectl wait --for=condition=ready pod -l app=backend -n ld-apps-prod --timeout=600s

# Check backend health
kubectl exec -it deployment/prod-backend -n ld-apps-prod -- \
  curl -f http://localhost:3000/health
```

#### Deploy Frontend Applications
```bash
# Deploy all frontend applications
kubectl apply -f k8s/base/training-frontend.yaml
kubectl apply -f k8s/base/vendors-frontend.yaml
kubectl apply -f k8s/base/wins-frontend.yaml

# Wait for all frontends to be ready
kubectl wait --for=condition=ready pod -l app=training-frontend -n ld-apps-prod --timeout=300s
kubectl wait --for=condition=ready pod -l app=vendors-frontend -n ld-apps-prod --timeout=300s
kubectl wait --for=condition=ready pod -l app=wins-frontend -n ld-apps-prod --timeout=300s
```

### 4. Ingress and Load Balancing

#### Apply Ingress Configuration
```bash
# Deploy ingress controller (if not already installed)
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/main/deploy/static/provider/cloud/deploy.yaml

# Apply L&D Apps ingress
kubectl apply -f k8s/base/ingress.yaml

# Verify ingress
kubectl get ingress -n ld-apps-prod
```

### 5. Monitoring Stack Deployment

#### Deploy Prometheus and Grafana
```bash
# Install monitoring stack
cd k8s/monitoring
./install-monitoring.sh

# Verify monitoring services
kubectl get pods -n monitoring
kubectl get svc -n monitoring
```

#### Deploy ELK Stack
```bash
# Apply ELK configuration
kubectl apply -f k8s/monitoring/elk-stack.yaml

# Wait for Elasticsearch to be ready
kubectl wait --for=condition=ready pod -l app=elasticsearch -n monitoring --timeout=600s

# Deploy Filebeat for log collection
kubectl apply -f k8s/monitoring/filebeat-daemonset.yaml
```

### 6. Backup System Setup

#### Configure Database Backups
```bash
# Create backup CronJob
kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: ld-apps-prod
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/sh
            - -c
            - |
              pg_dump -h postgres-service -U ld_admin ld_apps > /backup/ld_apps_\$(date +%Y%m%d_%H%M%S).sql
              # Upload to S3 or other backup storage
              aws s3 cp /backup/ld_apps_\$(date +%Y%m%d_%H%M%S).sql s3://ld-apps-backups/database/
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: ld-secrets
                  key: DATABASE_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            emptyDir: {}
          restartPolicy: OnFailure
EOF
```

#### Configure Application Data Backups
```bash
# Create application backup job
kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: app-data-backup
  namespace: ld-apps-prod
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: app-backup
            image: alpine:latest
            command:
            - /bin/sh
            - -c
            - |
              # Backup uploaded files and logs
              tar -czf /backup/app_data_\$(date +%Y%m%d_%H%M%S).tar.gz /app/uploads /app/logs
              # Upload to backup storage
              aws s3 cp /backup/app_data_\$(date +%Y%m%d_%H%M%S).tar.gz s3://ld-apps-backups/app-data/
            volumeMounts:
            - name: app-uploads
              mountPath: /app/uploads
            - name: app-logs
              mountPath: /app/logs
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: app-uploads
            persistentVolumeClaim:
              claimName: app-uploads-pvc
          - name: app-logs
            persistentVolumeClaim:
              claimName: app-logs-pvc
          - name: backup-storage
            emptyDir: {}
          restartPolicy: OnFailure
EOF
```

### 7. SSL/TLS Configuration

#### Configure Let's Encrypt
```bash
# Install cert-manager
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer for Let's Encrypt
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

### 8. Health Checks and Monitoring

#### Verify All Services
```bash
# Check all pods are running
kubectl get pods -n ld-apps-prod

# Check services
kubectl get svc -n ld-apps-prod

# Check ingress
kubectl get ingress -n ld-apps-prod

# Test application endpoints
curl -f https://ld-apps.company.com/api/health
curl -f https://ld-apps.company.com/training/health
curl -f https://ld-apps.company.com/vendors/health
curl -f https://ld-apps.company.com/wins/health
```

#### Configure Alerting
```bash
# Update Prometheus alerting rules
kubectl apply -f k8s/monitoring/prometheus-values.yaml

# Test alert routing
kubectl exec -it deployment/prometheus -n monitoring -- \
  curl -X POST http://localhost:9090/-/reload
```

### 9. Performance Optimization

#### Enable Auto-scaling
```bash
# Verify HPA is configured
kubectl get hpa -n ld-apps-prod

# Test auto-scaling
kubectl run -i --tty load-test --rm --image=busybox --restart=Never -- \
  /bin/sh -c "while true; do wget -q -O- http://prod-backend-service:3000/health; done"
```

#### Configure CDN (Optional)
```bash
# Configure CloudFlare or similar CDN
# Update DNS records to point to CDN
# Configure cache rules for static assets
```

### 10. Security Hardening

#### Network Policies
```bash
# Apply network policies
kubectl apply -f k8s/base/network-policies.yaml

# Verify policies
kubectl get networkpolicies -n ld-apps-prod
```

#### Pod Security Standards
```bash
# Label namespace with security standards
kubectl label namespace ld-apps-prod \
  pod-security.kubernetes.io/enforce=restricted \
  pod-security.kubernetes.io/audit=restricted \
  pod-security.kubernetes.io/warn=restricted
```

## Post-Deployment Verification

### 1. Functional Testing
- [ ] User registration and login works
- [ ] All API endpoints respond correctly
- [ ] Database operations are functional
- [ ] File uploads work properly
- [ ] Email notifications are sent
- [ ] Frontend applications load correctly

### 2. Performance Testing
- [ ] Response times meet SLA requirements
- [ ] Load testing passes
- [ ] Database queries are optimized
- [ ] Cache hit rates are acceptable
- [ ] Memory and CPU usage are within limits

### 3. Security Testing
- [ ] SSL certificates are valid
- [ ] API rate limiting works
- [ ] Authentication and authorization work
- [ ] Network policies are enforced
- [ ] Secrets are properly managed

### 4. Monitoring and Alerting
- [ ] Prometheus is collecting metrics
- [ ] Grafana dashboards are working
- [ ] ELK stack is collecting logs
- [ ] Alerts are configured and firing
- [ ] Backup jobs are running

## Rollback Plan

### Emergency Rollback
```bash
# Rollback to previous deployment
kubectl rollout undo deployment/prod-backend -n ld-apps-prod
kubectl rollout undo deployment/prod-training-frontend -n ld-apps-prod
kubectl rollout undo deployment/prod-vendors-frontend -n ld-apps-prod
kubectl rollout undo deployment/prod-wins-frontend -n ld-apps-prod

# Verify rollback
kubectl rollout status deployment/prod-backend -n ld-apps-prod
```

### Database Rollback
```bash
# Restore from backup (if needed)
kubectl exec -it deployment/postgres -n ld-apps-prod -- \
  psql -U ld_admin -d ld_apps < /backup/ld_apps_backup.sql
```

## Maintenance Procedures

### Regular Maintenance Tasks
- [ ] **Daily**: Check application logs and metrics
- [ ] **Weekly**: Review security alerts and performance
- [ ] **Monthly**: Update dependencies and security patches
- [ ] **Quarterly**: Review and update backup/disaster recovery procedures

### Scaling Procedures
```bash
# Manual scaling
kubectl scale deployment prod-backend --replicas=5 -n ld-apps-prod

# Update HPA limits
kubectl patch hpa prod-backend-hpa -n ld-apps-prod -p '{"spec":{"maxReplicas":15}}'
```

### Backup Verification
```bash
# Test backup restoration
kubectl create job --from=cronjob/postgres-backup test-backup -n ld-apps-prod

# Verify backup integrity
kubectl logs job/test-backup -n ld-apps-prod
```

## Troubleshooting Guide

### Common Issues

#### Application Not Starting
```bash
# Check pod logs
kubectl logs deployment/prod-backend -n ld-apps-prod

# Check events
kubectl get events -n ld-apps-prod --sort-by=.metadata.creationTimestamp

# Check resource limits
kubectl describe pod -l app=backend -n ld-apps-prod
```

#### Database Connection Issues
```bash
# Check database connectivity
kubectl exec -it deployment/prod-backend -n ld-apps-prod -- \
  nc -zv postgres-service 5432

# Check database logs
kubectl logs deployment/postgres -n ld-apps-prod
```

#### Performance Issues
```bash
# Check resource usage
kubectl top pods -n ld-apps-prod

# Check HPA status
kubectl get hpa -n ld-apps-prod

# Review metrics in Grafana
# Check slow query logs
```

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-call Engineer**: <EMAIL>
- **Escalation**: <EMAIL>

## Success Criteria

### Deployment Success
- [x] All services deployed and running
- [x] Health checks passing
- [x] SSL certificates valid
- [x] Monitoring and alerting active
- [x] Backup systems operational
- [x] Performance targets met
- [x] Security controls in place

### Production Readiness
- [x] High availability configured
- [x] Auto-scaling enabled
- [x] Disaster recovery tested
- [x] Documentation complete
- [x] Team trained on operations
- [x] Runbooks prepared
- [x] Monitoring dashboards configured

## Conclusion

The L&D Applications have been successfully deployed to production with comprehensive monitoring, logging, and backup systems. The deployment follows industry best practices for security, scalability, and reliability.

Regular maintenance and monitoring will ensure continued optimal performance and security of the applications.