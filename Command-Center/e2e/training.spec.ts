import { test, expect } from '@playwright/test';

test.describe('Training Management Workflow', () => {
  // Helper function to login
  async function login(page: any) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/\/dashboard/);
  }

  test.beforeEach(async ({ page }) => {
    await login(page);
    // Navigate to Training app
    await page.goto('http://localhost:3001');
  });

  test('should display training dashboard with metrics', async ({ page }) => {
    // Check dashboard elements
    await expect(page.locator('h1:has-text("Training Dashboard")')).toBeVisible();
    
    // Check metric cards
    await expect(page.locator('[data-testid="total-courses"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-enrollments"]')).toBeVisible();
    await expect(page.locator('[data-testid="completion-rate"]')).toBeVisible();
    await expect(page.locator('[data-testid="skills-coverage"]')).toBeVisible();
    
    // Check recent activities
    await expect(page.locator('[data-testid="recent-activities"]')).toBeVisible();
  });

  test('should create a new training course', async ({ page }) => {
    // Navigate to courses page
    await page.click('text=Training Courses');
    
    // Click create course button
    await page.click('button:has-text("Create Course")');
    
    // Fill course form
    await page.fill('input[name="title"]', 'Advanced TypeScript Training');
    await page.fill('textarea[name="description"]', 'Deep dive into TypeScript advanced features');
    await page.fill('input[name="provider"]', 'Internal Training Team');
    await page.selectOption('select[name="type"]', 'workshop');
    await page.fill('input[name="duration"]', '16');
    await page.fill('input[name="cost"]', '500');
    await page.fill('input[name="capacity"]', '20');
    await page.selectOption('select[name="difficulty"]', 'advanced');
    
    // Select skills
    await page.click('[data-testid="skills-select"]');
    await page.click('text=TypeScript');
    await page.click('text=JavaScript');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Check success message
    await expect(page.locator('text=Course created successfully')).toBeVisible();
    
    // Verify course appears in list
    await expect(page.locator('text=Advanced TypeScript Training')).toBeVisible();
  });

  test('should enroll user in a course', async ({ page }) => {
    // Navigate to courses
    await page.click('text=Training Courses');
    
    // Click on a course
    await page.click('[data-testid="course-card"]:first-child');
    
    // Click enroll button
    await page.click('button:has-text("Enroll")');
    
    // Confirm enrollment
    await page.click('button:has-text("Confirm Enrollment")');
    
    // Check success message
    await expect(page.locator('text=Successfully enrolled')).toBeVisible();
    
    // Check enrollment status
    await expect(page.locator('text=Enrolled')).toBeVisible();
  });

  test('should complete skill assessment', async ({ page }) => {
    // Navigate to assessments
    await page.click('text=Skill Assessments');
    
    // Start assessment
    await page.click('button:has-text("Start Assessment")');
    
    // Fill assessment questions
    const questions = await page.locator('[data-testid="assessment-question"]').all();
    for (const question of questions) {
      // Rate skill level (1-5)
      await question.locator('input[type="radio"][value="4"]').click();
    }
    
    // Add comments
    await page.fill('textarea[name="comments"]', 'Self-assessment completed');
    
    // Submit assessment
    await page.click('button:has-text("Submit Assessment")');
    
    // Check completion
    await expect(page.locator('text=Assessment completed')).toBeVisible();
    
    // Check skill gaps identified
    await expect(page.locator('[data-testid="skill-gaps"]')).toBeVisible();
  });

  test('should view and accept training recommendations', async ({ page }) => {
    // Navigate to recommendations
    await page.click('text=Recommendations');
    
    // Check recommendations list
    await expect(page.locator('[data-testid="recommendation-card"]')).toHaveCount(3, { timeout: 10000 });
    
    // View recommendation details
    await page.click('[data-testid="recommendation-card"]:first-child');
    
    // Check details
    await expect(page.locator('[data-testid="recommendation-reasoning"]')).toBeVisible();
    await expect(page.locator('[data-testid="expected-impact"]')).toBeVisible();
    
    // Accept recommendation
    await page.click('button:has-text("Accept & Enroll")');
    
    // Check enrollment confirmation
    await expect(page.locator('text=Enrolled in recommended course')).toBeVisible();
  });

  test('should track course progress', async ({ page }) => {
    // Navigate to my learning
    await page.click('text=My Learning');
    
    // Click on enrolled course
    await page.click('[data-testid="enrolled-course"]:first-child');
    
    // Update progress
    await page.click('button:has-text("Mark Module Complete")');
    
    // Check progress update
    await expect(page.locator('[data-testid="course-progress"]')).toContainText('25%');
    
    // Complete all modules
    const modules = await page.locator('button:has-text("Mark Module Complete")').all();
    for (const module of modules) {
      await module.click();
      await page.waitForTimeout(500); // Wait for update
    }
    
    // Check completion
    await expect(page.locator('[data-testid="course-progress"]')).toContainText('100%');
    await expect(page.locator('text=Course Completed')).toBeVisible();
  });

  test('should generate training report', async ({ page }) => {
    // Navigate to analytics
    await page.click('text=Analytics');
    
    // Select report type
    await page.selectOption('select[name="reportType"]', 'department-overview');
    
    // Select date range
    await page.fill('input[name="startDate"]', '2024-01-01');
    await page.fill('input[name="endDate"]', '2024-12-31');
    
    // Generate report
    await page.click('button:has-text("Generate Report")');
    
    // Wait for report
    await expect(page.locator('[data-testid="report-content"]')).toBeVisible({ timeout: 15000 });
    
    // Check report sections
    await expect(page.locator('text=Training Metrics')).toBeVisible();
    await expect(page.locator('text=Skill Coverage')).toBeVisible();
    await expect(page.locator('text=Budget Utilization')).toBeVisible();
    
    // Export report
    await page.click('button:has-text("Export PDF")');
    
    // Check download started
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('training-report');
  });

  test('should manage learning paths', async ({ page }) => {
    // Navigate to learning paths
    await page.click('text=Learning Paths');
    
    // Create new path
    await page.click('button:has-text("Create Learning Path")');
    
    // Fill path details
    await page.fill('input[name="title"]', 'Full Stack Developer Path');
    await page.fill('textarea[name="description"]', 'Complete path to become a full stack developer');
    await page.selectOption('select[name="roleId"]', 'developer');
    await page.fill('input[name="estimatedDuration"]', '120');
    
    // Add courses to path
    await page.click('button:has-text("Add Courses")');
    await page.click('input[type="checkbox"][value="typescript-course"]');
    await page.click('input[type="checkbox"][value="react-course"]');
    await page.click('input[type="checkbox"][value="nodejs-course"]');
    await page.click('button:has-text("Add Selected")');
    
    // Save path
    await page.click('button:has-text("Save Learning Path")');
    
    // Check creation
    await expect(page.locator('text=Learning path created')).toBeVisible();
    await expect(page.locator('text=Full Stack Developer Path')).toBeVisible();
  });
});