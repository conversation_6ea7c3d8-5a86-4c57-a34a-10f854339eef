import { test, expect } from '@playwright/test';

test.describe('Wins of Week Workflow', () => {
  // Helper function to login
  async function login(page: any) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/\/dashboard/);
  }

  test.beforeEach(async ({ page }) => {
    await login(page);
    // Navigate to Wins app
    await page.goto('http://localhost:3003');
  });

  test('should display wins dashboard with current week info', async ({ page }) => {
    // Check dashboard elements
    await expect(page.locator('h1:has-text("Wins of the Week")')).toBeVisible();
    
    // Check current week display
    await expect(page.locator('[data-testid="current-week"]')).toBeVisible();
    await expect(page.locator('[data-testid="submission-status"]')).toBeVisible();
    
    // Check quick stats
    await expect(page.locator('[data-testid="total-achievements"]')).toBeVisible();
    await expect(page.locator('[data-testid="recognitions-given"]')).toBeVisible();
    await expect(page.locator('[data-testid="cost-savings"]')).toBeVisible();
    
    // Check recent wins showcase
    await expect(page.locator('[data-testid="recent-wins"]')).toBeVisible();
  });

  test('should create weekly submission with all sections', async ({ page }) => {
    // Click create submission
    await page.click('button:has-text("Create Weekly Submission")');
    
    // Add achievements
    await page.click('button:has-text("Add Achievement")');
    await page.fill('input[name="achievements[0].title"]', 'Completed API Integration');
    await page.fill('textarea[name="achievements[0].description"]', 'Successfully integrated payment gateway API');
    await page.selectOption('select[name="achievements[0].category"]', 'Technical');
    await page.fill('input[name="achievements[0].impact"]', 'Enabled online payments, increasing revenue by 15%');
    
    await page.click('button:has-text("Add Achievement")');
    await page.fill('input[name="achievements[1].title"]', 'Process Improvement');
    await page.fill('textarea[name="achievements[1].description"]', 'Automated deployment pipeline');
    await page.selectOption('select[name="achievements[1].category"]', 'Process');
    await page.fill('input[name="achievements[1].impact"]', 'Reduced deployment time from 2 hours to 15 minutes');
    
    // Add recognitions
    await page.click('[data-testid="recognitions-tab"]');
    await page.click('button:has-text("Add Recognition")');
    await page.fill('input[name="recognitions[0].recipientName"]', 'Sarah Johnson');
    await page.fill('input[name="recognitions[0].recipientRole"]', 'Senior Developer');
    await page.fill('textarea[name="recognitions[0].reason"]', 'Excellent mentoring on React best practices');
    await page.selectOption('select[name="recognitions[0].impactLevel"]', 'high');
    
    // Add cost initiatives
    await page.click('[data-testid="cost-initiatives-tab"]');
    await page.click('button:has-text("Add Cost Initiative")');
    await page.fill('textarea[name="costInitiatives[0].description"]', 'Optimized AWS resources by rightsizing instances');
    await page.fill('input[name="costInitiatives[0].estimatedSavings"]', '3500');
    await page.fill('input[name="costInitiatives[0].implementationDate"]', '2024-01-15');
    await page.selectOption('select[name="costInitiatives[0].category"]', 'Infrastructure');
    
    // Add training ideas
    await page.click('[data-testid="training-ideas-tab"]');
    await page.click('button:has-text("Add Training Idea")');
    await page.fill('input[name="trainingIdeas[0].title"]', 'Kubernetes Workshop');
    await page.fill('input[name="trainingIdeas[0].targetAudience"]', 'DevOps Team');
    await page.fill('textarea[name="trainingIdeas[0].expectedOutcome"]', 'Better container orchestration skills');
    await page.fill('input[name="trainingIdeas[0].estimatedCost"]', '2000');
    
    // Add progress updates
    await page.click('[data-testid="progress-updates-tab"]');
    await page.click('button:has-text("Add Progress Update")');
    await page.fill('input[name="progressUpdates[0].projectName"]', 'Customer Portal Redesign');
    await page.fill('input[name="progressUpdates[0].milestone"]', 'UI/UX Design Complete');
    await page.fill('input[name="progressUpdates[0].completionPercentage"]', '75');
    await page.fill('textarea[name="progressUpdates[0].nextSteps"]', 'Begin frontend implementation');
    
    // Add weekly highlight and summary
    await page.click('[data-testid="summary-tab"]');
    await page.fill('textarea[name="weeklyHighlight"]', 'Successfully launched the new payment integration with zero downtime');
    await page.fill('textarea[name="challenges"]', 'Had to work around legacy API limitations');
    await page.fill('textarea[name="nextWeekFocus"]', 'Complete frontend implementation and begin testing phase');
    
    // Save as draft
    await page.click('button:has-text("Save Draft")');
    await expect(page.locator('text=Draft saved successfully')).toBeVisible();
    
    // Submit
    await page.click('button:has-text("Submit")');
    await page.click('button:has-text("Confirm Submission")');
    
    // Check submission success
    await expect(page.locator('text=Submission completed')).toBeVisible();
    await expect(page.locator('[data-testid="submission-status"]')).toContainText('Submitted');
  });

  test('should view and edit draft submission', async ({ page }) => {
    // Navigate to my submissions
    await page.click('text=My Submissions');
    
    // Click on draft submission
    await page.click('[data-testid="submission-row"]:has-text("Draft")');
    
    // Edit achievement
    await page.click('[data-testid="edit-achievement-0"]');
    await page.fill('input[name="achievements[0].impact"]', 'Updated impact: Increased revenue by 20%');
    
    // Add new achievement
    await page.click('button:has-text("Add Achievement")');
    await page.fill('input[name="achievements[2].title"]', 'Additional Achievement');
    
    // Save changes
    await page.click('button:has-text("Save Changes")');
    await expect(page.locator('text=Changes saved')).toBeVisible();
  });

  test('should view team submissions and recognitions', async ({ page }) => {
    // Navigate to team view
    await page.click('text=Team Wins');
    
    // Check team submissions grid
    await expect(page.locator('[data-testid="team-submissions"]')).toBeVisible();
    
    // Filter by department
    await page.selectOption('select[name="department"]', 'Engineering');
    
    // View team member submission
    await page.click('[data-testid="team-member-card"]:first-child');
    
    // Check submission details
    await expect(page.locator('[data-testid="submission-details"]')).toBeVisible();
    await expect(page.locator('text=Achievements')).toBeVisible();
    await expect(page.locator('text=Recognitions')).toBeVisible();
    
    // Give kudos
    await page.click('button:has-text("Give Kudos")');
    await page.fill('textarea[name="kudosMessage"]', 'Great work on the API integration!');
    await page.click('button:has-text("Send Kudos")');
    
    // Check kudos sent
    await expect(page.locator('text=Kudos sent')).toBeVisible();
  });

  test('should view analytics and leaderboard', async ({ page }) => {
    // Navigate to analytics
    await page.click('text=Analytics');
    
    // Check analytics sections
    await expect(page.locator('text=Submission Trends')).toBeVisible();
    await expect(page.locator('[data-testid="submission-chart"]')).toBeVisible();
    
    await expect(page.locator('text=Achievement Categories')).toBeVisible();
    await expect(page.locator('[data-testid="achievement-pie-chart"]')).toBeVisible();
    
    await expect(page.locator('text=Cost Savings Overview')).toBeVisible();
    await expect(page.locator('[data-testid="cost-savings-chart"]')).toBeVisible();
    
    // View leaderboard
    await page.click('text=Leaderboard');
    
    // Check leaderboard display
    await expect(page.locator('[data-testid="leaderboard-table"]')).toBeVisible();
    await expect(page.locator('text=Top Contributors')).toBeVisible();
    
    // Change period
    await page.selectOption('select[name="period"]', 'month');
    await expect(page.locator('[data-testid="period-display"]')).toContainText('This Month');
    
    // Filter by department
    await page.selectOption('select[name="department"]', 'Engineering');
    await expect(page.locator('[data-testid="department-leaderboard"]')).toBeVisible();
  });

  test('should generate and view weekly digest', async ({ page }) => {
    // Navigate to weekly digest
    await page.click('text=Weekly Digest');
    
    // Check digest sections
    await expect(page.locator('text=Week Overview')).toBeVisible();
    await expect(page.locator('[data-testid="week-dates"]')).toBeVisible();
    await expect(page.locator('[data-testid="submission-count"]')).toBeVisible();
    
    // Top achievements
    await expect(page.locator('text=Top Achievements')).toBeVisible();
    await expect(page.locator('[data-testid="top-achievement"]')).toHaveCount(5, { timeout: 10000 });
    
    // Recognition highlights
    await expect(page.locator('text=Recognition Highlights')).toBeVisible();
    await expect(page.locator('[data-testid="recognition-card"]')).toBeVisible();
    
    // Cost savings summary
    await expect(page.locator('text=Cost Savings Impact')).toBeVisible();
    await expect(page.locator('[data-testid="total-savings"]')).toBeVisible();
    
    // Share digest
    await page.click('button:has-text("Share Digest")');
    await page.fill('input[name="shareEmails"]', '<EMAIL>, <EMAIL>');
    await page.click('button:has-text("Send")');
    
    // Check share success
    await expect(page.locator('text=Digest shared successfully')).toBeVisible();
  });

  test('should export wins data', async ({ page }) => {
    // Navigate to my submissions
    await page.click('text=My Submissions');
    
    // Click export
    await page.click('button:has-text("Export")');
    
    // Select export options
    await page.selectOption('select[name="format"]', 'pdf');
    await page.selectOption('select[name="dateRange"]', 'last-quarter');
    
    // Select sections to include
    await page.click('input[name="includeAchievements"]');
    await page.click('input[name="includeRecognitions"]');
    await page.click('input[name="includeCostSavings"]');
    
    // Export
    await page.click('button:has-text("Generate Export")');
    
    // Wait for generation
    await expect(page.locator('text=Generating export...')).toBeVisible();
    
    // Check download
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('wins-export');
    expect(download.suggestedFilename()).toContain('.pdf');
  });

  test('should handle submission approval workflow', async ({ page }) => {
    // Login as manager
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Navigate to Wins app
    await page.goto('http://localhost:3003');
    
    // Go to approvals
    await page.click('text=Approvals');
    
    // Check pending approvals
    await expect(page.locator('[data-testid="pending-approval"]')).toHaveCount(3, { timeout: 10000 });
    
    // Review submission
    await page.click('[data-testid="pending-approval"]:first-child');
    
    // Check all sections
    await page.click('[data-testid="achievements-tab"]');
    await page.click('[data-testid="recognitions-tab"]');
    await page.click('[data-testid="cost-initiatives-tab"]');
    
    // Add feedback
    await page.fill('textarea[name="feedback"]', 'Great work this week! The API integration was particularly impressive.');
    
    // Approve
    await page.click('button:has-text("Approve")');
    await page.click('button:has-text("Confirm Approval")');
    
    // Check approval success
    await expect(page.locator('text=Submission approved')).toBeVisible();
    await expect(page.locator('[data-testid="pending-approval"]')).toHaveCount(2);
  });
});