import { test, expect } from '@playwright/test';

test.describe('L&D Apps Integration Workflow', () => {
  // Helper function to login
  async function login(page: any, email: string) {
    await page.goto('/login');
    await page.fill('input[name="email"]', email);
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/\/dashboard/);
  }

  test('should complete full L&D workflow across all apps', async ({ page }) => {
    // Step 1: Manager identifies training need through Wins app
    await login(page, '<EMAIL>');
    await page.goto('http://localhost:3003');
    
    // Review team submissions and identify training gap
    await page.click('text=Team Wins');
    await page.click('[data-testid="team-member-card"]:has-text("John Doe")');
    
    // Notice multiple mentions of Kubernetes challenges
    await expect(page.locator('text=Kubernetes deployment issues')).toBeVisible();
    
    // Navigate to training ideas section
    await page.click('[data-testid="training-ideas-tab"]');
    await expect(page.locator('text=Kubernetes Workshop')).toBeVisible();
    
    // Step 2: Create training assessment in Training app
    await page.goto('http://localhost:3001');
    
    // Create assessment for team
    await page.click('text=Assessments');
    await page.click('button:has-text("Create Assessment")');
    
    await page.fill('input[name="title"]', 'DevOps Skills Assessment Q1 2024');
    await page.fill('textarea[name="description"]', 'Quarterly assessment focusing on containerization and orchestration skills');
    await page.selectOption('select[name="departmentId"]', 'Engineering');
    
    // Add Kubernetes skill to assess
    await page.click('[data-testid="skills-select"]');
    await page.click('text=Kubernetes');
    await page.click('text=Docker');
    await page.click('text=CI/CD');
    
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Assessment created')).toBeVisible();
    
    // Step 3: Employee completes assessment
    await page.click('button:has-text("Logout")');
    await login(page, '<EMAIL>');
    await page.goto('http://localhost:3001');
    
    // Complete assessment
    await page.click('text=My Assessments');
    await page.click('text=DevOps Skills Assessment Q1 2024');
    await page.click('button:has-text("Start Assessment")');
    
    // Rate Kubernetes skill as low
    await page.locator('[data-testid="skill-kubernetes"] input[value="2"]').click();
    await page.locator('[data-testid="skill-docker"] input[value="4"]').click();
    await page.locator('[data-testid="skill-cicd"] input[value="3"]').click();
    
    await page.click('button:has-text("Submit Assessment")');
    await expect(page.locator('text=Assessment completed')).toBeVisible();
    
    // Check skill gaps identified
    await expect(page.locator('text=Kubernetes - Gap: 3 levels')).toBeVisible();
    
    // Step 4: Search for vendors in Vendors app
    await page.goto('http://localhost:3002');
    
    // Search for Kubernetes training vendors
    await page.fill('input[placeholder="Search vendors..."]', 'Kubernetes');
    await page.selectOption('select[name="category"]', 'Training');
    
    // View vendor details
    await page.click('[data-testid="vendor-card"]:has-text("Cloud Native Training Co")');
    
    // Check vendor has Kubernetes expertise
    await expect(page.locator('text=Kubernetes Certified Service Provider')).toBeVisible();
    await expect(page.locator('[data-testid="vendor-rating"]')).toContainText('4.5');
    
    // View proposals
    await page.click('text=Proposals');
    await page.click('[data-testid="proposal-card"]:has-text("Kubernetes Mastery Program")');
    
    // Review proposal details
    await expect(page.locator('text=40-hour comprehensive program')).toBeVisible();
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$15,000');
    
    // Step 5: Manager approves vendor and creates training course
    await page.click('button:has-text("Logout")');
    await login(page, '<EMAIL>');
    
    // Approve vendor proposal in Vendors app
    await page.goto('http://localhost:3002');
    await page.click('text=Proposals');
    await page.click('[data-testid="proposal-status-filter"]');
    await page.click('text=Submitted');
    await page.click('[data-testid="proposal-card"]:has-text("Kubernetes Mastery Program")');
    
    await page.click('button:has-text("Approve Proposal")');
    await page.fill('textarea[name="approvalNotes"]', 'Approved for Q1 training budget');
    await page.click('button:has-text("Confirm Approval")');
    
    // Create training course in Training app
    await page.goto('http://localhost:3001');
    await page.click('text=Training Courses');
    await page.click('button:has-text("Create Course")');
    
    await page.fill('input[name="title"]', 'Kubernetes Mastery Program');
    await page.fill('textarea[name="description"]', 'Comprehensive Kubernetes training by Cloud Native Training Co');
    await page.fill('input[name="provider"]', 'Cloud Native Training Co');
    await page.selectOption('select[name="type"]', 'external');
    await page.fill('input[name="duration"]', '40');
    await page.fill('input[name="cost"]', '15000');
    await page.selectOption('select[name="difficulty"]', 'intermediate');
    
    // Link to Kubernetes skill
    await page.click('[data-testid="skills-select"]');
    await page.click('text=Kubernetes');
    
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Course created successfully')).toBeVisible();
    
    // Step 6: System generates recommendations
    await page.click('text=Recommendations');
    await page.click('button:has-text("Generate Recommendations")');
    
    // Wait for recommendations
    await expect(page.locator('text=Generating recommendations...')).toBeVisible();
    await expect(page.locator('[data-testid="recommendation-card"]')).toBeVisible({ timeout: 15000 });
    
    // Step 7: Employee enrolls in recommended course
    await page.click('button:has-text("Logout")');
    await login(page, '<EMAIL>');
    await page.goto('http://localhost:3001');
    
    // View recommendations
    await page.click('text=My Recommendations');
    await expect(page.locator('text=Kubernetes Mastery Program')).toBeVisible();
    await expect(page.locator('text=Based on your skill gap in Kubernetes')).toBeVisible();
    
    // Accept recommendation
    await page.click('[data-testid="recommendation-card"]:has-text("Kubernetes Mastery Program")');
    await page.click('button:has-text("Accept & Enroll")');
    
    await expect(page.locator('text=Successfully enrolled')).toBeVisible();
    
    // Step 8: Track progress and update wins
    // Simulate course completion (in real scenario, this would happen over time)
    await page.click('text=My Learning');
    await page.click('text=Kubernetes Mastery Program');
    
    // Update progress
    await page.fill('input[name="progress"]', '100');
    await page.click('button:has-text("Update Progress")');
    
    // Navigate to Wins app to report achievement
    await page.goto('http://localhost:3003');
    await page.click('button:has-text("Create Weekly Submission")');
    
    // Add achievement
    await page.click('button:has-text("Add Achievement")');
    await page.fill('input[name="achievements[0].title"]', 'Completed Kubernetes Certification');
    await page.fill('textarea[name="achievements[0].description"]', 'Successfully completed Kubernetes Mastery Program and passed certification exam');
    await page.selectOption('select[name="achievements[0].category"]', 'Learning');
    await page.fill('input[name="achievements[0].impact"]', 'Now capable of managing production Kubernetes clusters');
    
    // Add cost initiative based on new skills
    await page.click('[data-testid="cost-initiatives-tab"]');
    await page.click('button:has-text("Add Cost Initiative")');
    await page.fill('textarea[name="costInitiatives[0].description"]', 'Implemented Kubernetes autoscaling reducing cloud costs');
    await page.fill('input[name="costInitiatives[0].estimatedSavings"]', '8000');
    
    await page.click('button:has-text("Submit")');
    await page.click('button:has-text("Confirm Submission")');
    
    await expect(page.locator('text=Submission completed')).toBeVisible();
    
    // Step 9: Verify integration success
    // Manager views improved metrics
    await page.click('button:has-text("Logout")');
    await login(page, '<EMAIL>');
    
    // Check Training app metrics
    await page.goto('http://localhost:3001');
    await page.click('text=Analytics');
    
    await expect(page.locator('[data-testid="skill-gap-reduction"]')).toContainText('Kubernetes: -3');
    await expect(page.locator('[data-testid="completion-rate"]')).toContainText('100%');
    
    // Check Wins app ROI
    await page.goto('http://localhost:3003');
    await page.click('text=Analytics');
    
    await expect(page.locator('[data-testid="training-roi"]')).toBeVisible();
    await expect(page.locator('text=$8,000 monthly savings from Kubernetes optimization')).toBeVisible();
    
    // Verify vendor performance update
    await page.goto('http://localhost:3002');
    await page.click('text=Vendors');
    await page.click('text=Cloud Native Training Co');
    
    await expect(page.locator('[data-testid="successful-trainings"]')).toContainText('1');
    await expect(page.locator('[data-testid="vendor-rating"]')).toContainText('4.5');
  });

  test('should handle cross-app notifications and updates', async ({ page }) => {
    await login(page, '<EMAIL>');
    
    // Check unified notification center
    await page.click('[data-testid="notification-bell"]');
    
    // Should see notifications from all apps
    await expect(page.locator('text=New training recommendation available')).toBeVisible();
    await expect(page.locator('text=Weekly submission reminder')).toBeVisible();
    await expect(page.locator('text=Vendor proposal requires review')).toBeVisible();
    
    // Click training notification
    await page.click('text=New training recommendation available');
    
    // Should navigate to Training app
    await expect(page).toHaveURL(/localhost:3001/);
    await expect(page.locator('text=Recommended Courses')).toBeVisible();
  });

  test('should maintain consistent user session across apps', async ({ page }) => {
    await login(page, '<EMAIL>');
    
    // Navigate between apps without re-login
    await page.goto('http://localhost:3001'); // Training
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    await page.goto('http://localhost:3002'); // Vendors  
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    await page.goto('http://localhost:3003'); // Wins
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    // Verify user context is maintained
    const userName = await page.locator('[data-testid="user-name"]').textContent();
    expect(userName).toBe('Employee User');
  });
});