import { test, expect } from '@playwright/test';

test.describe('Vendor Management Workflow', () => {
  // Helper function to login
  async function login(page: any) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/\/dashboard/);
  }

  test.beforeEach(async ({ page }) => {
    await login(page);
    // Navigate to Vendors app
    await page.goto('http://localhost:3002');
  });

  test('should display vendor dashboard with statistics', async ({ page }) => {
    // Check dashboard elements
    await expect(page.locator('h1:has-text("Vendor Management")')).toBeVisible();
    
    // Check metric cards
    await expect(page.locator('[data-testid="total-vendors"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-vendors"]')).toBeVisible();
    await expect(page.locator('[data-testid="pending-proposals"]')).toBeVisible();
    await expect(page.locator('[data-testid="average-rating"]')).toBeVisible();
    
    // Check vendor categories chart
    await expect(page.locator('[data-testid="vendor-categories-chart"]')).toBeVisible();
  });

  test('should register a new vendor', async ({ page }) => {
    // Navigate to vendors list
    await page.click('text=Vendors');
    
    // Click add vendor button
    await page.click('button:has-text("Add Vendor")');
    
    // Fill vendor form
    await page.fill('input[name="companyName"]', 'Tech Training Solutions');
    await page.fill('input[name="contactPerson"]', 'John Smith');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="website"]', 'https://techtraining.com');
    await page.selectOption('select[name="category"]', 'Training');
    
    // Add phone numbers
    await page.click('button:has-text("Add Phone")');
    await page.fill('input[name="phoneNumbers[0].type"]', 'Primary');
    await page.fill('input[name="phoneNumbers[0].number"]', '+1234567890');
    
    // Add certifications
    await page.click('[data-testid="certifications-input"]');
    await page.type('[data-testid="certifications-input"]', 'ISO 9001');
    await page.keyboard.press('Enter');
    await page.type('[data-testid="certifications-input"]', 'ISO 27001');
    await page.keyboard.press('Enter');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Check success
    await expect(page.locator('text=Vendor registered successfully')).toBeVisible();
    await expect(page.locator('text=Tech Training Solutions')).toBeVisible();
  });

  test('should create and submit vendor proposal', async ({ page }) => {
    // Navigate to vendors
    await page.click('text=Vendors');
    
    // Click on a vendor
    await page.click('[data-testid="vendor-card"]:first-child');
    
    // Click create proposal
    await page.click('button:has-text("Create Proposal")');
    
    // Fill proposal form
    await page.fill('input[name="title"]', 'Annual Training Program 2024');
    await page.fill('textarea[name="description"]', 'Comprehensive training program for all departments');
    
    // Add cost items
    await page.click('button:has-text("Add Cost Item")');
    await page.fill('input[name="costs[0].item"]', 'Basic Training Sessions');
    await page.fill('input[name="costs[0].amount"]', '5000');
    await page.fill('input[name="costs[0].quantity"]', '10');
    
    await page.click('button:has-text("Add Cost Item")');
    await page.fill('input[name="costs[1].item"]', 'Advanced Workshops');
    await page.fill('input[name="costs[1].amount"]', '8000');
    await page.fill('input[name="costs[1].quantity"]', '5');
    
    // Set validity period
    const validUntil = new Date();
    validUntil.setMonth(validUntil.getMonth() + 3);
    await page.fill('input[name="validUntil"]', validUntil.toISOString().split('T')[0]);
    
    // Upload attachment
    await page.setInputFiles('input[type="file"]', {
      name: 'proposal.pdf',
      mimeType: 'application/pdf',
      buffer: Buffer.from('Mock PDF content'),
    });
    
    // Save as draft
    await page.click('button:has-text("Save Draft")');
    await expect(page.locator('text=Proposal saved')).toBeVisible();
    
    // Submit proposal
    await page.click('button:has-text("Submit Proposal")');
    await page.click('button:has-text("Confirm Submission")');
    
    // Check submission
    await expect(page.locator('text=Proposal submitted')).toBeVisible();
    await expect(page.locator('[data-testid="proposal-status"]')).toContainText('Submitted');
  });

  test('should review and evaluate vendor', async ({ page }) => {
    // Navigate to vendors
    await page.click('text=Vendors');
    
    // Click on a vendor with proposals
    await page.click('[data-testid="vendor-card"]:has-text("Has Proposals")');
    
    // Navigate to reviews tab
    await page.click('text=Reviews');
    
    // Click add review
    await page.click('button:has-text("Add Review")');
    
    // Fill review form
    await page.fill('input[name="reviewerName"]', 'Jane Doe');
    await page.fill('input[name="reviewerRole"]', 'Training Manager');
    
    // Set rating
    await page.click('[data-testid="rating-star-4"]');
    
    // Add strengths
    await page.click('[data-testid="strengths-input"]');
    await page.type('[data-testid="strengths-input"]', 'Excellent trainers');
    await page.keyboard.press('Enter');
    await page.type('[data-testid="strengths-input"]', 'Good course materials');
    await page.keyboard.press('Enter');
    await page.type('[data-testid="strengths-input"]', 'Flexible scheduling');
    await page.keyboard.press('Enter');
    
    // Add improvements
    await page.click('[data-testid="improvements-input"]');
    await page.type('[data-testid="improvements-input"]', 'Better online platform');
    await page.keyboard.press('Enter');
    
    // Add comments
    await page.fill('textarea[name="comment"]', 'Overall a great vendor with room for minor improvements in their online delivery platform.');
    
    // Submit review
    await page.click('button[type="submit"]');
    
    // Check review added
    await expect(page.locator('text=Review submitted successfully')).toBeVisible();
    await expect(page.locator('[data-testid="vendor-rating"]')).toBeVisible();
  });

  test('should compare multiple vendors', async ({ page }) => {
    // Navigate to vendors
    await page.click('text=Vendors');
    
    // Enter comparison mode
    await page.click('button:has-text("Compare Vendors")');
    
    // Select vendors to compare
    await page.click('[data-testid="vendor-checkbox-1"]');
    await page.click('[data-testid="vendor-checkbox-2"]');
    await page.click('[data-testid="vendor-checkbox-3"]');
    
    // View comparison
    await page.click('button:has-text("Compare Selected")');
    
    // Check comparison table
    await expect(page.locator('[data-testid="comparison-table"]')).toBeVisible();
    await expect(page.locator('text=Rating Comparison')).toBeVisible();
    await expect(page.locator('text=Cost Comparison')).toBeVisible();
    await expect(page.locator('text=Certifications')).toBeVisible();
    
    // Export comparison
    await page.click('button:has-text("Export Comparison")');
    
    // Check download
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('vendor-comparison');
  });

  test('should manage vendor contracts', async ({ page }) => {
    // Navigate to contracts
    await page.click('text=Contracts');
    
    // Create new contract
    await page.click('button:has-text("New Contract")');
    
    // Select vendor and proposal
    await page.selectOption('select[name="vendorId"]', { index: 1 });
    await page.waitForTimeout(500); // Wait for proposals to load
    await page.selectOption('select[name="proposalId"]', { index: 1 });
    
    // Fill contract details
    await page.fill('input[name="contractNumber"]', 'CTR-2024-001');
    await page.fill('input[name="startDate"]', '2024-01-01');
    await page.fill('input[name="endDate"]', '2024-12-31');
    
    // Add terms
    await page.fill('textarea[name="terms"]', 'Standard terms and conditions apply');
    
    // Upload signed contract
    await page.setInputFiles('input[name="contractFile"]', {
      name: 'contract.pdf',
      mimeType: 'application/pdf',
      buffer: Buffer.from('Mock contract PDF'),
    });
    
    // Save contract
    await page.click('button:has-text("Create Contract")');
    
    // Check creation
    await expect(page.locator('text=Contract created successfully')).toBeVisible();
    await expect(page.locator('text=CTR-2024-001')).toBeVisible();
  });

  test('should search and filter vendors', async ({ page }) => {
    // Navigate to vendors
    await page.click('text=Vendors');
    
    // Search by name
    await page.fill('input[placeholder="Search vendors..."]', 'Training');
    await page.waitForTimeout(500); // Debounce
    
    // Check filtered results
    const vendorCards = page.locator('[data-testid="vendor-card"]');
    await expect(vendorCards).toHaveCount(3, { timeout: 5000 });
    
    // Apply category filter
    await page.selectOption('select[name="category"]', 'Training');
    await expect(vendorCards).toHaveCount(2, { timeout: 5000 });
    
    // Apply rating filter
    await page.fill('input[name="minRating"]', '4');
    await expect(vendorCards).toHaveCount(1, { timeout: 5000 });
    
    // Clear filters
    await page.click('button:has-text("Clear Filters")');
    await expect(vendorCards.first()).toBeVisible();
  });

  test('should export vendor data', async ({ page }) => {
    // Navigate to vendors
    await page.click('text=Vendors');
    
    // Click export button
    await page.click('button:has-text("Export")');
    
    // Select export options
    await page.selectOption('select[name="format"]', 'excel');
    await page.click('input[name="includeProposals"]');
    await page.click('input[name="includeReviews"]');
    
    // Export
    await page.click('button:has-text("Export Data")');
    
    // Check download
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('vendors-export');
    expect(download.suggestedFilename()).toContain('.xlsx');
  });
});