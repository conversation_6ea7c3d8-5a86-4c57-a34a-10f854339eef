# E2E Tests for L&D Applications

This directory contains end-to-end tests using <PERSON><PERSON> for the L&D (Learning & Development) applications suite.

## Test Coverage

### Authentication Tests (`auth.spec.ts`)
- User registration with validation
- Login with valid/invalid credentials
- Logout functionality
- Session management and expiry
- Password requirements validation
- Refresh token rotation

### Training Management Tests (`training.spec.ts`)
- Training dashboard display
- Course creation and management
- User enrollment workflow
- Skill assessment completion
- Training recommendations
- Progress tracking
- Report generation
- Learning path management

### Vendor Management Tests (`vendors.spec.ts`)
- Vendor dashboard statistics
- Vendor registration
- Proposal creation and submission
- Vendor review and evaluation
- Vendor comparison
- Contract management
- Search and filtering
- Data export functionality

### Wins of Week Tests (`wins.spec.ts`)
- Wins dashboard display
- Weekly submission creation with all sections:
  - Achievements
  - Recognitions
  - Cost initiatives
  - Training ideas
  - Progress updates
- Draft editing and management
- Team submissions view
- Analytics and leaderboard
- Weekly digest generation
- Data export
- Approval workflow

### Integration Tests (`integration.spec.ts`)
- Full L&D workflow across all apps
- Cross-app notifications
- Session persistence across applications
- End-to-end business process validation

## Setup

1. Install Playwright and its dependencies:
```bash
npm install --save-dev @playwright/test
npm run playwright:install
```

2. Ensure all applications are running:
```bash
# Backend API
npm run start:dev

# Frontend apps (in separate terminals)
cd ../Training-need-analysis && npm run dev
cd ../Vendors && npm run dev  
cd ../Wins-of-Week && npm run dev
```

## Running Tests

### Run all tests
```bash
npm run test:e2e:ui
```

### Run tests in headed mode (see browser)
```bash
npm run test:e2e:ui:headed
```

### Debug tests
```bash
npm run test:e2e:ui:debug
```

### Run specific test file
```bash
npx playwright test e2e/auth.spec.ts
```

### Run tests for specific browser
```bash
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit
```

## Test Reports

After running tests, view the HTML report:
```bash
npm run test:e2e:ui:report
```

## Configuration

Tests are configured in `playwright.config.ts`:
- Base URL: `http://localhost:3000`
- Frontend apps:
  - Training: `http://localhost:3001`
  - Vendors: `http://localhost:3002`
  - Wins: `http://localhost:3003`
- Browsers: Chromium, Firefox, WebKit
- Screenshots: On failure only
- Videos: Retained on failure
- Traces: On first retry

## Writing New Tests

1. Create a new test file in the `e2e` directory
2. Import Playwright test utilities:
```typescript
import { test, expect } from '@playwright/test';
```

3. Structure tests using describe blocks:
```typescript
test.describe('Feature Name', () => {
  test('should do something', async ({ page }) => {
    // Test implementation
  });
});
```

4. Use data-testid attributes for reliable element selection:
```typescript
await page.click('[data-testid="submit-button"]');
```

## Best Practices

1. **Use Page Object Model** for complex pages
2. **Avoid hard-coded waits** - use Playwright's auto-waiting
3. **Make tests independent** - each test should be able to run in isolation
4. **Use meaningful test names** that describe the scenario
5. **Clean up test data** after tests complete
6. **Use fixtures** for common setup/teardown operations
7. **Implement retry logic** for flaky network operations

## Troubleshooting

### Tests failing due to timing issues
- Increase timeout in specific assertions:
```typescript
await expect(element).toBeVisible({ timeout: 10000 });
```

### Application not ready
- Ensure `webServer` configuration in playwright.config.ts has sufficient timeout
- Check that all required services are running

### Authentication issues
- Verify test user credentials exist in the database
- Check JWT token configuration matches between apps

### Element not found
- Use Playwright Inspector to debug selectors:
```bash
npx playwright test --debug
```

## CI/CD Integration

To run tests in CI environment:

1. Set environment variable:
```bash
export CI=true
```

2. Install browsers:
```bash
npx playwright install --with-deps
```

3. Run tests:
```bash
npm run test:e2e:ui
```

## Test Data Management

Test data is managed through:
- Database seeders for initial data
- API calls for dynamic test data creation
- Cleanup in `afterAll` hooks

## Performance Considerations

- Tests run in parallel by default
- Use `test.describe.serial()` for dependent tests
- Configure workers in playwright.config.ts based on CI resources

## Maintenance

Regular maintenance tasks:
1. Update selectors when UI changes
2. Review and update test data
3. Remove obsolete tests
4. Update Playwright version quarterly
5. Monitor test execution time and optimize slow tests