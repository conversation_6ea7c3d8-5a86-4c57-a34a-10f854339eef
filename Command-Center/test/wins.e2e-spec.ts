import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '@nestjs/common';
import { PrismaService } from '../src/database/prisma/prisma.service';

describe('WinsController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let accessToken: string;
  let userId: string;
  let submissionId: string;
  let departmentId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    prisma = app.get<PrismaService>(PrismaService);
    await app.init();

    // Setup: Create test user and authenticate
    const authResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Wins',
        lastName: 'Tester',
      });

    accessToken = authResponse.body.tokens.accessToken;
    userId = authResponse.body.user.id;

    // Create test department
    const department = await prisma.department.create({
      data: {
        name: 'Test Department',
        manager: 'Test Manager',
        employeeCount: 50,
      },
    });
    departmentId = department.id;

    // Update user with department
    await prisma.user.update({
      where: { id: userId },
      data: { departmentId },
    });
  });

  afterAll(async () => {
    // Cleanup
    await prisma.weeklySubmission.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.department.deleteMany({});
    await app.close();
  });

  describe('/wins/submissions (POST)', () => {
    it('should create a new weekly submission', () => {
      const weekStartDate = new Date();
      weekStartDate.setDate(weekStartDate.getDate() - 7);
      const weekEndDate = new Date();

      return request(app.getHttpServer())
        .post('/wins/submissions')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          userId: userId,
          weekStartDate: weekStartDate.toISOString(),
          weekEndDate: weekEndDate.toISOString(),
          achievements: [
            {
              title: 'Completed Training Module',
              description: 'Finished advanced TypeScript training',
              category: 'Learning',
              impact: 'Improved code quality by 20%'
            },
            {
              title: 'Process Improvement',
              description: 'Implemented automated testing pipeline',
              category: 'Process',
              impact: 'Reduced deployment time by 50%'
            }
          ],
          recognitions: [
            {
              recipientName: 'Jane Doe',
              recipientRole: 'Senior Developer',
              reason: 'Excellent mentoring on React project',
              impactLevel: 'high'
            }
          ],
          costInitiatives: [
            {
              description: 'Optimized cloud resources',
              estimatedSavings: 5000,
              implementationDate: new Date().toISOString(),
              category: 'Infrastructure'
            }
          ],
          trainingIdeas: [
            {
              title: 'Docker Workshop',
              targetAudience: 'Development Team',
              expectedOutcome: 'Better containerization practices',
              estimatedCost: 3000
            }
          ],
          progressUpdates: [
            {
              projectName: 'Customer Portal',
              milestone: 'Phase 1 Complete',
              completionPercentage: 100,
              nextSteps: 'Begin Phase 2 implementation'
            }
          ],
          weeklyHighlight: 'Successfully launched new feature with zero downtime',
          challenges: 'Integration with legacy system required extra effort',
          nextWeekFocus: 'Complete API documentation and performance optimization',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.userId).toBe(userId);
          expect(res.body.status).toBe('draft');
          expect(res.body.achievements).toHaveLength(2);
          expect(res.body.recognitions).toHaveLength(1);
          submissionId = res.body.id;
        });
    });

    it('should fail without authentication', () => {
      return request(app.getHttpServer())
        .post('/wins/submissions')
        .send({
          userId: userId,
          weekStartDate: new Date().toISOString(),
          weekEndDate: new Date().toISOString(),
        })
        .expect(401);
    });

    it('should prevent duplicate submissions for same week', () => {
      const weekStartDate = new Date();
      weekStartDate.setDate(weekStartDate.getDate() - 7);
      const weekEndDate = new Date();

      return request(app.getHttpServer())
        .post('/wins/submissions')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          userId: userId,
          weekStartDate: weekStartDate.toISOString(),
          weekEndDate: weekEndDate.toISOString(),
          achievements: [],
          recognitions: [],
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('already exists');
        });
    });
  });

  describe('/wins/submissions (GET)', () => {
    it('should get all submissions', () => {
      return request(app.getHttpServer())
        .get('/wins/submissions')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('submissions');
          expect(res.body).toHaveProperty('pagination');
          expect(Array.isArray(res.body.submissions)).toBe(true);
          expect(res.body.submissions.length).toBeGreaterThan(0);
        });
    });

    it('should filter submissions by user', () => {
      return request(app.getHttpServer())
        .get(`/wins/submissions?userId=${userId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.submissions.every(s => s.userId === userId)).toBe(true);
        });
    });

    it('should filter submissions by status', () => {
      return request(app.getHttpServer())
        .get('/wins/submissions?status=draft')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.submissions.every(s => s.status === 'draft')).toBe(true);
        });
    });

    it('should filter submissions by date range', () => {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
      const endDate = new Date();

      return request(app.getHttpServer())
        .get(`/wins/submissions?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.submissions.length).toBeGreaterThan(0);
        });
    });
  });

  describe('/wins/submissions/:id (GET)', () => {
    it('should get submission by id', () => {
      return request(app.getHttpServer())
        .get(`/wins/submissions/${submissionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(submissionId);
          expect(res.body).toHaveProperty('user');
          expect(res.body.achievements).toHaveLength(2);
        });
    });

    it('should return 404 for non-existent submission', () => {
      return request(app.getHttpServer())
        .get('/wins/submissions/00000000-0000-0000-0000-000000000000')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });
  });

  describe('/wins/submissions/:id (PUT)', () => {
    it('should update a submission', () => {
      return request(app.getHttpServer())
        .put(`/wins/submissions/${submissionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          weeklyHighlight: 'Updated highlight with more details',
          status: 'submitted',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.weeklyHighlight).toBe('Updated highlight with more details');
          expect(res.body.status).toBe('submitted');
        });
    });

    it('should add new achievements to submission', () => {
      const existingSubmission = request(app.getHttpServer())
        .get(`/wins/submissions/${submissionId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      return request(app.getHttpServer())
        .put(`/wins/submissions/${submissionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          achievements: [
            ...existingSubmission.body?.achievements || [],
            {
              title: 'New Achievement',
              description: 'Completed additional task',
              category: 'Other',
              impact: 'Minor improvement'
            }
          ]
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.achievements.length).toBeGreaterThan(2);
        });
    });
  });

  describe('/wins/analytics (GET)', () => {
    it('should get wins analytics', () => {
      return request(app.getHttpServer())
        .get('/wins/analytics')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalSubmissions');
          expect(res.body).toHaveProperty('submissionsByStatus');
          expect(res.body).toHaveProperty('totalAchievements');
          expect(res.body).toHaveProperty('achievementsByCategory');
          expect(res.body).toHaveProperty('totalRecognitions');
          expect(res.body).toHaveProperty('costSavingsTotal');
          expect(res.body).toHaveProperty('trainingIdeasCount');
          expect(res.body).toHaveProperty('topContributors');
        });
    });

    it('should get department analytics', () => {
      return request(app.getHttpServer())
        .get(`/wins/analytics?departmentId=${departmentId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('departmentStats');
          expect(res.body.departmentStats).toHaveProperty('totalSubmissions');
          expect(res.body.departmentStats).toHaveProperty('participationRate');
        });
    });

    it('should get analytics for date range', () => {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 3);
      const endDate = new Date();

      return request(app.getHttpServer())
        .get(`/wins/analytics?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('dateRange');
          expect(res.body.dateRange).toHaveProperty('start');
          expect(res.body.dateRange).toHaveProperty('end');
        });
    });
  });

  describe('/wins/weekly-digest (GET)', () => {
    it('should get weekly digest', () => {
      return request(app.getHttpServer())
        .get('/wins/weekly-digest')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('weekStartDate');
          expect(res.body).toHaveProperty('weekEndDate');
          expect(res.body).toHaveProperty('submissions');
          expect(res.body).toHaveProperty('highlights');
          expect(res.body).toHaveProperty('topAchievements');
          expect(res.body).toHaveProperty('recognitions');
          expect(res.body).toHaveProperty('costSavings');
        });
    });
  });

  describe('/wins/export (POST)', () => {
    it('should export submissions data', () => {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
      const endDate = new Date();

      return request(app.getHttpServer())
        .post('/wins/export')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          format: 'csv',
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          includeFields: [
            'achievements',
            'recognitions',
            'costInitiatives',
            'weeklyHighlight'
          ]
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('fileUrl');
          expect(res.body).toHaveProperty('format');
          expect(res.body.format).toBe('csv');
        });
    });

    it('should export in Excel format', () => {
      return request(app.getHttpServer())
        .post('/wins/export')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          format: 'excel',
          userId: userId,
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.format).toBe('excel');
        });
    });
  });

  describe('/wins/leaderboard (GET)', () => {
    it('should get leaderboard', () => {
      return request(app.getHttpServer())
        .get('/wins/leaderboard')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('period');
          expect(res.body).toHaveProperty('leaderboard');
          expect(Array.isArray(res.body.leaderboard)).toBe(true);
          if (res.body.leaderboard.length > 0) {
            expect(res.body.leaderboard[0]).toHaveProperty('userId');
            expect(res.body.leaderboard[0]).toHaveProperty('userName');
            expect(res.body.leaderboard[0]).toHaveProperty('achievementCount');
            expect(res.body.leaderboard[0]).toHaveProperty('recognitionCount');
            expect(res.body.leaderboard[0]).toHaveProperty('costSavings');
            expect(res.body.leaderboard[0]).toHaveProperty('score');
          }
        });
    });

    it('should get monthly leaderboard', () => {
      return request(app.getHttpServer())
        .get('/wins/leaderboard?period=month')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.period).toBe('month');
        });
    });

    it('should get department leaderboard', () => {
      return request(app.getHttpServer())
        .get(`/wins/leaderboard?departmentId=${departmentId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('departmentId');
          expect(res.body.departmentId).toBe(departmentId);
        });
    });
  });

  describe('/wins/recognitions (GET)', () => {
    it('should get all recognitions', () => {
      return request(app.getHttpServer())
        .get('/wins/recognitions')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          if (res.body.length > 0) {
            expect(res.body[0]).toHaveProperty('recipientName');
            expect(res.body[0]).toHaveProperty('reason');
            expect(res.body[0]).toHaveProperty('submittedBy');
          }
        });
    });
  });

  describe('/wins/submissions/:id (DELETE)', () => {
    it('should delete a draft submission', () => {
      return request(app.getHttpServer())
        .delete(`/wins/submissions/${submissionId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });

    it('should not delete a reviewed submission', async () => {
      // Create and submit a new submission
      const newSubmission = await prisma.weeklySubmission.create({
        data: {
          userId: userId,
          weekStartDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          weekEndDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          achievements: [],
          recognitions: [],
          costInitiatives: [],
          trainingIdeas: [],
          progressUpdates: [],
          status: 'reviewed',
        },
      });

      return request(app.getHttpServer())
        .delete(`/wins/submissions/${newSubmission.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('Cannot delete reviewed submission');
        });
    });
  });
});