import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '@nestjs/common';
import { PrismaService } from '../src/database/prisma/prisma.service';

describe('TrainingController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let accessToken: string;
  let userId: string;
  let courseId: string;
  let skillId: string;
  let enrollmentId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    prisma = app.get<PrismaService>(PrismaService);
    await app.init();

    // Setup: Create test user and authenticate
    const authResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Training',
        lastName: 'Tester',
      });

    accessToken = authResponse.body.tokens.accessToken;
    userId = authResponse.body.user.id;

    // Create test skill
    const skill = await prisma.skill.create({
      data: {
        name: 'Test Skill',
        category: 'Technical',
        description: 'Test skill for integration tests',
        levels: [
          { level: 1, description: 'Beginner' },
          { level: 2, description: 'Intermediate' },
          { level: 3, description: 'Advanced' },
        ],
      },
    });
    skillId = skill.id;
  });

  afterAll(async () => {
    // Cleanup
    await prisma.trainingEnrollment.deleteMany({});
    await prisma.trainingRecommendation.deleteMany({});
    await prisma.trainingCourse.deleteMany({});
    await prisma.skill.deleteMany({});
    await prisma.user.deleteMany({});
    await app.close();
  });

  describe('/training/courses (POST)', () => {
    it('should create a new training course', () => {
      return request(app.getHttpServer())
        .post('/training/courses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          title: 'Test Course',
          description: 'A test training course',
          provider: 'Internal',
          type: 'workshop',
          duration: 16,
          cost: 500,
          capacity: 20,
          prerequisites: [],
          difficulty: 'intermediate',
          skillIds: [skillId],
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.title).toBe('Test Course');
          expect(res.body.skills).toHaveLength(1);
          courseId = res.body.id;
        });
    });

    it('should fail without authentication', () => {
      return request(app.getHttpServer())
        .post('/training/courses')
        .send({
          title: 'Test Course',
          description: 'A test training course',
          provider: 'Internal',
          type: 'workshop',
          duration: 16,
          cost: 500,
        })
        .expect(401);
    });

    it('should validate required fields', () => {
      return request(app.getHttpServer())
        .post('/training/courses')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          title: 'Test Course',
          // Missing required fields
        })
        .expect(400);
    });
  });

  describe('/training/courses (GET)', () => {
    it('should get all training courses', () => {
      return request(app.getHttpServer())
        .get('/training/courses')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('courses');
          expect(res.body).toHaveProperty('pagination');
          expect(Array.isArray(res.body.courses)).toBe(true);
          expect(res.body.courses.length).toBeGreaterThan(0);
        });
    });

    it('should filter courses by type', () => {
      return request(app.getHttpServer())
        .get('/training/courses?type=workshop')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.courses.every(c => c.type === 'workshop')).toBe(true);
        });
    });

    it('should filter courses by difficulty', () => {
      return request(app.getHttpServer())
        .get('/training/courses?difficulty=intermediate')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.courses.every(c => c.difficulty === 'intermediate')).toBe(true);
        });
    });

    it('should search courses by title', () => {
      return request(app.getHttpServer())
        .get('/training/courses?search=Test')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.courses.length).toBeGreaterThan(0);
          expect(res.body.courses[0].title).toContain('Test');
        });
    });
  });

  describe('/training/courses/:id (GET)', () => {
    it('should get course by id', () => {
      return request(app.getHttpServer())
        .get(`/training/courses/${courseId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(courseId);
          expect(res.body).toHaveProperty('skills');
          expect(res.body).toHaveProperty('enrollments_rel');
          expect(res.body).toHaveProperty('recommendations');
        });
    });

    it('should return 404 for non-existent course', () => {
      return request(app.getHttpServer())
        .get('/training/courses/00000000-0000-0000-0000-000000000000')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });
  });

  describe('/training/courses/:id (PUT)', () => {
    it('should update a course', () => {
      return request(app.getHttpServer())
        .put(`/training/courses/${courseId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          title: 'Updated Test Course',
          cost: 600,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.title).toBe('Updated Test Course');
          expect(res.body.cost).toBe(600);
        });
    });
  });

  describe('/training/enrollments (POST)', () => {
    it('should enroll user in a course', () => {
      return request(app.getHttpServer())
        .post('/training/enrollments')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          userId: userId,
          courseId: courseId,
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.userId).toBe(userId);
          expect(res.body.courseId).toBe(courseId);
          expect(res.body.status).toBe('enrolled');
          enrollmentId = res.body.id;
        });
    });

    it('should fail to enroll in the same course twice', () => {
      return request(app.getHttpServer())
        .post('/training/enrollments')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          userId: userId,
          courseId: courseId,
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('already enrolled');
        });
    });
  });

  describe('/training/enrollments (GET)', () => {
    it('should get user enrollments', () => {
      return request(app.getHttpServer())
        .get(`/training/enrollments?userId=${userId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
          expect(res.body[0]).toHaveProperty('user');
          expect(res.body[0]).toHaveProperty('course');
        });
    });

    it('should get course enrollments', () => {
      return request(app.getHttpServer())
        .get(`/training/enrollments?courseId=${courseId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.every(e => e.courseId === courseId)).toBe(true);
        });
    });
  });

  describe('/training/enrollments/:id (PUT)', () => {
    it('should update enrollment progress', () => {
      return request(app.getHttpServer())
        .put(`/training/enrollments/${enrollmentId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          progress: 50,
          status: 'in-progress',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.progress).toBe(50);
          expect(res.body.status).toBe('in-progress');
        });
    });

    it('should complete enrollment', () => {
      return request(app.getHttpServer())
        .put(`/training/enrollments/${enrollmentId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          progress: 100,
          status: 'completed',
          score: 85,
          completedDate: new Date().toISOString(),
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('completed');
          expect(res.body.score).toBe(85);
        });
    });
  });

  describe('/training/recommendations/:userId (GET)', () => {
    it('should get user recommendations', () => {
      return request(app.getHttpServer())
        .get(`/training/recommendations/${userId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('/training/recommendations/:userId/generate (POST)', () => {
    it('should generate recommendations for user', () => {
      return request(app.getHttpServer())
        .post(`/training/recommendations/${userId}/generate`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(201)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('/training/metrics (GET)', () => {
    it('should get training metrics', () => {
      return request(app.getHttpServer())
        .get('/training/metrics')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalCourses');
          expect(res.body).toHaveProperty('totalEnrollments');
          expect(res.body).toHaveProperty('completionRate');
        });
    });
  });

  describe('/training/courses/:id (DELETE)', () => {
    it('should fail to delete course with active enrollments', () => {
      return request(app.getHttpServer())
        .delete(`/training/courses/${courseId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('active enrollments');
        });
    });

    it('should delete course after clearing enrollments', async () => {
      // First, clear the enrollment
      await prisma.trainingEnrollment.deleteMany({
        where: { courseId }
      });

      return request(app.getHttpServer())
        .delete(`/training/courses/${courseId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });
  });
});