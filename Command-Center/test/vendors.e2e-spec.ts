import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '@nestjs/common';
import { PrismaService } from '../src/database/prisma/prisma.service';

describe('VendorsController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let accessToken: string;
  let vendorId: string;
  let proposalId: string;
  let reviewId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    prisma = app.get<PrismaService>(PrismaService);
    await app.init();

    // Setup: Create test user and authenticate
    const authResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Vendor',
        lastName: 'Tester',
      });

    accessToken = authResponse.body.tokens.accessToken;
  });

  afterAll(async () => {
    // Cleanup
    await prisma.review.deleteMany({});
    await prisma.proposal.deleteMany({});
    await prisma.vendor.deleteMany({});
    await prisma.user.deleteMany({});
    await app.close();
  });

  describe('/vendors (POST)', () => {
    it('should create a new vendor', () => {
      return request(app.getHttpServer())
        .post('/vendors')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          companyName: 'Test Training Company',
          contactPerson: 'John Doe',
          phoneNumbers: [
            { type: 'primary', number: '+1234567890' },
            { type: 'support', number: '+0987654321' }
          ],
          email: '<EMAIL>',
          website: 'https://testcompany.com',
          category: 'Training',
          certifications: ['ISO 9001', 'ISO 27001'],
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.companyName).toBe('Test Training Company');
          expect(res.body.phoneNumbers).toHaveLength(2);
          expect(res.body.status).toBe('active');
          expect(res.body.rating).toBe(0);
          vendorId = res.body.id;
        });
    });

    it('should fail without authentication', () => {
      return request(app.getHttpServer())
        .post('/vendors')
        .send({
          companyName: 'Test Company',
          contactPerson: 'John Doe',
          email: '<EMAIL>',
        })
        .expect(401);
    });

    it('should validate required fields', () => {
      return request(app.getHttpServer())
        .post('/vendors')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          companyName: 'Test Company',
          // Missing required fields
        })
        .expect(400);
    });

    it('should validate email format', () => {
      return request(app.getHttpServer())
        .post('/vendors')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          companyName: 'Test Company',
          contactPerson: 'John Doe',
          phoneNumbers: [],
          email: 'invalid-email',
          category: 'Training',
        })
        .expect(400);
    });
  });

  describe('/vendors (GET)', () => {
    it('should get all vendors', () => {
      return request(app.getHttpServer())
        .get('/vendors')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('vendors');
          expect(res.body).toHaveProperty('pagination');
          expect(Array.isArray(res.body.vendors)).toBe(true);
          expect(res.body.vendors.length).toBeGreaterThan(0);
        });
    });

    it('should filter vendors by category', () => {
      return request(app.getHttpServer())
        .get('/vendors?category=Training')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.vendors.every(v => v.category === 'Training')).toBe(true);
        });
    });

    it('should filter vendors by status', () => {
      return request(app.getHttpServer())
        .get('/vendors?status=active')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.vendors.every(v => v.status === 'active')).toBe(true);
        });
    });

    it('should search vendors by name', () => {
      return request(app.getHttpServer())
        .get('/vendors?search=Test')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.vendors.length).toBeGreaterThan(0);
          expect(res.body.vendors[0].companyName).toContain('Test');
        });
    });

    it('should filter by minimum rating', () => {
      return request(app.getHttpServer())
        .get('/vendors?minRating=0')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.vendors.every(v => v.rating >= 0)).toBe(true);
        });
    });
  });

  describe('/vendors/:id (GET)', () => {
    it('should get vendor by id', () => {
      return request(app.getHttpServer())
        .get(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(vendorId);
          expect(res.body).toHaveProperty('proposals');
          expect(res.body).toHaveProperty('reviews');
        });
    });

    it('should return 404 for non-existent vendor', () => {
      return request(app.getHttpServer())
        .get('/vendors/00000000-0000-0000-0000-000000000000')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });
  });

  describe('/vendors/:id (PUT)', () => {
    it('should update a vendor', () => {
      return request(app.getHttpServer())
        .put(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          companyName: 'Updated Test Company',
          website: 'https://updated.testcompany.com',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.companyName).toBe('Updated Test Company');
          expect(res.body.website).toBe('https://updated.testcompany.com');
        });
    });

    it('should update vendor status', () => {
      return request(app.getHttpServer())
        .put(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          status: 'inactive',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('inactive');
        });
    });
  });

  describe('/vendors/:vendorId/proposals (POST)', () => {
    it('should create a new proposal', () => {
      const validUntil = new Date();
      validUntil.setMonth(validUntil.getMonth() + 3);

      return request(app.getHttpServer())
        .post(`/vendors/${vendorId}/proposals`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          title: 'Corporate Training Proposal',
          description: 'Comprehensive training program for 2024',
          costs: [
            { item: 'Basic Training', amount: 5000, quantity: 10 },
            { item: 'Advanced Workshop', amount: 8000, quantity: 5 }
          ],
          totalCost: 90000,
          validUntil: validUntil.toISOString(),
          attachments: ['proposal.pdf', 'training-outline.docx'],
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.vendorId).toBe(vendorId);
          expect(res.body.title).toBe('Corporate Training Proposal');
          expect(res.body.status).toBe('draft');
          proposalId = res.body.id;
        });
    });

    it('should validate proposal data', () => {
      return request(app.getHttpServer())
        .post(`/vendors/${vendorId}/proposals`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          title: 'Invalid Proposal',
          // Missing required fields
        })
        .expect(400);
    });
  });

  describe('/vendors/proposals (GET)', () => {
    it('should get all proposals', () => {
      return request(app.getHttpServer())
        .get('/vendors/proposals')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('proposals');
          expect(res.body).toHaveProperty('pagination');
          expect(Array.isArray(res.body.proposals)).toBe(true);
        });
    });

    it('should filter proposals by vendor', () => {
      return request(app.getHttpServer())
        .get(`/vendors/proposals?vendorId=${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.proposals.every(p => p.vendorId === vendorId)).toBe(true);
        });
    });

    it('should filter proposals by status', () => {
      return request(app.getHttpServer())
        .get('/vendors/proposals?status=draft')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.proposals.every(p => p.status === 'draft')).toBe(true);
        });
    });

    it('should filter proposals by cost range', () => {
      return request(app.getHttpServer())
        .get('/vendors/proposals?minCost=10000&maxCost=100000')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.proposals.every(p => 
            p.totalCost >= 10000 && p.totalCost <= 100000
          )).toBe(true);
        });
    });
  });

  describe('/vendors/proposals/:id (GET)', () => {
    it('should get proposal by id', () => {
      return request(app.getHttpServer())
        .get(`/vendors/proposals/${proposalId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(proposalId);
          expect(res.body).toHaveProperty('vendor');
          expect(res.body).toHaveProperty('reviews');
        });
    });
  });

  describe('/vendors/proposals/:id (PUT)', () => {
    it('should update a proposal', () => {
      return request(app.getHttpServer())
        .put(`/vendors/proposals/${proposalId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          status: 'submitted',
          submittedAt: new Date().toISOString(),
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('submitted');
          expect(res.body.submittedAt).toBeTruthy();
        });
    });
  });

  describe('/vendors/:vendorId/reviews (POST)', () => {
    it('should create a vendor review', () => {
      return request(app.getHttpServer())
        .post(`/vendors/${vendorId}/reviews`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          rating: 4,
          reviewerName: 'John Smith',
          reviewerRole: 'Training Manager',
          comment: 'Good training provider with experienced instructors',
          strengths: ['Expert instructors', 'Good materials', 'Flexible scheduling'],
          improvements: ['Could improve online platform', 'Better follow-up support'],
          proposalId: proposalId,
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.vendorId).toBe(vendorId);
          expect(res.body.rating).toBe(4);
          reviewId = res.body.id;
        });
    });

    it('should validate rating range', () => {
      return request(app.getHttpServer())
        .post(`/vendors/${vendorId}/reviews`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          rating: 6, // Invalid: should be 1-5
          reviewerName: 'John Smith',
          reviewerRole: 'Manager',
          comment: 'Test review',
        })
        .expect(400);
    });
  });

  describe('/vendors/reviews (GET)', () => {
    it('should get all reviews', () => {
      return request(app.getHttpServer())
        .get('/vendors/reviews')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });

    it('should get reviews by vendor', () => {
      return request(app.getHttpServer())
        .get(`/vendors/reviews?vendorId=${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.every(r => r.vendorId === vendorId)).toBe(true);
        });
    });

    it('should filter reviews by rating', () => {
      return request(app.getHttpServer())
        .get('/vendors/reviews?minRating=4')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.every(r => r.rating >= 4)).toBe(true);
        });
    });
  });

  describe('/vendors/analytics (GET)', () => {
    it('should get vendor analytics', () => {
      return request(app.getHttpServer())
        .get('/vendors/analytics')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalVendors');
          expect(res.body).toHaveProperty('activeVendors');
          expect(res.body).toHaveProperty('vendorsByCategory');
          expect(res.body).toHaveProperty('topRatedVendors');
          expect(res.body).toHaveProperty('averageRating');
          expect(res.body).toHaveProperty('totalProposals');
          expect(res.body).toHaveProperty('proposalsByStatus');
        });
    });
  });

  describe('/vendors/compare (POST)', () => {
    it('should compare vendors', async () => {
      // Create another vendor for comparison
      const vendor2Response = await request(app.getHttpServer())
        .post('/vendors')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          companyName: 'Competitor Training Inc',
          contactPerson: 'Jane Doe',
          phoneNumbers: [{ type: 'primary', number: '+1112223333' }],
          email: '<EMAIL>',
          category: 'Training',
          certifications: ['ISO 9001'],
        });

      const vendor2Id = vendor2Response.body.id;

      return request(app.getHttpServer())
        .post('/vendors/compare')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          vendorIds: [vendorId, vendor2Id],
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('vendors');
          expect(res.body).toHaveProperty('comparison');
          expect(res.body.vendors).toHaveLength(2);
          expect(res.body.comparison).toHaveProperty('ratings');
          expect(res.body.comparison).toHaveProperty('certifications');
          expect(res.body.comparison).toHaveProperty('proposalCounts');
        });
    });
  });

  describe('/vendors/:id (DELETE)', () => {
    it('should fail to delete vendor with proposals', () => {
      return request(app.getHttpServer())
        .delete(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('has associated proposals');
        });
    });

    it('should delete vendor after clearing proposals', async () => {
      // First update vendor back to active for reactivation test
      await request(app.getHttpServer())
        .put(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ status: 'active' });

      // Delete all proposals
      await prisma.proposal.deleteMany({
        where: { vendorId }
      });

      return request(app.getHttpServer())
        .delete(`/vendors/${vendorId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });
  });
});