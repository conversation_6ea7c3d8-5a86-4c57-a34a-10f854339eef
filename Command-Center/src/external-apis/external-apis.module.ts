import { Modu<PERSON> } from '@nestjs/common';
import { ExternalApisController } from './external-apis.controller';
import { ExternalApisModule as CommonExternalApisModule } from '../common/external-apis/external-apis.module';
import { ErrorHandlingService } from '../common/external-apis/error-handling.service';
import { AuthModule } from '../auth/auth.module';
import { RedisModule } from '../common/cache/redis.module';
import { LoggerModule } from '../common/logger/logger.module';

@Module({
  imports: [
    CommonExternalApisModule,
    AuthModule,
    RedisModule,
    LoggerModule,
  ],
  controllers: [ExternalApisController],
  providers: [ErrorHandlingService],
})
export class ExternalApisModule {}