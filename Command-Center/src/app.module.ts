import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './modules/health/health.module';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { FilesModule } from './modules/files/files.module';
import { RedisModule } from './modules/redis/redis.module';
import { EnhancedCacheModule } from './modules/cache/cache.module';
import { QueueModule } from './modules/queue/queue.module';
import { VectorModule } from './modules/vector/vector.module';
import { PrismaModule } from './database/prisma/prisma.module';
import { TypeOrmConfigService } from './database/typeorm/typeorm-config.service';
import { PerformanceModule } from './modules/performance/performance.module';
import { ScalabilityModule } from './modules/scalability/scalability.module';
import { TestingModule } from './modules/testing/testing.module';
import { DeploymentModule } from './modules/deployment/deployment.module';
import { AmnaModule } from './modules/amna/amna.module';
import { TrainingModule } from './modules/training/training.module';
import { VendorModule } from './modules/vendor/vendor.module';
import { WinsModule } from './modules/wins/wins.module';
import configuration from './config/configuration';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useClass: TypeOrmConfigService,
    }),
    PrismaModule,
    RedisModule,
    EnhancedCacheModule,
    QueueModule,
    VectorModule,
    PerformanceModule,
    ScalabilityModule,
    TestingModule,
    DeploymentModule,
    HealthModule,
    UsersModule,
    AuthModule,
    DocumentsModule,
    FilesModule,
    AmnaModule,
    TrainingModule,
    VendorModule,
    WinsModule,
  ],
  controllers: [AppController],
  providers: [AppService, TypeOrmConfigService],
})
export class AppModule {}
