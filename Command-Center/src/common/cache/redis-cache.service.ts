import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { promisify } from 'util';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
  serialize?: boolean;
}

@Injectable()
export class RedisCacheService implements OnModuleInit {
  private redis: Redis;
  private readonly logger = new Logger(RedisCacheService.name);
  private readonly defaultTTL = 3600; // 1 hour

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    const redisUrl = this.configService.get<string>('REDIS_URL');
    const redisHost = this.configService.get<string>('REDIS_HOST', 'localhost');
    const redisPort = this.configService.get<number>('REDIS_PORT', 6379);
    const redisPassword = this.configService.get<string>('REDIS_PASSWORD');

    this.redis = new Redis({
      ...(redisUrl ? { url: redisUrl } : { host: redisHost, port: redisPort }),
      password: redisPassword,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
    });

    this.redis.on('connect', () => {
      this.logger.log('Redis connected successfully');
    });

    this.redis.on('error', (err) => {
      this.logger.error('Redis connection error:', err);
    });

    this.redis.on('ready', () => {
      this.logger.log('Redis is ready');
    });

    try {
      await this.redis.connect();
    } catch (error) {
      this.logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const value = await this.redis.get(fullKey);
      
      if (value === null) {
        return null;
      }

      return options?.serialize !== false ? JSON.parse(value) : value;
    } catch (error) {
      this.logger.error(`Error getting cache key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const ttl = options?.ttl || this.defaultTTL;
      const serializedValue = options?.serialize !== false ? JSON.stringify(value) : String(value);
      
      await this.redis.setex(fullKey, ttl, serializedValue);
    } catch (error) {
      this.logger.error(`Error setting cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete key from cache
   */
  async del(key: string, options?: CacheOptions): Promise<void> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      await this.redis.del(fullKey);
    } catch (error) {
      this.logger.error(`Error deleting cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string, options?: CacheOptions): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.exists(fullKey);
      return result === 1;
    } catch (error) {
      this.logger.error(`Error checking cache key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get or set pattern - get value if exists, otherwise set and return new value
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    const fresh = await factory();
    await this.set(key, fresh, options);
    return fresh;
  }

  /**
   * Get multiple keys at once
   */
  async mget<T>(keys: string[], options?: CacheOptions): Promise<(T | null)[]> {
    try {
      const fullKeys = keys.map(key => this.buildKey(key, options?.prefix));
      const values = await this.redis.mget(...fullKeys);
      
      return values.map(value => 
        value === null ? null : 
        options?.serialize !== false ? JSON.parse(value) : value
      );
    } catch (error) {
      this.logger.error(`Error getting multiple cache keys:`, error);
      return keys.map(() => null);
    }
  }

  /**
   * Set multiple keys at once
   */
  async mset<T>(entries: Array<[string, T]>, options?: CacheOptions): Promise<void> {
    try {
      const pipeline = this.redis.pipeline();
      const ttl = options?.ttl || this.defaultTTL;
      
      entries.forEach(([key, value]) => {
        const fullKey = this.buildKey(key, options?.prefix);
        const serializedValue = options?.serialize !== false ? JSON.stringify(value) : String(value);
        pipeline.setex(fullKey, ttl, serializedValue);
      });
      
      await pipeline.exec();
    } catch (error) {
      this.logger.error(`Error setting multiple cache keys:`, error);
      throw error;
    }
  }

  /**
   * Delete keys by pattern
   */
  async delByPattern(pattern: string, options?: CacheOptions): Promise<number> {
    try {
      const fullPattern = this.buildKey(pattern, options?.prefix);
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await this.redis.del(...keys);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting keys by pattern ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Increment counter
   */
  async incr(key: string, options?: CacheOptions): Promise<number> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.incr(fullKey);
      
      if (options?.ttl) {
        await this.redis.expire(fullKey, options.ttl);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error incrementing cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Decrement counter
   */
  async decr(key: string, options?: CacheOptions): Promise<number> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.decr(fullKey);
      
      if (options?.ttl) {
        await this.redis.expire(fullKey, options.ttl);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error decrementing cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Add to set
   */
  async sadd(key: string, members: string[], options?: CacheOptions): Promise<number> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.sadd(fullKey, ...members);
      
      if (options?.ttl) {
        await this.redis.expire(fullKey, options.ttl);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error adding to set ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get set members
   */
  async smembers(key: string, options?: CacheOptions): Promise<string[]> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      return await this.redis.smembers(fullKey);
    } catch (error) {
      this.logger.error(`Error getting set members ${key}:`, error);
      return [];
    }
  }

  /**
   * Check if member exists in set
   */
  async sismember(key: string, member: string, options?: CacheOptions): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.sismember(fullKey, member);
      return result === 1;
    } catch (error) {
      this.logger.error(`Error checking set member ${key}:`, error);
      return false;
    }
  }

  /**
   * Add to sorted set
   */
  async zadd(key: string, score: number, member: string, options?: CacheOptions): Promise<number> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const result = await this.redis.zadd(fullKey, score, member);
      
      if (options?.ttl) {
        await this.redis.expire(fullKey, options.ttl);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error adding to sorted set ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get sorted set range
   */
  async zrange(key: string, start: number, stop: number, options?: CacheOptions): Promise<string[]> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      return await this.redis.zrange(fullKey, start, stop);
    } catch (error) {
      this.logger.error(`Error getting sorted set range ${key}:`, error);
      return [];
    }
  }

  /**
   * Flush all cache
   */
  async flushAll(): Promise<void> {
    try {
      await this.redis.flushall();
      this.logger.log('Cache flushed successfully');
    } catch (error) {
      this.logger.error('Error flushing cache:', error);
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        memory: info,
        keyspace: keyspace,
        connected: this.redis.status === 'ready',
      };
    } catch (error) {
      this.logger.error('Error getting cache stats:', error);
      return null;
    }
  }

  private buildKey(key: string, prefix?: string): string {
    const parts = ['ld-apps'];
    if (prefix) parts.push(prefix);
    parts.push(key);
    return parts.join(':');
  }

  /**
   * Close Redis connection
   */
  async onModuleDestroy() {
    try {
      await this.redis.quit();
      this.logger.log('Redis connection closed');
    } catch (error) {
      this.logger.error('Error closing Redis connection:', error);
    }
  }
}