import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { LoggerService } from '../logger/logger.service';
import { PrismaService } from '../database/prisma.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { firstValueFrom } from 'rxjs';

export interface SyncJob {
  id: string;
  type: 'full' | 'incremental' | 'real-time';
  source: 'external-api' | 'database' | 'file-system';
  destination: 'database' | 'cache' | 'external-api';
  entity: string;
  frequency: string;
  lastRun?: string;
  nextRun?: string;
  status: 'active' | 'paused' | 'error' | 'completed';
  config: Record<string, any>;
  metrics: {
    totalRecords: number;
    successfulRecords: number;
    failedRecords: number;
    duration: number;
    lastError?: string;
  };
}

export interface SyncResult {
  jobId: string;
  status: 'success' | 'partial' | 'failed';
  recordsProcessed: number;
  recordsSuccessful: number;
  recordsFailed: number;
  duration: number;
  errors: string[];
  startTime: string;
  endTime: string;
}

export interface DataMappingRule {
  sourceField: string;
  targetField: string;
  transform?: 'uppercase' | 'lowercase' | 'date' | 'number' | 'boolean' | 'custom';
  customTransform?: (value: any) => any;
  required?: boolean;
  defaultValue?: any;
}

@Injectable()
export class DataSynchronizationService {
  private readonly syncJobs: Map<string, SyncJob> = new Map();
  private readonly runningJobs: Set<string> = new Set();

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService,
    private readonly prisma: PrismaService
  ) {
    this.initializeSyncJobs();
  }

  private initializeSyncJobs() {
    // Training data sync jobs
    this.syncJobs.set('training-courses-sync', {
      id: 'training-courses-sync',
      type: 'incremental',
      source: 'external-api',
      destination: 'database',
      entity: 'courses',
      frequency: '0 */6 * * *', // Every 6 hours
      status: 'active',
      config: {
        endpoint: '/api/courses',
        mapping: [
          { sourceField: 'id', targetField: 'externalId' },
          { sourceField: 'title', targetField: 'title' },
          { sourceField: 'description', targetField: 'description' },
          { sourceField: 'category', targetField: 'category' },
          { sourceField: 'level', targetField: 'level' },
          { sourceField: 'duration', targetField: 'duration', transform: 'number' },
          { sourceField: 'updatedAt', targetField: 'lastSynced', transform: 'date' }
        ]
      },
      metrics: {
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        duration: 0
      }
    });

    this.syncJobs.set('learning-progress-sync', {
      id: 'learning-progress-sync',
      type: 'real-time',
      source: 'external-api',
      destination: 'database',
      entity: 'enrollments',
      frequency: '*/15 * * * *', // Every 15 minutes
      status: 'active',
      config: {
        endpoint: '/api/enrollments',
        mapping: [
          { sourceField: 'userId', targetField: 'userId' },
          { sourceField: 'courseId', targetField: 'courseId' },
          { sourceField: 'progress', targetField: 'progress', transform: 'number' },
          { sourceField: 'status', targetField: 'status' },
          { sourceField: 'completedAt', targetField: 'completedAt', transform: 'date' }
        ]
      },
      metrics: {
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        duration: 0
      }
    });

    // Vendor data sync jobs
    this.syncJobs.set('vendor-risk-sync', {
      id: 'vendor-risk-sync',
      type: 'incremental',
      source: 'external-api',
      destination: 'database',
      entity: 'vendor-risk',
      frequency: '0 8 * * *', // Daily at 8 AM
      status: 'active',
      config: {
        endpoint: '/api/vendor-risk',
        mapping: [
          { sourceField: 'vendorId', targetField: 'vendorId' },
          { sourceField: 'riskScore', targetField: 'riskScore', transform: 'number' },
          { sourceField: 'riskLevel', targetField: 'riskLevel' },
          { sourceField: 'lastAssessment', targetField: 'lastAssessment', transform: 'date' }
        ]
      },
      metrics: {
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        duration: 0
      }
    });

    this.syncJobs.set('vendor-financial-sync', {
      id: 'vendor-financial-sync',
      type: 'incremental',
      source: 'external-api',
      destination: 'database',
      entity: 'vendor-financial',
      frequency: '0 9 * * 1', // Weekly on Monday at 9 AM
      status: 'active',
      config: {
        endpoint: '/api/vendor-financial',
        mapping: [
          { sourceField: 'vendorId', targetField: 'vendorId' },
          { sourceField: 'creditScore', targetField: 'creditScore', transform: 'number' },
          { sourceField: 'revenue', targetField: 'revenue', transform: 'number' },
          { sourceField: 'profitMargin', targetField: 'profitMargin', transform: 'number' }
        ]
      },
      metrics: {
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        duration: 0
      }
    });

    // Wins data sync jobs
    this.syncJobs.set('wins-engagement-sync', {
      id: 'wins-engagement-sync',
      type: 'real-time',
      source: 'external-api',
      destination: 'database',
      entity: 'wins-engagement',
      frequency: '*/30 * * * *', // Every 30 minutes
      status: 'active',
      config: {
        endpoint: '/api/wins-engagement',
        mapping: [
          { sourceField: 'submissionId', targetField: 'submissionId' },
          { sourceField: 'views', targetField: 'views', transform: 'number' },
          { sourceField: 'reactions', targetField: 'reactions', transform: 'number' },
          { sourceField: 'comments', targetField: 'comments', transform: 'number' }
        ]
      },
      metrics: {
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        duration: 0
      }
    });
  }

  async runSyncJob(jobId: string): Promise<SyncResult> {
    const job = this.syncJobs.get(jobId);
    if (!job) {
      throw new HttpException('Sync job not found', HttpStatus.NOT_FOUND);
    }

    if (this.runningJobs.has(jobId)) {
      throw new HttpException('Sync job is already running', HttpStatus.CONFLICT);
    }

    this.runningJobs.add(jobId);
    const startTime = new Date().toISOString();
    
    try {
      this.logger.log(`Starting sync job: ${jobId}`);
      const result = await this.executeSyncJob(job);
      
      // Update job metrics
      job.metrics = {
        totalRecords: result.recordsProcessed,
        successfulRecords: result.recordsSuccessful,
        failedRecords: result.recordsFailed,
        duration: result.duration
      };
      job.lastRun = startTime;
      job.status = result.status === 'success' ? 'completed' : 'error';

      // Cache the result
      await this.redisService.set(`sync:result:${jobId}`, result, { ttl: 86400 });

      this.logger.log(`Sync job completed: ${jobId} - ${result.status}`);
      return result;
    } catch (error) {
      this.logger.error(`Sync job failed: ${jobId}`, error);
      job.status = 'error';
      job.metrics.lastError = error.message;
      
      return {
        jobId,
        status: 'failed',
        recordsProcessed: 0,
        recordsSuccessful: 0,
        recordsFailed: 0,
        duration: Date.now() - new Date(startTime).getTime(),
        errors: [error.message],
        startTime,
        endTime: new Date().toISOString()
      };
    } finally {
      this.runningJobs.delete(jobId);
    }
  }

  private async executeSyncJob(job: SyncJob): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      switch (job.entity) {
        case 'courses':
          ({ recordsProcessed, recordsSuccessful, recordsFailed } = 
            await this.syncCourses(job));
          break;
        case 'enrollments':
          ({ recordsProcessed, recordsSuccessful, recordsFailed } = 
            await this.syncEnrollments(job));
          break;
        case 'vendor-risk':
          ({ recordsProcessed, recordsSuccessful, recordsFailed } = 
            await this.syncVendorRisk(job));
          break;
        case 'vendor-financial':
          ({ recordsProcessed, recordsSuccessful, recordsFailed } = 
            await this.syncVendorFinancial(job));
          break;
        case 'wins-engagement':
          ({ recordsProcessed, recordsSuccessful, recordsFailed } = 
            await this.syncWinsEngagement(job));
          break;
        default:
          throw new Error(`Unknown entity type: ${job.entity}`);
      }
    } catch (error) {
      errors.push(error.message);
      recordsFailed = recordsProcessed;
      recordsSuccessful = 0;
    }

    const duration = Date.now() - startTime;
    const status = recordsFailed === 0 ? 'success' : 
                   (recordsSuccessful > 0 ? 'partial' : 'failed');

    return {
      jobId: job.id,
      status,
      recordsProcessed,
      recordsSuccessful,
      recordsFailed,
      duration,
      errors,
      startTime: new Date(startTime).toISOString(),
      endTime: new Date().toISOString()
    };
  }

  private async syncCourses(job: SyncJob): Promise<{ recordsProcessed: number; recordsSuccessful: number; recordsFailed: number }> {
    const endpoint = job.config.endpoint;
    const mapping = job.config.mapping;
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      // Get data from external API
      const response = await firstValueFrom(
        this.httpService.get(endpoint, {
          timeout: 30000,
          headers: {
            'Authorization': `Bearer ${this.configService.get('EXTERNAL_API_TOKEN')}`,
            'Content-Type': 'application/json'
          }
        })
      );

      const externalCourses = response.data.courses || response.data;
      recordsProcessed = externalCourses.length;

      // Process each course
      for (const externalCourse of externalCourses) {
        try {
          const mappedData = this.mapData(externalCourse, mapping);
          
          // Upsert course in database
          await this.prisma.course.upsert({
            where: { externalId: mappedData.externalId },
            create: {
              ...mappedData,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            update: {
              ...mappedData,
              updatedAt: new Date()
            }
          });

          recordsSuccessful++;
        } catch (error) {
          this.logger.error(`Error processing course ${externalCourse.id}:`, error);
          recordsFailed++;
        }
      }
    } catch (error) {
      this.logger.error('Error fetching courses from external API:', error);
      throw error;
    }

    return { recordsProcessed, recordsSuccessful, recordsFailed };
  }

  private async syncEnrollments(job: SyncJob): Promise<{ recordsProcessed: number; recordsSuccessful: number; recordsFailed: number }> {
    const endpoint = job.config.endpoint;
    const mapping = job.config.mapping;
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      // Get enrollment data from external API
      const response = await firstValueFrom(
        this.httpService.get(endpoint, {
          timeout: 30000,
          headers: {
            'Authorization': `Bearer ${this.configService.get('EXTERNAL_API_TOKEN')}`,
            'Content-Type': 'application/json'
          }
        })
      );

      const enrollments = response.data.enrollments || response.data;
      recordsProcessed = enrollments.length;

      // Process each enrollment
      for (const enrollment of enrollments) {
        try {
          const mappedData = this.mapData(enrollment, mapping);
          
          // Upsert enrollment in database
          await this.prisma.enrollment.upsert({
            where: { 
              userId_courseId: {
                userId: mappedData.userId,
                courseId: mappedData.courseId
              }
            },
            create: {
              ...mappedData,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            update: {
              ...mappedData,
              updatedAt: new Date()
            }
          });

          recordsSuccessful++;
        } catch (error) {
          this.logger.error(`Error processing enrollment ${enrollment.id}:`, error);
          recordsFailed++;
        }
      }
    } catch (error) {
      this.logger.error('Error fetching enrollments from external API:', error);
      throw error;
    }

    return { recordsProcessed, recordsSuccessful, recordsFailed };
  }

  private async syncVendorRisk(job: SyncJob): Promise<{ recordsProcessed: number; recordsSuccessful: number; recordsFailed: number }> {
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      // Get all vendors that need risk updates
      const vendors = await this.prisma.vendor.findMany({
        where: {
          OR: [
            { lastRiskAssessment: null },
            { lastRiskAssessment: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } }
          ]
        }
      });

      recordsProcessed = vendors.length;

      // Process each vendor
      for (const vendor of vendors) {
        try {
          // This would call the VendorIntelligenceService
          // For now, we'll simulate the data
          const riskData = {
            riskScore: Math.floor(Math.random() * 100),
            riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
            lastAssessment: new Date()
          };

          await this.prisma.vendor.update({
            where: { id: vendor.id },
            data: {
              riskScore: riskData.riskScore,
              riskLevel: riskData.riskLevel,
              lastRiskAssessment: riskData.lastAssessment,
              updatedAt: new Date()
            }
          });

          recordsSuccessful++;
        } catch (error) {
          this.logger.error(`Error processing vendor risk ${vendor.id}:`, error);
          recordsFailed++;
        }
      }
    } catch (error) {
      this.logger.error('Error syncing vendor risk data:', error);
      throw error;
    }

    return { recordsProcessed, recordsSuccessful, recordsFailed };
  }

  private async syncVendorFinancial(job: SyncJob): Promise<{ recordsProcessed: number; recordsSuccessful: number; recordsFailed: number }> {
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      // Get all vendors that need financial updates
      const vendors = await this.prisma.vendor.findMany({
        where: {
          OR: [
            { lastFinancialUpdate: null },
            { lastFinancialUpdate: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }
          ]
        }
      });

      recordsProcessed = vendors.length;

      // Process each vendor
      for (const vendor of vendors) {
        try {
          // This would call the VendorIntelligenceService
          // For now, we'll simulate the data
          const financialData = {
            creditScore: 700 + Math.floor(Math.random() * 150),
            revenue: 1000000 + Math.floor(Math.random() * 10000000),
            profitMargin: 0.05 + Math.random() * 0.15
          };

          await this.prisma.vendor.update({
            where: { id: vendor.id },
            data: {
              creditScore: financialData.creditScore,
              revenue: financialData.revenue,
              profitMargin: financialData.profitMargin,
              lastFinancialUpdate: new Date(),
              updatedAt: new Date()
            }
          });

          recordsSuccessful++;
        } catch (error) {
          this.logger.error(`Error processing vendor financial ${vendor.id}:`, error);
          recordsFailed++;
        }
      }
    } catch (error) {
      this.logger.error('Error syncing vendor financial data:', error);
      throw error;
    }

    return { recordsProcessed, recordsSuccessful, recordsFailed };
  }

  private async syncWinsEngagement(job: SyncJob): Promise<{ recordsProcessed: number; recordsSuccessful: number; recordsFailed: number }> {
    let recordsProcessed = 0;
    let recordsSuccessful = 0;
    let recordsFailed = 0;

    try {
      // Get all wins submissions that need engagement updates
      const submissions = await this.prisma.winsSubmission.findMany({
        where: {
          status: 'approved',
          lastEngagementUpdate: {
            lt: new Date(Date.now() - 30 * 60 * 1000) // Last 30 minutes
          }
        }
      });

      recordsProcessed = submissions.length;

      // Process each submission
      for (const submission of submissions) {
        try {
          // This would call external APIs to get engagement metrics
          // For now, we'll simulate the data
          const engagementData = {
            views: Math.floor(Math.random() * 100),
            reactions: Math.floor(Math.random() * 50),
            comments: Math.floor(Math.random() * 20)
          };

          await this.prisma.winsSubmission.update({
            where: { id: submission.id },
            data: {
              views: engagementData.views,
              reactions: engagementData.reactions,
              comments: engagementData.comments,
              lastEngagementUpdate: new Date(),
              updatedAt: new Date()
            }
          });

          recordsSuccessful++;
        } catch (error) {
          this.logger.error(`Error processing wins engagement ${submission.id}:`, error);
          recordsFailed++;
        }
      }
    } catch (error) {
      this.logger.error('Error syncing wins engagement data:', error);
      throw error;
    }

    return { recordsProcessed, recordsSuccessful, recordsFailed };
  }

  private mapData(sourceData: any, mapping: DataMappingRule[]): any {
    const mappedData: any = {};

    for (const rule of mapping) {
      let value = sourceData[rule.sourceField];

      // Handle missing required fields
      if (rule.required && (value === undefined || value === null)) {
        if (rule.defaultValue !== undefined) {
          value = rule.defaultValue;
        } else {
          throw new Error(`Required field ${rule.sourceField} is missing`);
        }
      }

      // Apply transformations
      if (value !== undefined && value !== null) {
        switch (rule.transform) {
          case 'uppercase':
            value = String(value).toUpperCase();
            break;
          case 'lowercase':
            value = String(value).toLowerCase();
            break;
          case 'date':
            value = new Date(value);
            break;
          case 'number':
            value = Number(value);
            break;
          case 'boolean':
            value = Boolean(value);
            break;
          case 'custom':
            if (rule.customTransform) {
              value = rule.customTransform(value);
            }
            break;
        }
      }

      mappedData[rule.targetField] = value;
    }

    return mappedData;
  }

  async getSyncJobs(): Promise<SyncJob[]> {
    return Array.from(this.syncJobs.values());
  }

  async getSyncJob(jobId: string): Promise<SyncJob | null> {
    return this.syncJobs.get(jobId) || null;
  }

  async updateSyncJob(jobId: string, updates: Partial<SyncJob>): Promise<SyncJob> {
    const job = this.syncJobs.get(jobId);
    if (!job) {
      throw new HttpException('Sync job not found', HttpStatus.NOT_FOUND);
    }

    const updatedJob = { ...job, ...updates };
    this.syncJobs.set(jobId, updatedJob);
    
    return updatedJob;
  }

  async getSyncJobResults(jobId: string): Promise<SyncResult[]> {
    const pattern = `sync:result:${jobId}*`;
    const keys = await this.redisService.keys(pattern);
    
    const results: SyncResult[] = [];
    for (const key of keys) {
      const result = await this.redisService.get<SyncResult>(key);
      if (result) {
        results.push(result);
      }
    }

    return results.sort((a, b) => 
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    );
  }

  // Scheduled sync jobs
  @Cron('0 */6 * * *') // Every 6 hours
  async runScheduledCoursesSync() {
    try {
      await this.runSyncJob('training-courses-sync');
    } catch (error) {
      this.logger.error('Scheduled courses sync failed:', error);
    }
  }

  @Cron('*/15 * * * *') // Every 15 minutes
  async runScheduledEnrollmentsSync() {
    try {
      await this.runSyncJob('learning-progress-sync');
    } catch (error) {
      this.logger.error('Scheduled enrollments sync failed:', error);
    }
  }

  @Cron('0 8 * * *') // Daily at 8 AM
  async runScheduledVendorRiskSync() {
    try {
      await this.runSyncJob('vendor-risk-sync');
    } catch (error) {
      this.logger.error('Scheduled vendor risk sync failed:', error);
    }
  }

  @Cron('0 9 * * 1') // Weekly on Monday at 9 AM
  async runScheduledVendorFinancialSync() {
    try {
      await this.runSyncJob('vendor-financial-sync');
    } catch (error) {
      this.logger.error('Scheduled vendor financial sync failed:', error);
    }
  }

  @Cron('*/30 * * * *') // Every 30 minutes
  async runScheduledWinsEngagementSync() {
    try {
      await this.runSyncJob('wins-engagement-sync');
    } catch (error) {
      this.logger.error('Scheduled wins engagement sync failed:', error);
    }
  }
}