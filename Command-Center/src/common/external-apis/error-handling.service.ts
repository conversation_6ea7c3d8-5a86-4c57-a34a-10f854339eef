import { Injectable } from '@nestjs/common';
import { LoggerService } from '../logger/logger.service';
import { RedisService } from '../cache/redis.service';

export interface ApiError {
  id: string;
  service: string;
  endpoint: string;
  method: string;
  statusCode?: number;
  error: string;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
  resolved: boolean;
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  exponentialBackoff: boolean;
  retryCondition?: (error: any) => boolean;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

@Injectable()
export class ErrorHandlingService {
  private readonly circuitBreakers: Map<string, CircuitBreaker> = new Map();
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    exponentialBackoff: true,
    retryCondition: (error: any) => {
      // Retry on network errors and 5xx responses
      return !error.response || error.response.status >= 500;
    }
  };

  constructor(
    private readonly logger: LoggerService,
    private readonly redisService: RedisService
  ) {}

  async handleApiError(
    service: string,
    endpoint: string,
    method: string,
    error: any,
    retryConfig: RetryConfig = this.defaultRetryConfig
  ): Promise<ApiError> {
    const errorId = `${service}-${endpoint}-${Date.now()}`;
    
    const apiError: ApiError = {
      id: errorId,
      service,
      endpoint,
      method,
      statusCode: error.response?.status,
      error: error.message || 'Unknown error',
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries: retryConfig.maxRetries,
      resolved: false,
      impact: this.determineImpact(service, error)
    };

    // Log the error
    this.logger.error(`API Error in ${service}:`, {
      errorId,
      endpoint,
      method,
      statusCode: error.response?.status,
      error: error.message,
      stack: error.stack
    });

    // Store error for tracking
    await this.redisService.set(
      `api-error:${errorId}`,
      apiError,
      { ttl: 86400 } // 24 hours
    );

    // Add to service error count
    await this.incrementServiceErrorCount(service);

    // Check circuit breaker
    const circuitBreaker = this.getCircuitBreaker(service);
    circuitBreaker.recordFailure();

    return apiError;
  }

  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    service: string,
    endpoint: string,
    retryConfig: RetryConfig = this.defaultRetryConfig
  ): Promise<T> {
    let lastError: any;
    let delay = retryConfig.initialDelay;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        // Check circuit breaker before attempt
        const circuitBreaker = this.getCircuitBreaker(service);
        if (circuitBreaker.isOpen()) {
          throw new Error(`Circuit breaker open for ${service}`);
        }

        const result = await operation();
        
        // Record success
        circuitBreaker.recordSuccess();
        
        if (attempt > 0) {
          this.logger.log(`Retry successful for ${service}:${endpoint} after ${attempt} attempts`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Check if we should retry
        if (attempt === retryConfig.maxRetries || 
            (retryConfig.retryCondition && !retryConfig.retryCondition(error))) {
          break;
        }

        // Log retry attempt
        this.logger.warn(`Retry attempt ${attempt + 1} failed for ${service}:${endpoint}`, {
          error: error.message,
          nextRetryIn: delay
        });

        // Wait before retry
        await this.delay(delay);

        // Calculate next delay
        if (retryConfig.exponentialBackoff) {
          delay = Math.min(delay * 2, retryConfig.maxDelay);
        }
      }
    }

    // All retries failed
    await this.handleApiError(service, endpoint, 'GET', lastError, retryConfig);
    throw lastError;
  }

  private getCircuitBreaker(service: string): CircuitBreaker {
    if (!this.circuitBreakers.has(service)) {
      const config: CircuitBreakerConfig = {
        failureThreshold: 5,
        resetTimeout: 60000, // 1 minute
        monitoringPeriod: 60000 // 1 minute
      };
      
      this.circuitBreakers.set(service, new CircuitBreaker(service, config, this.logger));
    }
    
    return this.circuitBreakers.get(service)!;
  }

  private async incrementServiceErrorCount(service: string): Promise<void> {
    const key = `service-errors:${service}`;
    const count = await this.redisService.incr(key);
    
    // Set expiry on first increment
    if (count === 1) {
      await this.redisService.expire(key, 3600); // 1 hour
    }

    // Alert if error count is high
    if (count > 10) {
      this.logger.error(`High error count for ${service}: ${count} errors in the last hour`);
    }
  }

  private determineImpact(service: string, error: any): 'low' | 'medium' | 'high' | 'critical' {
    const statusCode = error.response?.status;
    
    // Critical services
    if (['learning-platforms', 'vendor-intelligence', 'communication-platforms'].includes(service)) {
      if (statusCode >= 500 || !statusCode) {
        return 'critical';
      }
      if (statusCode >= 400) {
        return 'high';
      }
      return 'medium';
    }
    
    // Other services
    if (statusCode >= 500 || !statusCode) {
      return 'high';
    }
    if (statusCode >= 400) {
      return 'medium';
    }
    return 'low';
  }

  async getServiceErrors(service: string, limit: number = 50): Promise<ApiError[]> {
    const pattern = `api-error:${service}-*`;
    const keys = await this.redisService.keys(pattern);
    
    const errors: ApiError[] = [];
    for (const key of keys.slice(0, limit)) {
      const error = await this.redisService.get<ApiError>(key);
      if (error) {
        errors.push(error);
      }
    }

    return errors.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  async getServiceHealth(service: string): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    errorCount: number;
    circuitBreakerOpen: boolean;
    lastError?: ApiError;
  }> {
    const errorCount = await this.redisService.get<number>(`service-errors:${service}`) || 0;
    const circuitBreaker = this.getCircuitBreaker(service);
    const isCircuitOpen = circuitBreaker.isOpen();
    
    const recentErrors = await this.getServiceErrors(service, 1);
    const lastError = recentErrors[0];

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (isCircuitOpen || errorCount > 20) {
      status = 'unhealthy';
    } else if (errorCount > 5) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      errorCount,
      circuitBreakerOpen: isCircuitOpen,
      lastError
    };
  }

  async resolveError(errorId: string): Promise<void> {
    const error = await this.redisService.get<ApiError>(`api-error:${errorId}`);
    if (error) {
      error.resolved = true;
      await this.redisService.set(`api-error:${errorId}`, error);
      this.logger.log(`Error resolved: ${errorId}`);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private readonly service: string,
    private readonly config: CircuitBreakerConfig,
    private readonly logger: LoggerService
  ) {}

  recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.config.failureThreshold && this.state === 'closed') {
      this.state = 'open';
      this.logger.warn(`Circuit breaker opened for ${this.service}`);
    }
  }

  recordSuccess(): void {
    this.failures = 0;
    
    if (this.state === 'half-open') {
      this.state = 'closed';
      this.logger.log(`Circuit breaker closed for ${this.service}`);
    }
  }

  isOpen(): boolean {
    if (this.state === 'closed') {
      return false;
    }
    
    if (this.state === 'open') {
      // Check if reset timeout has passed
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = 'half-open';
        this.logger.log(`Circuit breaker half-open for ${this.service}`);
        return false;
      }
      return true;
    }
    
    // half-open state
    return false;
  }

  getState(): 'closed' | 'open' | 'half-open' {
    return this.state;
  }

  getFailureCount(): number {
    return this.failures;
  }
}