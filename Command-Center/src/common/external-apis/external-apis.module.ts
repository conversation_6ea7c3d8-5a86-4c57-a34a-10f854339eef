import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { LearningPlatformsService } from './learning-platforms.service';
import { VendorIntelligenceService } from './vendor-intelligence.service';
import { CommunicationPlatformsService } from './communication-platforms.service';
import { DataSynchronizationService } from './data-synchronization.service';
import { RedisModule } from '../cache/redis.module';
import { LoggerModule } from '../logger/logger.module';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
    ScheduleModule.forRoot(),
    RedisModule,
    LoggerModule,
    DatabaseModule,
  ],
  providers: [
    LearningPlatformsService,
    VendorIntelligenceService,
    CommunicationPlatformsService,
    DataSynchronizationService,
  ],
  exports: [
    LearningPlatformsService,
    VendorIntelligenceService,
    CommunicationPlatformsService,
    DataSynchronizationService,
  ],
})
export class ExternalApisModule {}