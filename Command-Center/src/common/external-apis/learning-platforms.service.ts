import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { LoggerService } from '../logger/logger.service';
import { firstValueFrom } from 'rxjs';

export interface LearningPlatformConfig {
  baseUrl: string;
  apiKey: string;
  timeout: number;
  retryCount: number;
}

export interface ExternalCourse {
  id: string;
  title: string;
  description: string;
  provider: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  price: number;
  currency: string;
  rating: number;
  enrollmentCount: number;
  skills: string[];
  prerequisites: string[];
  language: string;
  format: 'video' | 'interactive' | 'text' | 'mixed';
  certification: boolean;
  updatedAt: string;
}

export interface EnrollmentRequest {
  userId: string;
  courseId: string;
  startDate?: string;
  metadata?: Record<string, any>;
}

export interface EnrollmentResponse {
  enrollmentId: string;
  accessUrl: string;
  validUntil: string;
  status: 'enrolled' | 'pending' | 'failed';
}

@Injectable()
export class LearningPlatformsService {
  private readonly platforms: Map<string, LearningPlatformConfig>;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {
    this.platforms = new Map([
      ['coursera', {
        baseUrl: this.configService.get<string>('COURSERA_API_URL'),
        apiKey: this.configService.get<string>('COURSERA_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['udemy', {
        baseUrl: this.configService.get<string>('UDEMY_API_URL'),
        apiKey: this.configService.get<string>('UDEMY_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['pluralsight', {
        baseUrl: this.configService.get<string>('PLURALSIGHT_API_URL'),
        apiKey: this.configService.get<string>('PLURALSIGHT_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['linkedin', {
        baseUrl: this.configService.get<string>('LINKEDIN_LEARNING_API_URL'),
        apiKey: this.configService.get<string>('LINKEDIN_LEARNING_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }]
    ]);
  }

  async searchCourses(query: string, filters?: {
    category?: string;
    level?: string;
    maxPrice?: number;
    provider?: string;
    minRating?: number;
  }): Promise<ExternalCourse[]> {
    const cacheKey = `courses:search:${JSON.stringify({ query, filters })}`;
    
    try {
      // Check cache first
      const cached = await this.redisService.get<ExternalCourse[]>(cacheKey);
      if (cached) {
        this.logger.log(`Found ${cached.length} courses in cache for query: ${query}`);
        return cached;
      }

      const allCourses: ExternalCourse[] = [];
      
      // Search across all platforms
      for (const [platform, config] of this.platforms) {
        if (!config.baseUrl || !config.apiKey) {
          this.logger.warn(`Skipping ${platform} - missing configuration`);
          continue;
        }

        try {
          const platformCourses = await this.searchPlatformCourses(platform, query, filters);
          allCourses.push(...platformCourses);
          this.logger.log(`Found ${platformCourses.length} courses from ${platform}`);
        } catch (error) {
          this.logger.error(`Error searching ${platform}:`, error);
        }
      }

      // Sort by relevance (rating and enrollment count)
      allCourses.sort((a, b) => {
        const scoreA = (a.rating * 0.7) + (Math.log(a.enrollmentCount + 1) * 0.3);
        const scoreB = (b.rating * 0.7) + (Math.log(b.enrollmentCount + 1) * 0.3);
        return scoreB - scoreA;
      });

      // Cache results for 1 hour
      await this.redisService.set(cacheKey, allCourses, { ttl: 3600 });
      
      this.logger.log(`Total courses found: ${allCourses.length}`);
      return allCourses;
    } catch (error) {
      this.logger.error('Error searching courses:', error);
      throw new HttpException('Failed to search courses', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async searchPlatformCourses(
    platform: string, 
    query: string, 
    filters?: any
  ): Promise<ExternalCourse[]> {
    const config = this.platforms.get(platform);
    if (!config) return [];

    const searchUrl = this.buildSearchUrl(platform, query, filters);
    const headers = this.buildHeaders(platform, config);

    for (let attempt = 0; attempt < config.retryCount; attempt++) {
      try {
        const response = await firstValueFrom(
          this.httpService.get(searchUrl, {
            headers,
            timeout: config.timeout
          })
        );

        return this.transformPlatformResponse(platform, response.data);
      } catch (error) {
        this.logger.warn(`Attempt ${attempt + 1} failed for ${platform}:`, error.message);
        
        if (attempt === config.retryCount - 1) {
          throw error;
        }
        
        // Exponential backoff
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }

    return [];
  }

  private buildSearchUrl(platform: string, query: string, filters?: any): string {
    const config = this.platforms.get(platform);
    const baseUrl = config.baseUrl;
    const params = new URLSearchParams();

    switch (platform) {
      case 'coursera':
        params.append('q', query);
        params.append('limit', '50');
        if (filters?.category) params.append('domainIds', filters.category);
        if (filters?.level) params.append('difficultyLevel', filters.level);
        return `${baseUrl}/courses?${params.toString()}`;

      case 'udemy':
        params.append('search', query);
        params.append('page_size', '50');
        if (filters?.category) params.append('category', filters.category);
        if (filters?.level) params.append('instructional_level', filters.level);
        if (filters?.maxPrice) params.append('price', `0-${filters.maxPrice}`);
        return `${baseUrl}/courses?${params.toString()}`;

      case 'pluralsight':
        params.append('q', query);
        params.append('ps', '50');
        if (filters?.level) params.append('level', filters.level);
        return `${baseUrl}/courses?${params.toString()}`;

      case 'linkedin':
        params.append('q', query);
        params.append('count', '50');
        if (filters?.category) params.append('category', filters.category);
        return `${baseUrl}/courses?${params.toString()}`;

      default:
        return `${baseUrl}/courses?q=${encodeURIComponent(query)}`;
    }
  }

  private buildHeaders(platform: string, config: LearningPlatformConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'Luminar-L&D-Platform/1.0'
    };

    switch (platform) {
      case 'coursera':
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        break;
      case 'udemy':
        headers['Authorization'] = `Basic ${Buffer.from(`${config.apiKey}:`).toString('base64')}`;
        break;
      case 'pluralsight':
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        break;
      case 'linkedin':
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        break;
      default:
        headers['X-API-Key'] = config.apiKey;
    }

    return headers;
  }

  private transformPlatformResponse(platform: string, data: any): ExternalCourse[] {
    try {
      switch (platform) {
        case 'coursera':
          return this.transformCourseraResponse(data);
        case 'udemy':
          return this.transformUdemyResponse(data);
        case 'pluralsight':
          return this.transformPluralsightResponse(data);
        case 'linkedin':
          return this.transformLinkedInResponse(data);
        default:
          return [];
      }
    } catch (error) {
      this.logger.error(`Error transforming ${platform} response:`, error);
      return [];
    }
  }

  private transformCourseraResponse(data: any): ExternalCourse[] {
    if (!data.elements) return [];

    return data.elements.map(course => ({
      id: `coursera-${course.id}`,
      title: course.name,
      description: course.description || '',
      provider: 'Coursera',
      category: course.domainTypes?.[0] || 'General',
      level: this.mapCourseraLevel(course.difficultyLevel),
      duration: course.workload || 0,
      price: course.price || 0,
      currency: 'USD',
      rating: course.averageRating || 0,
      enrollmentCount: course.enrollmentCount || 0,
      skills: course.skills || [],
      prerequisites: course.prerequisites || [],
      language: course.primaryLanguages?.[0] || 'en',
      format: 'mixed',
      certification: course.certificates?.length > 0,
      updatedAt: new Date().toISOString()
    }));
  }

  private transformUdemyResponse(data: any): ExternalCourse[] {
    if (!data.results) return [];

    return data.results.map(course => ({
      id: `udemy-${course.id}`,
      title: course.title,
      description: course.headline || '',
      provider: 'Udemy',
      category: course.primary_category?.title || 'General',
      level: this.mapUdemyLevel(course.instructional_level),
      duration: this.convertUdemyDuration(course.content_info),
      price: course.price_detail?.amount || 0,
      currency: course.price_detail?.currency || 'USD',
      rating: course.rating || 0,
      enrollmentCount: course.num_subscribers || 0,
      skills: course.objective_items || [],
      prerequisites: course.prerequisites || [],
      language: course.locale?.title || 'English',
      format: 'video',
      certification: course.has_certificate,
      updatedAt: course.last_update_date || new Date().toISOString()
    }));
  }

  private transformPluralsightResponse(data: any): ExternalCourse[] {
    if (!data.courses) return [];

    return data.courses.map(course => ({
      id: `pluralsight-${course.id}`,
      title: course.title,
      description: course.description || '',
      provider: 'Pluralsight',
      category: course.level || 'General',
      level: course.level?.toLowerCase() || 'intermediate',
      duration: this.convertPluralsightDuration(course.duration),
      price: 0, // Pluralsight is subscription-based
      currency: 'USD',
      rating: course.rating || 0,
      enrollmentCount: 0,
      skills: course.tags || [],
      prerequisites: [],
      language: 'en',
      format: 'video',
      certification: false,
      updatedAt: course.updatedOn || new Date().toISOString()
    }));
  }

  private transformLinkedInResponse(data: any): ExternalCourse[] {
    if (!data.elements) return [];

    return data.elements.map(course => ({
      id: `linkedin-${course.id}`,
      title: course.title,
      description: course.description || '',
      provider: 'LinkedIn Learning',
      category: course.classification?.library || 'General',
      level: course.difficultyLevel?.toLowerCase() || 'intermediate',
      duration: this.convertLinkedInDuration(course.timeToComplete),
      price: 0, // LinkedIn Learning is subscription-based
      currency: 'USD',
      rating: course.rating || 0,
      enrollmentCount: 0,
      skills: course.skills || [],
      prerequisites: [],
      language: course.availableLocales?.[0] || 'en',
      format: 'video',
      certification: course.hasCertificate,
      updatedAt: course.lastModified || new Date().toISOString()
    }));
  }

  async enrollInCourse(request: EnrollmentRequest): Promise<EnrollmentResponse> {
    const [platform, courseId] = request.courseId.split('-');
    const config = this.platforms.get(platform);

    if (!config) {
      throw new HttpException('Platform not supported', HttpStatus.BAD_REQUEST);
    }

    try {
      const enrollmentData = await this.performEnrollment(platform, request);
      
      // Cache enrollment for quick access
      await this.redisService.set(
        `enrollment:${request.userId}:${request.courseId}`,
        enrollmentData,
        { ttl: 86400 } // 24 hours
      );

      return enrollmentData;
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      throw new HttpException('Failed to enroll in course', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async performEnrollment(platform: string, request: EnrollmentRequest): Promise<EnrollmentResponse> {
    // This would contain platform-specific enrollment logic
    // For now, return mock data
    return {
      enrollmentId: `enroll-${Date.now()}`,
      accessUrl: `https://${platform}.com/course/${request.courseId}`,
      validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'enrolled'
    };
  }

  private mapCourseraLevel(level: string): 'beginner' | 'intermediate' | 'advanced' {
    switch (level?.toLowerCase()) {
      case 'beginner': return 'beginner';
      case 'intermediate': return 'intermediate';
      case 'advanced': return 'advanced';
      default: return 'intermediate';
    }
  }

  private mapUdemyLevel(level: string): 'beginner' | 'intermediate' | 'advanced' {
    switch (level?.toLowerCase()) {
      case 'beginner level':
      case 'all levels':
        return 'beginner';
      case 'intermediate level':
        return 'intermediate';
      case 'expert level':
        return 'advanced';
      default:
        return 'intermediate';
    }
  }

  private convertUdemyDuration(contentInfo: any): number {
    if (!contentInfo) return 0;
    return Math.round(contentInfo.length_video_hours || 0);
  }

  private convertPluralsightDuration(duration: string): number {
    if (!duration) return 0;
    const match = duration.match(/(\d+)h\s*(\d+)m/);
    if (match) {
      return parseInt(match[1]) + Math.round(parseInt(match[2]) / 60);
    }
    return 0;
  }

  private convertLinkedInDuration(duration: any): number {
    if (!duration) return 0;
    return Math.round(duration.duration / 3600); // Convert seconds to hours
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}