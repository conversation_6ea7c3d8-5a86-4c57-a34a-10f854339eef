import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { LoggerService } from '../logger/logger.service';
import { firstValueFrom } from 'rxjs';

export interface CommunicationPlatformConfig {
  baseUrl: string;
  apiKey: string;
  timeout: number;
  retryCount: number;
}

export interface NotificationRequest {
  recipients: string[];
  channel: 'email' | 'slack' | 'teams' | 'sms';
  subject?: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  templateId?: string;
  variables?: Record<string, any>;
  attachments?: {
    filename: string;
    content: string;
    mimeType: string;
  }[];
}

export interface NotificationResponse {
  id: string;
  status: 'sent' | 'failed' | 'pending';
  deliveredTo: string[];
  failedTo: string[];
  timestamp: string;
  error?: string;
}

export interface WinsAnnouncement {
  title: string;
  description: string;
  author: string;
  category: string;
  achievements: string[];
  metrics?: {
    impact: string;
    value: number;
    beneficiaries: string[];
  };
  media?: {
    type: 'image' | 'video' | 'document';
    url: string;
  }[];
}

export interface TrainingNotification {
  type: 'enrollment' | 'completion' | 'reminder' | 'deadline';
  courseTitle: string;
  learner: string;
  deadline?: string;
  progress?: number;
  score?: number;
  nextSteps?: string[];
}

export interface VendorAlert {
  type: 'risk_change' | 'contract_expiry' | 'performance_issue' | 'compliance_violation';
  vendorName: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: string;
  actionRequired: string;
  deadline?: string;
}

@Injectable()
export class CommunicationPlatformsService {
  private readonly platforms: Map<string, CommunicationPlatformConfig>;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {
    this.platforms = new Map([
      ['sendgrid', {
        baseUrl: 'https://api.sendgrid.com/v3',
        apiKey: this.configService.get<string>('SENDGRID_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['slack', {
        baseUrl: 'https://slack.com/api',
        apiKey: this.configService.get<string>('SLACK_BOT_TOKEN'),
        timeout: 30000,
        retryCount: 3
      }],
      ['teams', {
        baseUrl: 'https://graph.microsoft.com/v1.0',
        apiKey: this.configService.get<string>('TEAMS_ACCESS_TOKEN'),
        timeout: 30000,
        retryCount: 3
      }],
      ['twilio', {
        baseUrl: 'https://api.twilio.com/2010-04-01',
        apiKey: this.configService.get<string>('TWILIO_AUTH_TOKEN'),
        timeout: 30000,
        retryCount: 3
      }]
    ]);
  }

  async sendNotification(request: NotificationRequest): Promise<NotificationResponse> {
    const config = this.platforms.get(request.channel === 'email' ? 'sendgrid' : 
                                     request.channel === 'slack' ? 'slack' :
                                     request.channel === 'teams' ? 'teams' : 'twilio');

    if (!config?.apiKey) {
      throw new HttpException(`${request.channel} not configured`, HttpStatus.BAD_REQUEST);
    }

    try {
      const response = await this.sendToChannel(request, config);
      
      // Cache notification for tracking
      await this.redisService.set(
        `notification:${response.id}`,
        response,
        { ttl: 86400 } // 24 hours
      );

      this.logger.log(`Notification sent via ${request.channel}: ${response.id}`);
      return response;
    } catch (error) {
      this.logger.error(`Failed to send notification via ${request.channel}:`, error);
      throw new HttpException('Failed to send notification', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async announceWin(announcement: WinsAnnouncement, channels: string[] = ['slack', 'teams']): Promise<NotificationResponse[]> {
    const message = this.formatWinAnnouncement(announcement);
    const responses: NotificationResponse[] = [];

    for (const channel of channels) {
      try {
        const response = await this.sendNotification({
          recipients: [this.getChannelRecipient(channel)],
          channel: channel as any,
          subject: `🎉 New Win: ${announcement.title}`,
          message,
          priority: 'medium',
          templateId: 'win-announcement'
        });
        responses.push(response);
      } catch (error) {
        this.logger.error(`Failed to announce win on ${channel}:`, error);
      }
    }

    return responses;
  }

  async sendTrainingNotification(notification: TrainingNotification, recipients: string[], channels: string[] = ['email']): Promise<NotificationResponse[]> {
    const message = this.formatTrainingNotification(notification);
    const subject = this.getTrainingSubject(notification);
    const responses: NotificationResponse[] = [];

    for (const channel of channels) {
      try {
        const response = await this.sendNotification({
          recipients,
          channel: channel as any,
          subject,
          message,
          priority: this.getTrainingPriority(notification),
          templateId: `training-${notification.type}`
        });
        responses.push(response);
      } catch (error) {
        this.logger.error(`Failed to send training notification on ${channel}:`, error);
      }
    }

    return responses;
  }

  async sendVendorAlert(alert: VendorAlert, recipients: string[], channels: string[] = ['email', 'slack']): Promise<NotificationResponse[]> {
    const message = this.formatVendorAlert(alert);
    const subject = this.getVendorAlertSubject(alert);
    const responses: NotificationResponse[] = [];

    for (const channel of channels) {
      try {
        const response = await this.sendNotification({
          recipients,
          channel: channel as any,
          subject,
          message,
          priority: this.getVendorAlertPriority(alert),
          templateId: `vendor-${alert.type}`
        });
        responses.push(response);
      } catch (error) {
        this.logger.error(`Failed to send vendor alert on ${channel}:`, error);
      }
    }

    return responses;
  }

  private async sendToChannel(request: NotificationRequest, config: CommunicationPlatformConfig): Promise<NotificationResponse> {
    switch (request.channel) {
      case 'email':
        return this.sendEmail(request, config);
      case 'slack':
        return this.sendSlackMessage(request, config);
      case 'teams':
        return this.sendTeamsMessage(request, config);
      case 'sms':
        return this.sendSMS(request, config);
      default:
        throw new HttpException('Unsupported channel', HttpStatus.BAD_REQUEST);
    }
  }

  private async sendEmail(request: NotificationRequest, config: CommunicationPlatformConfig): Promise<NotificationResponse> {
    const payload = {
      personalizations: [{
        to: request.recipients.map(email => ({ email })),
        subject: request.subject || 'Notification from Luminar L&D Platform'
      }],
      from: {
        email: this.configService.get<string>('FROM_EMAIL'),
        name: 'Luminar L&D Platform'
      },
      content: [{
        type: 'text/html',
        value: this.formatHtmlMessage(request.message, request.variables)
      }],
      ...(request.attachments && {
        attachments: request.attachments.map(att => ({
          filename: att.filename,
          content: att.content,
          type: att.mimeType
        }))
      })
    };

    const response = await firstValueFrom(
      this.httpService.post(`${config.baseUrl}/mail/send`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return {
      id: `email-${Date.now()}`,
      status: 'sent',
      deliveredTo: request.recipients,
      failedTo: [],
      timestamp: new Date().toISOString()
    };
  }

  private async sendSlackMessage(request: NotificationRequest, config: CommunicationPlatformConfig): Promise<NotificationResponse> {
    const payload = {
      channel: request.recipients[0] || '#general',
      text: request.message,
      blocks: this.formatSlackBlocks(request.message, request.variables),
      username: 'Luminar L&D Bot',
      icon_emoji: ':robot_face:'
    };

    const response = await firstValueFrom(
      this.httpService.post(`${config.baseUrl}/chat.postMessage`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return {
      id: response.data.ts || `slack-${Date.now()}`,
      status: response.data.ok ? 'sent' : 'failed',
      deliveredTo: response.data.ok ? request.recipients : [],
      failedTo: response.data.ok ? [] : request.recipients,
      timestamp: new Date().toISOString(),
      error: response.data.error
    };
  }

  private async sendTeamsMessage(request: NotificationRequest, config: CommunicationPlatformConfig): Promise<NotificationResponse> {
    const payload = {
      body: {
        contentType: 'html',
        content: this.formatTeamsMessage(request.message, request.variables)
      },
      subject: request.subject
    };

    const chatId = request.recipients[0] || 'general';
    
    const response = await firstValueFrom(
      this.httpService.post(`${config.baseUrl}/chats/${chatId}/messages`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return {
      id: response.data.id || `teams-${Date.now()}`,
      status: 'sent',
      deliveredTo: request.recipients,
      failedTo: [],
      timestamp: new Date().toISOString()
    };
  }

  private async sendSMS(request: NotificationRequest, config: CommunicationPlatformConfig): Promise<NotificationResponse> {
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const from = this.configService.get<string>('TWILIO_PHONE_NUMBER');
    
    const promises = request.recipients.map(async (to) => {
      const payload = new URLSearchParams({
        To: to,
        From: from,
        Body: request.message
      });

      try {
        const response = await firstValueFrom(
          this.httpService.post(`${config.baseUrl}/Accounts/${accountSid}/Messages.json`, payload, {
            headers: {
              'Authorization': `Basic ${Buffer.from(`${accountSid}:${config.apiKey}`).toString('base64')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            timeout: config.timeout
          })
        );
        return { recipient: to, success: true, sid: response.data.sid };
      } catch (error) {
        return { recipient: to, success: false, error: error.message };
      }
    });

    const results = await Promise.all(promises);
    const successful = results.filter(r => r.success).map(r => r.recipient);
    const failed = results.filter(r => !r.success).map(r => r.recipient);

    return {
      id: `sms-${Date.now()}`,
      status: failed.length === 0 ? 'sent' : (successful.length === 0 ? 'failed' : 'sent'),
      deliveredTo: successful,
      failedTo: failed,
      timestamp: new Date().toISOString(),
      error: failed.length > 0 ? `Failed to send to ${failed.length} recipients` : undefined
    };
  }

  private formatWinAnnouncement(announcement: WinsAnnouncement): string {
    return `
🎉 **${announcement.title}** 🎉

**Author:** ${announcement.author}
**Category:** ${announcement.category}

**Description:**
${announcement.description}

**Key Achievements:**
${announcement.achievements.map(achievement => `• ${achievement}`).join('\n')}

${announcement.metrics ? `
**Impact Metrics:**
• ${announcement.metrics.impact}
• Value: ${announcement.metrics.value}
• Beneficiaries: ${announcement.metrics.beneficiaries.join(', ')}
` : ''}

Great work team! 🚀
    `.trim();
  }

  private formatTrainingNotification(notification: TrainingNotification): string {
    switch (notification.type) {
      case 'enrollment':
        return `Hello ${notification.learner}! You've been enrolled in "${notification.courseTitle}". Start your learning journey today!`;
      
      case 'completion':
        return `Congratulations ${notification.learner}! You've completed "${notification.courseTitle}" with a score of ${notification.score}%. ${notification.nextSteps ? `Next steps: ${notification.nextSteps.join(', ')}` : ''}`;
      
      case 'reminder':
        return `Hi ${notification.learner}, don't forget to continue your progress in "${notification.courseTitle}". You're ${notification.progress}% complete!`;
      
      case 'deadline':
        return `⏰ Reminder: The deadline for "${notification.courseTitle}" is ${notification.deadline}. Complete your training to stay on track!`;
      
      default:
        return `Training update for "${notification.courseTitle}"`;
    }
  }

  private formatVendorAlert(alert: VendorAlert): string {
    const severityEmoji = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    };

    return `
${severityEmoji[alert.severity]} **Vendor Alert: ${alert.vendorName}**

**Type:** ${alert.type.replace('_', ' ').toUpperCase()}
**Severity:** ${alert.severity.toUpperCase()}

**Details:**
${alert.details}

**Action Required:**
${alert.actionRequired}

${alert.deadline ? `**Deadline:** ${alert.deadline}` : ''}

Please review and take appropriate action.
    `.trim();
  }

  private formatHtmlMessage(message: string, variables?: Record<string, any>): string {
    let formatted = message;
    
    if (variables) {
      Object.entries(variables).forEach(([key, value]) => {
        formatted = formatted.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      });
    }

    return `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #007bff; color: white; padding: 20px; text-align: center; }
    .content { padding: 20px; }
    .footer { background: #f8f9fa; padding: 10px; text-align: center; font-size: 12px; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Luminar L&D Platform</h1>
    </div>
    <div class="content">
      ${formatted.replace(/\n/g, '<br>')}
    </div>
    <div class="footer">
      <p>This is an automated message from Luminar L&D Platform</p>
    </div>
  </div>
</body>
</html>
    `;
  }

  private formatSlackBlocks(message: string, variables?: Record<string, any>): any[] {
    return [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: message
        }
      }
    ];
  }

  private formatTeamsMessage(message: string, variables?: Record<string, any>): string {
    return `<div style="font-family: Segoe UI, Arial, sans-serif;">${message.replace(/\n/g, '<br>')}</div>`;
  }

  private getChannelRecipient(channel: string): string {
    switch (channel) {
      case 'slack':
        return this.configService.get<string>('SLACK_CHANNEL_ID') || '#general';
      case 'teams':
        return this.configService.get<string>('TEAMS_CHANNEL_ID') || 'general';
      default:
        return '';
    }
  }

  private getTrainingSubject(notification: TrainingNotification): string {
    switch (notification.type) {
      case 'enrollment':
        return `Welcome to ${notification.courseTitle}`;
      case 'completion':
        return `Congratulations on completing ${notification.courseTitle}`;
      case 'reminder':
        return `Continue your progress in ${notification.courseTitle}`;
      case 'deadline':
        return `Deadline reminder for ${notification.courseTitle}`;
      default:
        return `Training update: ${notification.courseTitle}`;
    }
  }

  private getTrainingPriority(notification: TrainingNotification): 'low' | 'medium' | 'high' | 'urgent' {
    switch (notification.type) {
      case 'deadline':
        return 'high';
      case 'completion':
        return 'medium';
      case 'enrollment':
        return 'medium';
      case 'reminder':
        return 'low';
      default:
        return 'medium';
    }
  }

  private getVendorAlertSubject(alert: VendorAlert): string {
    return `${alert.severity.toUpperCase()} Vendor Alert: ${alert.vendorName}`;
  }

  private getVendorAlertPriority(alert: VendorAlert): 'low' | 'medium' | 'high' | 'urgent' {
    switch (alert.severity) {
      case 'critical':
        return 'urgent';
      case 'high':
        return 'high';
      case 'medium':
        return 'medium';
      case 'low':
        return 'low';
      default:
        return 'medium';
    }
  }
}