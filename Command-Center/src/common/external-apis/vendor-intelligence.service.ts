import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { LoggerService } from '../logger/logger.service';
import { firstValueFrom } from 'rxjs';

export interface VendorIntelligenceConfig {
  baseUrl: string;
  apiKey: string;
  timeout: number;
  retryCount: number;
}

export interface VendorRiskData {
  vendorId: string;
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: {
    financial: number;
    operational: number;
    compliance: number;
    security: number;
    reputation: number;
  };
  lastUpdated: string;
  alerts: VendorAlert[];
}

export interface VendorAlert {
  id: string;
  type: 'financial' | 'legal' | 'security' | 'compliance' | 'operational';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source: string;
  timestamp: string;
  resolved: boolean;
}

export interface VendorFinancialData {
  vendorId: string;
  creditScore: number;
  creditRating: string;
  revenue: number;
  netIncome: number;
  debtToEquity: number;
  liquidityRatio: number;
  profitMargin: number;
  yearOverYearGrowth: number;
  industryRank: number;
  lastUpdated: string;
}

export interface VendorComplianceData {
  vendorId: string;
  certifications: {
    name: string;
    issuer: string;
    validUntil: string;
    status: 'active' | 'expired' | 'pending';
  }[];
  audits: {
    type: string;
    date: string;
    result: 'passed' | 'failed' | 'conditional';
    score: number;
    findings: string[];
  }[];
  violations: {
    type: string;
    date: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    resolved: boolean;
  }[];
  lastUpdated: string;
}

export interface VendorMarketData {
  vendorId: string;
  marketPosition: number;
  marketShare: number;
  competitorAnalysis: {
    competitor: string;
    comparison: 'better' | 'similar' | 'worse';
    strengths: string[];
    weaknesses: string[];
  }[];
  industryTrends: {
    trend: string;
    impact: 'positive' | 'negative' | 'neutral';
    confidence: number;
  }[];
  lastUpdated: string;
}

@Injectable()
export class VendorIntelligenceService {
  private readonly providers: Map<string, VendorIntelligenceConfig>;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {
    this.providers = new Map([
      ['dun_bradstreet', {
        baseUrl: this.configService.get<string>('DUN_BRADSTREET_API_URL'),
        apiKey: this.configService.get<string>('DUN_BRADSTREET_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['experian', {
        baseUrl: this.configService.get<string>('EXPERIAN_API_URL'),
        apiKey: this.configService.get<string>('EXPERIAN_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['riskmethods', {
        baseUrl: this.configService.get<string>('RISKMETHODS_API_URL'),
        apiKey: this.configService.get<string>('RISKMETHODS_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }],
      ['suppliergateway', {
        baseUrl: this.configService.get<string>('SUPPLIER_GATEWAY_API_URL'),
        apiKey: this.configService.get<string>('SUPPLIER_GATEWAY_API_KEY'),
        timeout: 30000,
        retryCount: 3
      }]
    ]);
  }

  async getVendorRiskAssessment(vendorId: string, taxId?: string, companyName?: string): Promise<VendorRiskData> {
    const cacheKey = `vendor:risk:${vendorId}`;
    
    try {
      // Check cache first
      const cached = await this.redisService.get<VendorRiskData>(cacheKey);
      if (cached && this.isDataFresh(cached.lastUpdated, 24 * 60 * 60 * 1000)) {
        this.logger.log(`Found cached risk data for vendor: ${vendorId}`);
        return cached;
      }

      const riskData = await this.aggregateRiskData(vendorId, taxId, companyName);
      
      // Cache results for 24 hours
      await this.redisService.set(cacheKey, riskData, { ttl: 86400 });
      
      this.logger.log(`Risk assessment completed for vendor: ${vendorId}`);
      return riskData;
    } catch (error) {
      this.logger.error(`Error getting risk assessment for vendor ${vendorId}:`, error);
      throw new HttpException('Failed to get vendor risk assessment', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async aggregateRiskData(vendorId: string, taxId?: string, companyName?: string): Promise<VendorRiskData> {
    const riskAssessments = await Promise.allSettled([
      this.getDunBradstreetRisk(taxId, companyName),
      this.getExperianRisk(taxId, companyName),
      this.getRiskMethodsData(vendorId),
      this.getSupplierGatewayData(vendorId)
    ]);

    const validAssessments = riskAssessments
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<any>).value)
      .filter(Boolean);

    if (validAssessments.length === 0) {
      throw new HttpException('No risk data available from providers', HttpStatus.NOT_FOUND);
    }

    // Aggregate risk scores
    const aggregatedRisk = this.calculateAggregatedRisk(validAssessments);
    
    return {
      vendorId,
      riskScore: aggregatedRisk.score,
      riskLevel: aggregatedRisk.level,
      riskFactors: aggregatedRisk.factors,
      lastUpdated: new Date().toISOString(),
      alerts: aggregatedRisk.alerts
    };
  }

  private async getDunBradstreetRisk(taxId?: string, companyName?: string): Promise<any> {
    const config = this.providers.get('dun_bradstreet');
    if (!config?.baseUrl || !config?.apiKey) return null;

    const searchParams = new URLSearchParams();
    if (taxId) searchParams.append('duns', taxId);
    if (companyName) searchParams.append('name', companyName);

    const response = await firstValueFrom(
      this.httpService.get(`${config.baseUrl}/risk-assessment?${searchParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return this.transformDunBradstreetResponse(response.data);
  }

  private async getExperianRisk(taxId?: string, companyName?: string): Promise<any> {
    const config = this.providers.get('experian');
    if (!config?.baseUrl || !config?.apiKey) return null;

    const payload = {
      businessInfo: {
        ...(taxId && { taxId }),
        ...(companyName && { name: companyName })
      }
    };

    const response = await firstValueFrom(
      this.httpService.post(`${config.baseUrl}/business-risk`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return this.transformExperianResponse(response.data);
  }

  private async getRiskMethodsData(vendorId: string): Promise<any> {
    const config = this.providers.get('riskmethods');
    if (!config?.baseUrl || !config?.apiKey) return null;

    const response = await firstValueFrom(
      this.httpService.get(`${config.baseUrl}/suppliers/${vendorId}/risk`, {
        headers: {
          'X-API-Key': config.apiKey,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return this.transformRiskMethodsResponse(response.data);
  }

  private async getSupplierGatewayData(vendorId: string): Promise<any> {
    const config = this.providers.get('suppliergateway');
    if (!config?.baseUrl || !config?.apiKey) return null;

    const response = await firstValueFrom(
      this.httpService.get(`${config.baseUrl}/vendor/${vendorId}/assessment`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    );

    return this.transformSupplierGatewayResponse(response.data);
  }

  async getVendorFinancialData(vendorId: string, taxId?: string): Promise<VendorFinancialData> {
    const cacheKey = `vendor:financial:${vendorId}`;
    
    try {
      const cached = await this.redisService.get<VendorFinancialData>(cacheKey);
      if (cached && this.isDataFresh(cached.lastUpdated, 7 * 24 * 60 * 60 * 1000)) {
        return cached;
      }

      const financialData = await this.aggregateFinancialData(vendorId, taxId);
      
      // Cache for 7 days
      await this.redisService.set(cacheKey, financialData, { ttl: 604800 });
      
      return financialData;
    } catch (error) {
      this.logger.error(`Error getting financial data for vendor ${vendorId}:`, error);
      throw new HttpException('Failed to get vendor financial data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async aggregateFinancialData(vendorId: string, taxId?: string): Promise<VendorFinancialData> {
    // Mock implementation - in production, this would call financial data providers
    return {
      vendorId,
      creditScore: 750 + Math.floor(Math.random() * 100),
      creditRating: 'A',
      revenue: 10000000 + Math.floor(Math.random() * 50000000),
      netIncome: 1000000 + Math.floor(Math.random() * 5000000),
      debtToEquity: 0.3 + Math.random() * 0.4,
      liquidityRatio: 1.2 + Math.random() * 0.8,
      profitMargin: 0.1 + Math.random() * 0.15,
      yearOverYearGrowth: -0.05 + Math.random() * 0.2,
      industryRank: Math.floor(Math.random() * 100) + 1,
      lastUpdated: new Date().toISOString()
    };
  }

  async getVendorComplianceData(vendorId: string): Promise<VendorComplianceData> {
    const cacheKey = `vendor:compliance:${vendorId}`;
    
    try {
      const cached = await this.redisService.get<VendorComplianceData>(cacheKey);
      if (cached && this.isDataFresh(cached.lastUpdated, 30 * 24 * 60 * 60 * 1000)) {
        return cached;
      }

      const complianceData = await this.aggregateComplianceData(vendorId);
      
      // Cache for 30 days
      await this.redisService.set(cacheKey, complianceData, { ttl: 2592000 });
      
      return complianceData;
    } catch (error) {
      this.logger.error(`Error getting compliance data for vendor ${vendorId}:`, error);
      throw new HttpException('Failed to get vendor compliance data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async aggregateComplianceData(vendorId: string): Promise<VendorComplianceData> {
    // Mock implementation - in production, this would call compliance data providers
    return {
      vendorId,
      certifications: [
        {
          name: 'ISO 9001',
          issuer: 'International Organization for Standardization',
          validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active'
        },
        {
          name: 'SOC 2 Type II',
          issuer: 'AICPA',
          validUntil: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active'
        }
      ],
      audits: [
        {
          type: 'Quality Management',
          date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
          result: 'passed',
          score: 85,
          findings: ['Minor documentation gaps', 'Process improvements recommended']
        }
      ],
      violations: [],
      lastUpdated: new Date().toISOString()
    };
  }

  async getVendorMarketData(vendorId: string): Promise<VendorMarketData> {
    const cacheKey = `vendor:market:${vendorId}`;
    
    try {
      const cached = await this.redisService.get<VendorMarketData>(cacheKey);
      if (cached && this.isDataFresh(cached.lastUpdated, 7 * 24 * 60 * 60 * 1000)) {
        return cached;
      }

      const marketData = await this.aggregateMarketData(vendorId);
      
      // Cache for 7 days
      await this.redisService.set(cacheKey, marketData, { ttl: 604800 });
      
      return marketData;
    } catch (error) {
      this.logger.error(`Error getting market data for vendor ${vendorId}:`, error);
      throw new HttpException('Failed to get vendor market data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async aggregateMarketData(vendorId: string): Promise<VendorMarketData> {
    // Mock implementation - in production, this would call market intelligence providers
    return {
      vendorId,
      marketPosition: Math.floor(Math.random() * 50) + 1,
      marketShare: Math.random() * 0.1,
      competitorAnalysis: [
        {
          competitor: 'Competitor A',
          comparison: 'better',
          strengths: ['Lower cost', 'Better quality'],
          weaknesses: ['Limited capacity', 'Slower delivery']
        }
      ],
      industryTrends: [
        {
          trend: 'Digital transformation',
          impact: 'positive',
          confidence: 0.8
        },
        {
          trend: 'Supply chain consolidation',
          impact: 'negative',
          confidence: 0.6
        }
      ],
      lastUpdated: new Date().toISOString()
    };
  }

  private transformDunBradstreetResponse(data: any): any {
    // Transform D&B response to internal format
    return {
      provider: 'dun_bradstreet',
      score: data.riskScore || 50,
      factors: {
        financial: data.financialRisk || 30,
        operational: data.operationalRisk || 20,
        compliance: data.complianceRisk || 10,
        security: data.securityRisk || 15,
        reputation: data.reputationRisk || 25
      },
      alerts: data.alerts || []
    };
  }

  private transformExperianResponse(data: any): any {
    // Transform Experian response to internal format
    return {
      provider: 'experian',
      score: data.businessScore || 60,
      factors: {
        financial: data.creditRisk || 40,
        operational: data.businessRisk || 30,
        compliance: data.regulatoryRisk || 20,
        security: data.dataRisk || 10,
        reputation: data.marketRisk || 35
      },
      alerts: data.riskAlerts || []
    };
  }

  private transformRiskMethodsResponse(data: any): any {
    // Transform RiskMethods response to internal format
    return {
      provider: 'riskmethods',
      score: data.overallRisk || 45,
      factors: {
        financial: data.financialStability || 25,
        operational: data.operationalRisk || 35,
        compliance: data.complianceScore || 30,
        security: data.securityRisk || 20,
        reputation: data.reputationScore || 30
      },
      alerts: data.riskEvents || []
    };
  }

  private transformSupplierGatewayResponse(data: any): any {
    // Transform Supplier Gateway response to internal format
    return {
      provider: 'suppliergateway',
      score: data.riskScore || 55,
      factors: {
        financial: data.financialHealth || 35,
        operational: data.performanceRisk || 25,
        compliance: data.complianceRisk || 25,
        security: data.cybersecurityRisk || 30,
        reputation: data.reputationRisk || 20
      },
      alerts: data.alerts || []
    };
  }

  private calculateAggregatedRisk(assessments: any[]): any {
    const totalWeight = assessments.length;
    
    const aggregatedFactors = {
      financial: 0,
      operational: 0,
      compliance: 0,
      security: 0,
      reputation: 0
    };

    let totalScore = 0;
    const allAlerts: VendorAlert[] = [];

    assessments.forEach(assessment => {
      totalScore += assessment.score;
      
      Object.keys(aggregatedFactors).forEach(factor => {
        aggregatedFactors[factor] += assessment.factors[factor] || 0;
      });
      
      allAlerts.push(...(assessment.alerts || []));
    });

    const finalScore = Math.round(totalScore / totalWeight);
    
    Object.keys(aggregatedFactors).forEach(factor => {
      aggregatedFactors[factor] = Math.round(aggregatedFactors[factor] / totalWeight);
    });

    return {
      score: finalScore,
      level: this.calculateRiskLevel(finalScore),
      factors: aggregatedFactors,
      alerts: allAlerts
    };
  }

  private calculateRiskLevel(score: number): 'low' | 'medium' | 'high' | 'critical' {
    if (score >= 80) return 'critical';
    if (score >= 60) return 'high';
    if (score >= 40) return 'medium';
    return 'low';
  }

  private isDataFresh(lastUpdated: string, maxAge: number): boolean {
    const updateTime = new Date(lastUpdated).getTime();
    const now = Date.now();
    return (now - updateTime) < maxAge;
  }
}