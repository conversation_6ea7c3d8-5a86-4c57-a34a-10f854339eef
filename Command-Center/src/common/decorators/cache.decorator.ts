import { SetMetadata } from '@nestjs/common';
import { createHash } from 'crypto';

export const CACHE_KEY_METADATA = 'cache_key';
export const CACHE_TTL_METADATA = 'cache_ttl';
export const CACHE_PREFIX_METADATA = 'cache_prefix';

export interface CacheDecoratorOptions {
  /**
   * Cache key template. Use {param} for parameter substitution
   * Example: 'user:{id}' or 'posts:{userId}:{page}'
   */
  key: string;
  
  /**
   * Time to live in seconds
   */
  ttl?: number;
  
  /**
   * Cache key prefix
   */
  prefix?: string;
  
  /**
   * Condition function to determine if response should be cached
   */
  condition?: (result: any) => boolean;
  
  /**
   * Use parameter hash for dynamic cache keys
   */
  useHash?: boolean;
}

/**
 * Cache decorator for methods
 * Automatically caches method results based on parameters
 */
export function Cache(options: CacheDecoratorOptions): MethodDecorator {
  return (target: any, propertyName: string | symbol, descriptor: PropertyDescriptor) => {
    SetMetadata(CACHE_KEY_METADATA, options.key)(target, propertyName, descriptor);
    SetMetadata(CACHE_TTL_METADATA, options.ttl)(target, propertyName, descriptor);
    SetMetadata(CACHE_PREFIX_METADATA, options.prefix)(target, propertyName, descriptor);

    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const cacheService = this.cacheService || this.redis;
      
      if (!cacheService) {
        // If no cache service available, just call original method
        return originalMethod.apply(this, args);
      }

      // Generate cache key
      const cacheKey = generateCacheKey(options.key, args, options.useHash);
      
      // Try to get from cache
      const cached = await cacheService.get(cacheKey, {
        ttl: options.ttl,
        prefix: options.prefix,
      });
      
      if (cached !== null) {
        return cached;
      }
      
      // Execute original method
      const result = await originalMethod.apply(this, args);
      
      // Check condition before caching
      if (options.condition && !options.condition(result)) {
        return result;
      }
      
      // Cache result
      await cacheService.set(cacheKey, result, {
        ttl: options.ttl,
        prefix: options.prefix,
      });
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * Cache invalidation decorator
 * Invalidates cache entries when method is called
 */
export function CacheInvalidate(options: {
  keys: string[];
  patterns?: string[];
  prefix?: string;
}): MethodDecorator {
  return (target: any, propertyName: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const result = await originalMethod.apply(this, args);
      
      const cacheService = this.cacheService || this.redis;
      if (!cacheService) {
        return result;
      }

      // Invalidate specific keys
      for (const keyTemplate of options.keys) {
        const key = generateCacheKey(keyTemplate, args);
        await cacheService.del(key, { prefix: options.prefix });
      }
      
      // Invalidate by patterns
      if (options.patterns) {
        for (const pattern of options.patterns) {
          await cacheService.delByPattern(pattern, { prefix: options.prefix });
        }
      }
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * Cache evict decorator
 * Evicts all cache entries with the specified prefix
 */
export function CacheEvict(prefix?: string): MethodDecorator {
  return (target: any, propertyName: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const result = await originalMethod.apply(this, args);
      
      const cacheService = this.cacheService || this.redis;
      if (!cacheService) {
        return result;
      }

      await cacheService.delByPattern('*', { prefix });
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * Generate cache key from template and arguments
 */
function generateCacheKey(template: string, args: any[], useHash: boolean = false): string {
  let key = template;
  
  // Simple parameter substitution
  args.forEach((arg, index) => {
    if (typeof arg === 'object' && arg !== null) {
      // For objects, substitute by property name
      Object.keys(arg).forEach(prop => {
        key = key.replace(new RegExp(`{${prop}}`, 'g'), String(arg[prop]));
      });
    } else {
      // For primitives, substitute by index
      key = key.replace(new RegExp(`{${index}}`, 'g'), String(arg));
    }
  });
  
  // If useHash is enabled, hash the remaining parameters
  if (useHash) {
    const hash = createHash('md5')
      .update(JSON.stringify(args))
      .digest('hex')
      .substring(0, 8);
    key = key.includes('{') ? key.replace(/{[^}]+}/g, hash) : `${key}:${hash}`;
  }
  
  return key;
}

/**
 * Cache configuration for different data types
 */
export const CacheConfig = {
  USER: {
    key: 'user:{id}',
    ttl: 3600, // 1 hour
    prefix: 'users',
  },
  TRAINING: {
    key: 'training:{id}',
    ttl: 1800, // 30 minutes
    prefix: 'training',
  },
  VENDOR: {
    key: 'vendor:{id}',
    ttl: 3600, // 1 hour
    prefix: 'vendors',
  },
  WINS: {
    key: 'wins:{id}',
    ttl: 900, // 15 minutes
    prefix: 'wins',
  },
  ANALYTICS: {
    key: 'analytics:{type}:{period}',
    ttl: 7200, // 2 hours
    prefix: 'analytics',
  },
  SEARCH: {
    key: 'search:{query}',
    ttl: 600, // 10 minutes
    prefix: 'search',
    useHash: true,
  },
};

/**
 * Common cache decorators
 */
export const CacheUser = (id: string) => Cache({
  key: `user:${id}`,
  ttl: 3600,
  prefix: 'users',
});

export const CacheTraining = (id: string) => Cache({
  key: `training:${id}`,
  ttl: 1800,
  prefix: 'training',
});

export const CacheVendor = (id: string) => Cache({
  key: `vendor:${id}`,
  ttl: 3600,
  prefix: 'vendors',
});

export const CacheWins = (id: string) => Cache({
  key: `wins:${id}`,
  ttl: 900,
  prefix: 'wins',
});

export const CacheAnalytics = (type: string, period: string) => Cache({
  key: `analytics:${type}:${period}`,
  ttl: 7200,
  prefix: 'analytics',
});

export const CacheSearch = Cache({
  key: 'search:{0}',
  ttl: 600,
  prefix: 'search',
  useHash: true,
  condition: (result) => result && result.length > 0,
});