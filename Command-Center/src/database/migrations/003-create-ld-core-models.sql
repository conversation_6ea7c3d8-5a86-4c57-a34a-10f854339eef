-- =============================================================================
-- L&D (Learning & Development) Database Schema
-- Migration 003: Create Core L&D Models
-- =============================================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- CORE USER MANAGEMENT TABLES
-- =============================================================================

-- Departments table
CREATE TABLE IF NOT EXISTS departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    manager VARCHAR(255) NOT NULL,
    employee_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Roles table
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    department_id UUID NOT NULL,
    level VARCHAR(50) NOT NULL CHECK (level IN ('entry', 'mid', 'senior', 'lead', 'executive')),
    required_skills TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);

-- Users table (extending existing user system)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    department_id UUID,
    role_id UUID,
    manager_id UUID,
    join_date TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active',
    profile_picture TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL
);

-- =============================================================================
-- TRAINING NEED ANALYSIS TABLES
-- =============================================================================

-- Skills table
CREATE TABLE IF NOT EXISTS skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    levels JSONB, -- Array of SkillLevel objects
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User skills table
CREATE TABLE IF NOT EXISTS user_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    skill_id UUID NOT NULL,
    current_level INTEGER NOT NULL,
    assessed_date TIMESTAMP NOT NULL,
    source VARCHAR(50) NOT NULL CHECK (source IN ('self-assessment', 'manager-review', 'peer-review', '360-review')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    UNIQUE(user_id, skill_id)
);

-- Assessments table
CREATE TABLE IF NOT EXISTS assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    department_id UUID,
    role_id UUID,
    created_by UUID NOT NULL,
    created_date TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'archived')),
    skills_assessed TEXT[],
    due_date TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Assessment responses table
CREATE TABLE IF NOT EXISTS assessment_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assessment_id UUID NOT NULL,
    user_id UUID NOT NULL,
    completed_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'not-started' CHECK (status IN ('not-started', 'in-progress', 'completed')),
    skill_ratings JSONB, -- Array of SkillRating objects
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(assessment_id, user_id)
);

-- Skill gaps table
CREATE TABLE IF NOT EXISTS skill_gaps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    skill_id UUID NOT NULL,
    current_level INTEGER NOT NULL,
    required_level INTEGER NOT NULL,
    gap INTEGER NOT NULL,
    priority VARCHAR(50) NOT NULL CHECK (priority IN ('critical', 'high', 'medium', 'low')),
    impact VARCHAR(50) NOT NULL CHECK (impact IN ('high', 'medium', 'low')),
    urgency VARCHAR(50) NOT NULL CHECK (urgency IN ('immediate', 'short-term', 'long-term')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    UNIQUE(user_id, skill_id)
);

-- Training courses table
CREATE TABLE IF NOT EXISTS training_courses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    provider VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('internal', 'external', 'online', 'workshop', 'certification')),
    duration INTEGER NOT NULL, -- in hours
    cost DECIMAL(10,2) NOT NULL,
    capacity INTEGER,
    prerequisites TEXT[],
    difficulty VARCHAR(50) NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    rating DECIMAL(2,1) DEFAULT 0,
    enrollments INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'coming-soon')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Training course skills junction table
CREATE TABLE IF NOT EXISTS training_course_skills (
    training_course_id UUID NOT NULL,
    skill_id UUID NOT NULL,
    PRIMARY KEY (training_course_id, skill_id),
    FOREIGN KEY (training_course_id) REFERENCES training_courses(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
);

-- Learning paths table
CREATE TABLE IF NOT EXISTS learning_paths (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    role_id UUID,
    estimated_duration INTEGER NOT NULL, -- in hours
    difficulty VARCHAR(50) NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    completion_criteria TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL
);

-- Learning path courses junction table
CREATE TABLE IF NOT EXISTS learning_path_courses (
    learning_path_id UUID NOT NULL,
    training_course_id UUID NOT NULL,
    sequence_order INTEGER NOT NULL,
    PRIMARY KEY (learning_path_id, training_course_id),
    FOREIGN KEY (learning_path_id) REFERENCES learning_paths(id) ON DELETE CASCADE,
    FOREIGN KEY (training_course_id) REFERENCES training_courses(id) ON DELETE CASCADE
);

-- Learning path skills junction table
CREATE TABLE IF NOT EXISTS learning_path_skills (
    learning_path_id UUID NOT NULL,
    skill_id UUID NOT NULL,
    PRIMARY KEY (learning_path_id, skill_id),
    FOREIGN KEY (learning_path_id) REFERENCES learning_paths(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
);

-- Training recommendations table
CREATE TABLE IF NOT EXISTS training_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    course_id UUID NOT NULL,
    skill_gap_id UUID NOT NULL,
    priority INTEGER NOT NULL,
    reasoning TEXT NOT NULL,
    expected_impact VARCHAR(50) NOT NULL CHECK (expected_impact IN ('high', 'medium', 'low')),
    estimated_completion TIMESTAMP NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES training_courses(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_gap_id) REFERENCES skill_gaps(id) ON DELETE CASCADE
);

-- Training enrollments table
CREATE TABLE IF NOT EXISTS training_enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    course_id UUID NOT NULL,
    enrolled_date TIMESTAMP DEFAULT NOW(),
    start_date TIMESTAMP,
    completed_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'enrolled' CHECK (status IN ('enrolled', 'in-progress', 'completed', 'dropped', 'failed')),
    progress DECIMAL(5,2) DEFAULT 0,
    score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES training_courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id)
);

-- Learning activities table
CREATE TABLE IF NOT EXISTS learning_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(100) NOT NULL CHECK (type IN ('assessment-completed', 'training-started', 'training-completed', 'skill-updated', 'gap-identified')),
    user_id UUID NOT NULL,
    description TEXT NOT NULL,
    date TIMESTAMP DEFAULT NOW(),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Budget allocations table
CREATE TABLE IF NOT EXISTS budget_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    department_id UUID NOT NULL,
    year INTEGER NOT NULL,
    quarter INTEGER,
    total_budget DECIMAL(12,2) NOT NULL,
    allocated_budget DECIMAL(12,2) NOT NULL,
    spent_budget DECIMAL(12,2) NOT NULL,
    training_categories JSONB, -- Array of BudgetCategory objects
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE(department_id, year, quarter)
);

-- =============================================================================
-- VENDOR MANAGEMENT TABLES
-- =============================================================================

-- Vendors table
CREATE TABLE IF NOT EXISTS vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NOT NULL,
    phone_numbers JSONB, -- Array of PhoneNumber objects
    email VARCHAR(255) NOT NULL,
    website VARCHAR(255),
    category VARCHAR(50) NOT NULL CHECK (category IN ('Training', 'Assessment', 'Certification', 'Content', 'Consulting')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    rating DECIMAL(2,1) DEFAULT 0,
    certifications TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Proposals table
CREATE TABLE IF NOT EXISTS proposals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    costs JSONB, -- Array of CostItem objects
    total_cost DECIMAL(12,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'negotiation', 'approved', 'rejected')),
    attachments TEXT[],
    valid_until TIMESTAMP NOT NULL,
    submitted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE CASCADE
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL,
    proposal_id UUID,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    reviewer_name VARCHAR(255) NOT NULL,
    reviewer_role VARCHAR(255) NOT NULL,
    comment TEXT NOT NULL,
    strengths TEXT[],
    improvements TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE CASCADE,
    FOREIGN KEY (proposal_id) REFERENCES proposals(id) ON DELETE CASCADE
);

-- =============================================================================
-- WINS OF WEEK TABLES
-- =============================================================================

-- Weekly submissions table
CREATE TABLE IF NOT EXISTS weekly_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    week_start_date DATE NOT NULL,
    week_end_date DATE NOT NULL,
    submission_date TIMESTAMP DEFAULT NOW(),
    achievements JSONB, -- Array of Achievement objects
    recognitions JSONB, -- Array of Recognition objects
    cost_initiatives JSONB, -- Array of CostInitiative objects
    training_ideas JSONB, -- Array of TrainingIdea objects
    progress_updates JSONB, -- Array of ProgressUpdate objects
    weekly_highlight TEXT,
    challenges TEXT,
    next_week_focus TEXT,
    attachments JSONB, -- Array of Attachment objects
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'reviewed')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, week_start_date)
);

-- Competency frameworks table
CREATE TABLE IF NOT EXISTS competency_frameworks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    skills JSONB, -- Array of skill IDs
    roles JSONB, -- Array of role IDs
    assessment_criteria TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Department indexes
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);

-- Role indexes
CREATE INDEX IF NOT EXISTS idx_roles_department_id ON roles(department_id);
CREATE INDEX IF NOT EXISTS idx_roles_level ON roles(level);

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_users_manager_id ON users(manager_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Skill indexes
CREATE INDEX IF NOT EXISTS idx_skills_category ON skills(category);
CREATE INDEX IF NOT EXISTS idx_skills_name ON skills(name);

-- User skill indexes
CREATE INDEX IF NOT EXISTS idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skills_skill_id ON user_skills(skill_id);

-- Assessment indexes
CREATE INDEX IF NOT EXISTS idx_assessments_department_id ON assessments(department_id);
CREATE INDEX IF NOT EXISTS idx_assessments_role_id ON assessments(role_id);
CREATE INDEX IF NOT EXISTS idx_assessments_status ON assessments(status);

-- Assessment response indexes
CREATE INDEX IF NOT EXISTS idx_assessment_responses_assessment_id ON assessment_responses(assessment_id);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_user_id ON assessment_responses(user_id);

-- Skill gap indexes
CREATE INDEX IF NOT EXISTS idx_skill_gaps_user_id ON skill_gaps(user_id);
CREATE INDEX IF NOT EXISTS idx_skill_gaps_skill_id ON skill_gaps(skill_id);
CREATE INDEX IF NOT EXISTS idx_skill_gaps_priority ON skill_gaps(priority);

-- Training course indexes
CREATE INDEX IF NOT EXISTS idx_training_courses_provider ON training_courses(provider);
CREATE INDEX IF NOT EXISTS idx_training_courses_type ON training_courses(type);
CREATE INDEX IF NOT EXISTS idx_training_courses_status ON training_courses(status);

-- Learning path indexes
CREATE INDEX IF NOT EXISTS idx_learning_paths_role_id ON learning_paths(role_id);

-- Training recommendation indexes
CREATE INDEX IF NOT EXISTS idx_training_recommendations_user_id ON training_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_training_recommendations_course_id ON training_recommendations(course_id);
CREATE INDEX IF NOT EXISTS idx_training_recommendations_skill_gap_id ON training_recommendations(skill_gap_id);

-- Training enrollment indexes
CREATE INDEX IF NOT EXISTS idx_training_enrollments_user_id ON training_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_training_enrollments_course_id ON training_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_training_enrollments_status ON training_enrollments(status);

-- Learning activity indexes
CREATE INDEX IF NOT EXISTS idx_learning_activities_user_id ON learning_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_activities_type ON learning_activities(type);
CREATE INDEX IF NOT EXISTS idx_learning_activities_date ON learning_activities(date);

-- Budget allocation indexes
CREATE INDEX IF NOT EXISTS idx_budget_allocations_department_id ON budget_allocations(department_id);
CREATE INDEX IF NOT EXISTS idx_budget_allocations_year ON budget_allocations(year);

-- Vendor indexes
CREATE INDEX IF NOT EXISTS idx_vendors_category ON vendors(category);
CREATE INDEX IF NOT EXISTS idx_vendors_status ON vendors(status);
CREATE INDEX IF NOT EXISTS idx_vendors_rating ON vendors(rating);

-- Proposal indexes
CREATE INDEX IF NOT EXISTS idx_proposals_vendor_id ON proposals(vendor_id);
CREATE INDEX IF NOT EXISTS idx_proposals_status ON proposals(status);
CREATE INDEX IF NOT EXISTS idx_proposals_total_cost ON proposals(total_cost);

-- Review indexes
CREATE INDEX IF NOT EXISTS idx_reviews_vendor_id ON reviews(vendor_id);
CREATE INDEX IF NOT EXISTS idx_reviews_proposal_id ON reviews(proposal_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);

-- Weekly submission indexes
CREATE INDEX IF NOT EXISTS idx_weekly_submissions_user_id ON weekly_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_weekly_submissions_week_start_date ON weekly_submissions(week_start_date);
CREATE INDEX IF NOT EXISTS idx_weekly_submissions_status ON weekly_submissions(status);

-- Competency framework indexes
CREATE INDEX IF NOT EXISTS idx_competency_frameworks_name ON competency_frameworks(name);

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

-- Create updated_at triggers for all tables
CREATE TRIGGER update_departments_updated_at
    BEFORE UPDATE ON departments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON roles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_skills_updated_at
    BEFORE UPDATE ON skills
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_skills_updated_at
    BEFORE UPDATE ON user_skills
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessments_updated_at
    BEFORE UPDATE ON assessments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_responses_updated_at
    BEFORE UPDATE ON assessment_responses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_skill_gaps_updated_at
    BEFORE UPDATE ON skill_gaps
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_courses_updated_at
    BEFORE UPDATE ON training_courses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_paths_updated_at
    BEFORE UPDATE ON learning_paths
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_recommendations_updated_at
    BEFORE UPDATE ON training_recommendations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_enrollments_updated_at
    BEFORE UPDATE ON training_enrollments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budget_allocations_updated_at
    BEFORE UPDATE ON budget_allocations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vendors_updated_at
    BEFORE UPDATE ON vendors
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proposals_updated_at
    BEFORE UPDATE ON proposals
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_weekly_submissions_updated_at
    BEFORE UPDATE ON weekly_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_competency_frameworks_updated_at
    BEFORE UPDATE ON competency_frameworks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA SEEDING
-- =============================================================================

-- Insert sample departments
INSERT INTO departments (name, manager, employee_count) VALUES
    ('Engineering', 'John Smith', 25),
    ('Marketing', 'Sarah Johnson', 15),
    ('Sales', 'Michael Brown', 20),
    ('HR', 'Emily Davis', 8),
    ('Finance', 'Robert Wilson', 12)
ON CONFLICT DO NOTHING;

-- Insert sample skills
INSERT INTO skills (name, category, description, levels) VALUES
    ('JavaScript', 'Technical', 'JavaScript programming language', '[{"level": 1, "name": "Beginner", "description": "Basic syntax", "competencies": ["Variables", "Functions"]}, {"level": 2, "name": "Intermediate", "description": "Advanced concepts", "competencies": ["Async/Await", "Closures"]}]'),
    ('Leadership', 'Soft Skills', 'Team leadership and management', '[{"level": 1, "name": "Emerging", "description": "Basic leadership", "competencies": ["Delegation", "Communication"]}, {"level": 2, "name": "Competent", "description": "Experienced leader", "competencies": ["Strategy", "Coaching"]}]'),
    ('Project Management', 'Management', 'Project planning and execution', '[{"level": 1, "name": "Basic", "description": "Basic PM skills", "competencies": ["Planning", "Scheduling"]}, {"level": 2, "name": "Advanced", "description": "Complex project management", "competencies": ["Risk Management", "Stakeholder Management"]}]')
ON CONFLICT DO NOTHING;

-- Insert sample training courses
INSERT INTO training_courses (title, description, provider, type, duration, cost, difficulty, status) VALUES
    ('Advanced JavaScript Development', 'Master modern JavaScript concepts', 'TechEd Academy', 'online', 40, 299.99, 'advanced', 'active'),
    ('Leadership Fundamentals', 'Core leadership skills for managers', 'Leadership Institute', 'workshop', 16, 599.99, 'intermediate', 'active'),
    ('Agile Project Management', 'Comprehensive Agile methodology training', 'PM Professional', 'certification', 32, 899.99, 'intermediate', 'active')
ON CONFLICT DO NOTHING;