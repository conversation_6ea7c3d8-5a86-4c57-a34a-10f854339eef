import { IsOptional, IsString, <PERSON><PERSON>num, <PERSON><PERSON><PERSON>ber, IsArray, <PERSON>, <PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { TrainingType, TrainingDifficulty, TrainingStatus } from './training-course.dto';

export class TrainingFiltersDto {
  @ApiPropertyOptional({
    description: 'Search term for course title, description, or provider',
    example: 'JavaScript'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by course type',
    enum: TrainingType,
    example: TrainingType.ONLINE
  })
  @IsOptional()
  @IsEnum(TrainingType)
  type?: TrainingType;

  @ApiPropertyOptional({
    description: 'Filter by difficulty level',
    enum: TrainingDifficulty,
    example: TrainingDifficulty.INTERMEDIATE
  })
  @IsOptional()
  @IsEnum(TrainingDifficulty)
  difficulty?: TrainingDifficulty;

  @ApiPropertyOptional({
    description: 'Filter by provider',
    example: 'TechEd Academy'
  })
  @IsOptional()
  @IsString()
  provider?: string;

  @ApiPropertyOptional({
    description: 'Filter by course status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(TrainingStatus)
  status?: TrainingStatus;

  @ApiPropertyOptional({
    description: 'Minimum cost filter',
    example: 100,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minCost?: number;

  @ApiPropertyOptional({
    description: 'Maximum cost filter',
    example: 1000,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxCost?: number;

  @ApiPropertyOptional({
    description: 'Filter by skills addressed by the course',
    example: ['skill-uuid-1', 'skill-uuid-2']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(s => s.trim());
    }
    return value;
  })
  skills?: string[];

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'createdAt',
    enum: ['createdAt', 'title', 'duration', 'cost', 'rating', 'enrollments']
  })
  @IsOptional()
  @IsString()
  sortBy?: 'createdAt' | 'title' | 'duration' | 'cost' | 'rating' | 'enrollments';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc']
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({
    description: 'Filter by minimum rating',
    example: 4.0,
    minimum: 0,
    maximum: 5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  minRating?: number;

  @ApiPropertyOptional({
    description: 'Filter by maximum rating',
    example: 5.0,
    minimum: 0,
    maximum: 5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  maxRating?: number;

  @ApiPropertyOptional({
    description: 'Filter by minimum duration (hours)',
    example: 10,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minDuration?: number;

  @ApiPropertyOptional({
    description: 'Filter by maximum duration (hours)',
    example: 100,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxDuration?: number;

  @ApiPropertyOptional({
    description: 'Filter by department ID',
    example: 'department-uuid-123'
  })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Filter by enrollment status (for user-specific queries)',
    example: 'enrolled',
    enum: ['enrolled', 'in-progress', 'completed', 'dropped', 'failed']
  })
  @IsOptional()
  @IsString()
  enrollmentStatus?: string;

  @ApiPropertyOptional({
    description: 'Show only courses with available capacity',
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  availableOnly?: boolean;
}