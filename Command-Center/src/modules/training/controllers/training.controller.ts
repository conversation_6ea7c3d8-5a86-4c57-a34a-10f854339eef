import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiParam, 
  ApiQuery 
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { TrainingService } from '../services/training.service';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { RequirePermissions } from '../../auth/decorators/permissions.decorator';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { User } from '../../users/entities/user.entity';
import { 
  CreateTrainingCourseDto, 
  UpdateTrainingCourseDto, 
  TrainingCourseResponseDto 
} from '../dto/training-course.dto';
import { 
  CreateEnrollmentDto, 
  UpdateEnrollmentDto 
} from '../dto/enrollment.dto';
import { TrainingFiltersDto } from '../dto/training-filters.dto';

@ApiTags('Training')
@Controller('training')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class TrainingController {
  constructor(private readonly trainingService: TrainingService) {}

  // =============================================================================
  // COURSE MANAGEMENT
  // =============================================================================

  @Post('courses')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager')
  @RequirePermissions('training:create')
  @ApiOperation({ summary: 'Create a new training course' })
  @ApiResponse({ 
    status: 201, 
    description: 'Course created successfully',
    type: TrainingCourseResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Invalid course data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createCourse(
    @Body(ValidationPipe) createCourseDto: CreateTrainingCourseDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.trainingService.createCourse(createCourseDto);
  }

  @Get('courses')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Get all training courses with filters' })
  @ApiResponse({ 
    status: 200, 
    description: 'Courses retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        courses: {
          type: 'array',
          items: { $ref: '#/components/schemas/TrainingCourseResponseDto' }
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'type', required: false, description: 'Course type filter' })
  @ApiQuery({ name: 'difficulty', required: false, description: 'Difficulty level filter' })
  @ApiQuery({ name: 'provider', required: false, description: 'Provider filter' })
  @ApiQuery({ name: 'status', required: false, description: 'Status filter' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async getCourses(
    @Query() filters: TrainingFiltersDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.trainingService.getCourses(filters);
  }

  @Get('courses/:id')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Get training course by ID' })
  @ApiParam({ name: 'id', description: 'Course ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Course retrieved successfully',
    type: TrainingCourseResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async getCourseById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<any> {
    return await this.trainingService.getCourseById(id);
  }

  @Put('courses/:id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager')
  @RequirePermissions('training:update')
  @ApiOperation({ summary: 'Update training course' })
  @ApiParam({ name: 'id', description: 'Course ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Course updated successfully',
    type: TrainingCourseResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Invalid course data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async updateCourse(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateCourseDto: UpdateTrainingCourseDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.trainingService.updateCourse(id, updateCourseDto);
  }

  @Delete('courses/:id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager')
  @RequirePermissions('training:delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete training course' })
  @ApiParam({ name: 'id', description: 'Course ID' })
  @ApiResponse({ status: 204, description: 'Course deleted successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Course not found' })
  async deleteCourse(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<void> {
    await this.trainingService.deleteCourse(id);
  }

  // =============================================================================
  // ENROLLMENT MANAGEMENT
  // =============================================================================

  @Post('enrollments')
  @RequirePermissions('training:enroll')
  @ApiOperation({ summary: 'Enroll in a training course' })
  @ApiResponse({ 
    status: 201, 
    description: 'Enrollment successful',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        courseId: { type: 'string' },
        status: { type: 'string' },
        enrolledDate: { type: 'string', format: 'date-time' },
        progress: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid enrollment data' })
  @ApiResponse({ status: 409, description: 'Already enrolled' })
  async enrollInCourse(
    @Body(ValidationPipe) enrollmentDto: CreateEnrollmentDto,
    @GetUser() user: User,
  ): Promise<any> {
    // If userId is not provided, use the authenticated user's ID
    if (!enrollmentDto.userId) {
      enrollmentDto.userId = user.id;
    }
    
    return await this.trainingService.enrollUser(enrollmentDto);
  }

  @Get('enrollments')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Get enrollments' })
  @ApiResponse({ 
    status: 200, 
    description: 'Enrollments retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          userId: { type: 'string' },
          courseId: { type: 'string' },
          status: { type: 'string' },
          progress: { type: 'number' },
          course: { type: 'object' },
          user: { type: 'object' }
        }
      }
    }
  })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'courseId', required: false, description: 'Filter by course ID' })
  async getEnrollments(
    @Query('userId') userId?: string,
    @Query('courseId') courseId?: string,
    @GetUser() user?: User,
  ): Promise<any[]> {
    // Non-admin users can only see their own enrollments
    if (!user?.roles?.some(role => ['admin', 'training_manager'].includes(role.name))) {
      userId = user.id;
    }
    
    return await this.trainingService.getEnrollments(userId, courseId);
  }

  @Put('enrollments/:id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager', 'user')
  @RequirePermissions('training:update')
  @ApiOperation({ summary: 'Update enrollment' })
  @ApiParam({ name: 'id', description: 'Enrollment ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Enrollment updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        courseId: { type: 'string' },
        status: { type: 'string' },
        progress: { type: 'number' },
        score: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid enrollment data' })
  @ApiResponse({ status: 404, description: 'Enrollment not found' })
  async updateEnrollment(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateEnrollmentDto: UpdateEnrollmentDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.trainingService.updateEnrollment(id, updateEnrollmentDto);
  }

  // =============================================================================
  // RECOMMENDATIONS
  // =============================================================================

  @Get('recommendations')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Get training recommendations for user' })
  @ApiResponse({ 
    status: 200, 
    description: 'Recommendations retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          courseId: { type: 'string' },
          priority: { type: 'number' },
          reasoning: { type: 'string' },
          expectedImpact: { type: 'string' },
          course: { type: 'object' },
          skillGap: { type: 'object' }
        }
      }
    }
  })
  async getRecommendations(
    @GetUser() user: User,
  ): Promise<any[]> {
    return await this.trainingService.getRecommendations(user.id);
  }

  @Post('recommendations/generate')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Generate new training recommendations' })
  @ApiResponse({ 
    status: 201, 
    description: 'Recommendations generated successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          courseId: { type: 'string' },
          priority: { type: 'number' },
          reasoning: { type: 'string' },
          expectedImpact: { type: 'string' }
        }
      }
    }
  })
  async generateRecommendations(
    @GetUser() user: User,
  ): Promise<any[]> {
    return await this.trainingService.generateRecommendations(user.id);
  }

  @Put('recommendations/:id/status')
  @RequirePermissions('training:update')
  @ApiOperation({ summary: 'Update recommendation status' })
  @ApiParam({ name: 'id', description: 'Recommendation ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Recommendation status updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        status: { type: 'string' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  async updateRecommendationStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { status: 'accepted' | 'declined' },
    @GetUser() user: User,
  ): Promise<any> {
    return await this.trainingService.updateRecommendationStatus(id, body.status);
  }

  // =============================================================================
  // ANALYTICS & METRICS
  // =============================================================================

  @Get('metrics')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager', 'hr')
  @RequirePermissions('training:analytics')
  @ApiOperation({ summary: 'Get training metrics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalCourses: { type: 'number' },
        totalEnrollments: { type: 'number' },
        completionRate: { type: 'number' },
        averageScore: { type: 'number' },
        departmentMetrics: { type: 'array' }
      }
    }
  })
  @ApiQuery({ name: 'departmentId', required: false, description: 'Filter by department' })
  async getTrainingMetrics(
    @Query('departmentId') departmentId?: string,
  ): Promise<any> {
    return await this.trainingService.getTrainingMetrics(departmentId);
  }

  @Get('courses/:id/analytics')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager')
  @RequirePermissions('training:analytics')
  @ApiOperation({ summary: 'Get course analytics' })
  @ApiParam({ name: 'id', description: 'Course ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Course analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        enrollmentCount: { type: 'number' },
        completionRate: { type: 'number' },
        averageScore: { type: 'number' },
        departmentBreakdown: { type: 'array' },
        progressTrends: { type: 'array' }
      }
    }
  })
  async getCourseAnalytics(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<any> {
    return await this.trainingService.getCourseAnalytics(id);
  }

  @Get('users/:userId/progress')
  @RequirePermissions('training:read')
  @ApiOperation({ summary: 'Get user training progress' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'User progress retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalEnrollments: { type: 'number' },
        completedCourses: { type: 'number' },
        inProgressCourses: { type: 'number' },
        averageScore: { type: 'number' },
        skillProgress: { type: 'array' }
      }
    }
  })
  async getUserProgress(
    @Param('userId', ParseUUIDPipe) userId: string,
    @GetUser() user: User,
  ): Promise<any> {
    // Users can only see their own progress unless they're admin/manager
    if (userId !== user.id && !user?.roles?.some(role => ['admin', 'training_manager'].includes(role.name))) {
      userId = user.id;
    }
    
    return await this.trainingService.getUserProgress(userId);
  }

  @Get('roi-analysis')
  @UseGuards(RolesGuard)
  @Roles('admin', 'training_manager', 'finance')
  @RequirePermissions('training:analytics')
  @ApiOperation({ summary: 'Get training ROI analysis' })
  @ApiResponse({ 
    status: 200, 
    description: 'ROI analysis retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalInvestment: { type: 'number' },
        estimatedROI: { type: 'number' },
        skillImprovementMetrics: { type: 'array' },
        performanceCorrelation: { type: 'object' }
      }
    }
  })
  @ApiQuery({ name: 'departmentId', required: false, description: 'Filter by department' })
  async getROIAnalysis(
    @Query('departmentId') departmentId?: string,
  ): Promise<any> {
    return await this.trainingService.getROIAnalysis(departmentId);
  }
}