import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { WinsService } from './services/wins.service';
import { SubmissionService } from './services/submission.service';
import { WinsAnalyticsService } from './services/wins-analytics.service';
import { WinsController } from './controllers/wins.controller';
import { SubmissionController } from './controllers/submission.controller';
import { WinsGateway } from './gateways/wins.gateway';
import { ReportGeneratorService } from './services/report-generator.service';
import { AchievementService } from './services/achievement.service';

@Module({
  imports: [PrismaModule],
  controllers: [
    WinsController,
    SubmissionController,
  ],
  providers: [
    WinsService,
    SubmissionService,
    WinsAnalyticsService,
    WinsGateway,
    ReportGeneratorService,
    AchievementService,
  ],
  exports: [
    WinsService,
    SubmissionService,
    WinsAnalyticsService,
    ReportGeneratorService,
    AchievementService,
  ],
})
export class WinsModule {}