import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WinsService } from './wins.service';
import { Submission } from './entities/submission.entity';
import { Achievement } from './entities/achievement.entity';
import { Recognition } from './entities/recognition.entity';
import { CostInitiative } from './entities/cost-initiative.entity';
import { TrainingIdea } from './entities/training-idea.entity';
import { ProgressUpdate } from './entities/progress-update.entity';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateSubmissionDto } from './dto/create-submission.dto';

describe('WinsService', () => {
  let service: WinsService;
  let submissionRepository: Repository<Submission>;
  let achievementRepository: Repository<Achievement>;
  let recognitionRepository: Repository<Recognition>;
  let costInitiativeRepository: Repository<CostInitiative>;
  let trainingIdeaRepository: Repository<TrainingIdea>;
  let progressUpdateRepository: Repository<ProgressUpdate>;

  const mockUser = {
    id: 'user1',
    name: 'Test User',
    email: '<EMAIL>',
    department: { id: 'dept1', name: 'L&D' },
  };

  const mockSubmission = {
    id: '1',
    userId: 'user1',
    user: mockUser,
    weekStartDate: new Date('2024-01-01'),
    weekEndDate: new Date('2024-01-07'),
    weeklyHighlight: 'Great week of achievements',
    status: 'submitted',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockAchievement = {
    id: '1',
    submissionId: '1',
    title: 'Completed Training Program',
    description: 'Successfully completed advanced training',
    impact: 'Improved team productivity by 20%',
    category: 'training',
  };

  const mockRecognition = {
    id: '1',
    submissionId: '1',
    type: 'award',
    title: 'Employee of the Month',
    issuedBy: 'Management',
    date: new Date(),
  };

  const mockCostInitiative = {
    id: '1',
    submissionId: '1',
    title: 'Process Automation',
    description: 'Automated manual reporting process',
    estimatedSavings: 50000,
    implementationDate: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WinsService,
        {
          provide: getRepositoryToken(Submission),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Achievement),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Recognition),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CostInitiative),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(TrainingIdea),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ProgressUpdate),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WinsService>(WinsService);
    submissionRepository = module.get<Repository<Submission>>(getRepositoryToken(Submission));
    achievementRepository = module.get<Repository<Achievement>>(getRepositoryToken(Achievement));
    recognitionRepository = module.get<Repository<Recognition>>(getRepositoryToken(Recognition));
    costInitiativeRepository = module.get<Repository<CostInitiative>>(getRepositoryToken(CostInitiative));
    trainingIdeaRepository = module.get<Repository<TrainingIdea>>(getRepositoryToken(TrainingIdea));
    progressUpdateRepository = module.get<Repository<ProgressUpdate>>(getRepositoryToken(ProgressUpdate));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSubmission', () => {
    it('should create a new submission with related entities', async () => {
      const createSubmissionDto: CreateSubmissionDto = {
        userId: 'user1',
        weekStartDate: '2024-01-01',
        weekEndDate: '2024-01-07',
        weeklyHighlight: 'Great achievements this week',
        achievements: [
          {
            title: 'New Achievement',
            description: 'Description',
            impact: 'High impact',
            category: 'training',
          },
        ],
        recognitions: [
          {
            type: 'certificate',
            title: 'Training Certificate',
            issuedBy: 'Training Provider',
            date: '2024-01-05',
          },
        ],
        costInitiatives: [
          {
            title: 'Cost Saving Initiative',
            description: 'Saved costs',
            estimatedSavings: 25000,
            implementationDate: '2024-01-03',
          },
        ],
      };

      // Check for existing submission
      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue(null);

      const savedSubmission = { ...mockSubmission, id: '2' };
      jest.spyOn(submissionRepository, 'create').mockReturnValue(savedSubmission as any);
      jest.spyOn(submissionRepository, 'save').mockResolvedValue(savedSubmission as any);

      jest.spyOn(achievementRepository, 'create').mockImplementation((data) => ({ ...data, id: '2' } as any));
      jest.spyOn(achievementRepository, 'save').mockImplementation((data) => Promise.resolve(data));

      jest.spyOn(recognitionRepository, 'create').mockImplementation((data) => ({ ...data, id: '2' } as any));
      jest.spyOn(recognitionRepository, 'save').mockImplementation((data) => Promise.resolve(data));

      jest.spyOn(costInitiativeRepository, 'create').mockImplementation((data) => ({ ...data, id: '2' } as any));
      jest.spyOn(costInitiativeRepository, 'save').mockImplementation((data) => Promise.resolve(data));

      const result = await service.createSubmission(createSubmissionDto);

      expect(result).toEqual(savedSubmission);
      expect(submissionRepository.create).toHaveBeenCalled();
      expect(achievementRepository.save).toHaveBeenCalled();
      expect(recognitionRepository.save).toHaveBeenCalled();
      expect(costInitiativeRepository.save).toHaveBeenCalled();
    });

    it('should throw error if submission already exists for the week', async () => {
      const createSubmissionDto: CreateSubmissionDto = {
        userId: 'user1',
        weekStartDate: '2024-01-01',
        weekEndDate: '2024-01-07',
        weeklyHighlight: 'Duplicate submission',
      };

      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue(mockSubmission as any);

      await expect(service.createSubmission(createSubmissionDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAllSubmissions', () => {
    it('should return filtered submissions', async () => {
      const queryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockSubmission], 1]),
      };

      jest.spyOn(submissionRepository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);

      const result = await service.findAllSubmissions({
        userId: 'user1',
        status: 'submitted',
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: [mockSubmission],
        total: 1,
        page: 1,
        limit: 10,
      });
      expect(queryBuilder.where).toHaveBeenCalledWith('submission.userId = :userId', { userId: 'user1' });
    });
  });

  describe('findSubmissionById', () => {
    it('should return a submission with all related entities', async () => {
      const fullSubmission = {
        ...mockSubmission,
        achievements: [mockAchievement],
        recognitions: [mockRecognition],
        costInitiatives: [mockCostInitiative],
        trainingIdeas: [],
        progressUpdates: [],
      };

      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue(fullSubmission as any);

      const result = await service.findSubmissionById('1');

      expect(result).toEqual(fullSubmission);
      expect(submissionRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: [
          'user',
          'user.department',
          'achievements',
          'recognitions',
          'costInitiatives',
          'trainingIdeas',
          'progressUpdates',
        ],
      });
    });

    it('should throw NotFoundException if submission not found', async () => {
      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue(null);

      await expect(service.findSubmissionById('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateSubmissionStatus', () => {
    it('should update submission status', async () => {
      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue(mockSubmission as any);
      jest.spyOn(submissionRepository, 'save').mockResolvedValue({
        ...mockSubmission,
        status: 'approved',
        reviewedAt: new Date(),
        reviewedBy: 'manager1',
      } as any);

      const result = await service.updateSubmissionStatus('1', {
        status: 'approved',
        reviewNotes: 'Great work!',
        reviewedBy: 'manager1',
      });

      expect(result.status).toBe('approved');
      expect(result.reviewedAt).toBeDefined();
      expect(result.reviewedBy).toBe('manager1');
    });

    it('should throw error if trying to update already reviewed submission', async () => {
      jest.spyOn(submissionRepository, 'findOne').mockResolvedValue({
        ...mockSubmission,
        status: 'approved',
        reviewedAt: new Date(),
      } as any);

      await expect(
        service.updateSubmissionStatus('1', { status: 'rejected' } as any)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getAnalytics', () => {
    it('should return analytics data', async () => {
      const queryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            month: '2024-01',
            submissions: '10',
            achievements: '50',
            recognitions: '15',
            costSavings: '250000',
          },
        ]),
      };

      const totalQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalSubmissions: '50',
          totalAchievements: '250',
          totalRecognitions: '75',
          totalCostSavings: '1250000',
          activeUsers: '25',
        }),
      };

      jest.spyOn(submissionRepository, 'createQueryBuilder')
        .mockReturnValueOnce(queryBuilder as any)
        .mockReturnValueOnce(totalQueryBuilder as any);

      const result = await service.getAnalytics({
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      });

      expect(result).toEqual({
        totalSubmissions: 50,
        totalAchievements: 250,
        totalRecognitions: 75,
        totalCostSavings: 1250000,
        activeUsers: 25,
        monthlyBreakdown: [
          {
            month: '2024-01',
            submissions: 10,
            achievements: 50,
            recognitions: 15,
            costSavings: 250000,
          },
        ],
      });
    });
  });

  describe('getTopPerformers', () => {
    it('should return top performers by achievement count', async () => {
      const queryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            userId: 'user1',
            userName: 'Top Performer',
            departmentName: 'L&D',
            achievementCount: '25',
            recognitionCount: '10',
          },
        ]),
      };

      jest.spyOn(achievementRepository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);

      const result = await service.getTopPerformers({ limit: 5 });

      expect(result).toEqual([
        {
          userId: 'user1',
          userName: 'Top Performer',
          departmentName: 'L&D',
          achievementCount: 25,
          recognitionCount: 10,
        },
      ]);
      expect(queryBuilder.limit).toHaveBeenCalledWith(5);
    });
  });

  describe('getDepartmentAnalytics', () => {
    it('should return analytics by department', async () => {
      const queryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            departmentId: 'dept1',
            departmentName: 'L&D',
            totalSubmissions: '20',
            totalAchievements: '100',
            totalCostSavings: '500000',
            activeUsers: '10',
          },
        ]),
      };

      jest.spyOn(submissionRepository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);

      const result = await service.getDepartmentAnalytics({
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      });

      expect(result).toEqual([
        {
          departmentId: 'dept1',
          departmentName: 'L&D',
          totalSubmissions: 20,
          totalAchievements: 100,
          totalCostSavings: 500000,
          activeUsers: 10,
        },
      ]);
    });
  });
});