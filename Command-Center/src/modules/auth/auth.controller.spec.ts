import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { LoginDto, RegisterDto } from './dto/auth.dto';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    login: jest.fn(),
    register: jest.fn(),
    refreshToken: jest.fn(),
  };

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
  };

  const mockAuthResponse = {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
    user: mockUser,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should return auth response for valid credentials', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password',
      };

      mockAuthService.login.mockResolvedValue(mockAuthResponse);

      const result = await controller.login(loginDto);

      expect(result).toEqual(mockAuthResponse);
      expect(authService.login).toHaveBeenCalledWith(loginDto);
    });

    it('should throw error if service fails', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password',
      };

      mockAuthService.login.mockRejectedValue(new Error('Login failed'));

      await expect(controller.login(loginDto)).rejects.toThrow('Login failed');
    });
  });

  describe('register', () => {
    it('should return auth response for successful registration', async () => {
      const registerDto: RegisterDto = {
        email: '<EMAIL>',
        password: 'password',
        name: 'New User',
      };

      mockAuthService.register.mockResolvedValue(mockAuthResponse);

      const result = await controller.register(registerDto);

      expect(result).toEqual(mockAuthResponse);
      expect(authService.register).toHaveBeenCalledWith(registerDto);
    });

    it('should throw error if registration fails', async () => {
      const registerDto: RegisterDto = {
        email: '<EMAIL>',
        password: 'password',
        name: 'Existing User',
      };

      mockAuthService.register.mockRejectedValue(new Error('Email already exists'));

      await expect(controller.register(registerDto)).rejects.toThrow('Email already exists');
    });
  });

  describe('refreshToken', () => {
    it('should return new access token', async () => {
      const refreshDto = { refreshToken: 'valid_refresh_token' };
      const expectedResponse = { accessToken: 'new_access_token' };

      mockAuthService.refreshToken.mockResolvedValue(expectedResponse);

      const result = await controller.refreshToken(refreshDto);

      expect(result).toEqual(expectedResponse);
      expect(authService.refreshToken).toHaveBeenCalledWith('valid_refresh_token');
    });

    it('should throw error for invalid refresh token', async () => {
      const refreshDto = { refreshToken: 'invalid_token' };

      mockAuthService.refreshToken.mockRejectedValue(new Error('Invalid token'));

      await expect(controller.refreshToken(refreshDto)).rejects.toThrow('Invalid token');
    });
  });

  describe('getProfile', () => {
    it('should return user profile', () => {
      const req = { user: mockUser };

      const result = controller.getProfile(req);

      expect(result).toEqual(mockUser);
    });
  });
});