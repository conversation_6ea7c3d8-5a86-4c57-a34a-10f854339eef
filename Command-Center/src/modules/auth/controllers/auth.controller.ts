import {
  Controller,
  Post,
  Body,
  HttpStatus,
  HttpCode,
  UseGuards,
  Request,
  Get,
  Param,
  Put,
  Delete,
  Query,
  ValidationPipe,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { AuthService, LoginCredentials, RegisterData, LoginResult } from '../services/auth.service';
import { RoleService } from '../services/role.service';
import { TokenService } from '../services/token.service';
import { SessionService } from '../services/session.service';
import { SecurityService } from '../services/security.service';
import { RolesGuard } from '../guards/roles.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { Roles } from '../decorators/roles.decorator';
import { Permissions } from '../decorators/permissions.decorator';
import { GetUser } from '../decorators/get-user.decorator';
import { User } from '../../users/entities/user.entity';
import { 
  LoginDto, 
  RegisterDto, 
  ChangePasswordDto, 
  PasswordResetRequestDto, 
  PasswordResetConfirmDto,
  RefreshTokenDto,
  VerifyEmailDto
} from '../dto/auth.dto';
import { CreateRoleDto, UpdateRoleDto } from '../dto/role.dto';
import { AssignRoleDto } from '../dto/assign-role.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly roleService: RoleService,
    private readonly tokenService: TokenService,
    private readonly sessionService: SessionService,
    private readonly securityService: SecurityService,
  ) {}

  // =============================================================================
  // AUTHENTICATION ENDPOINTS
  // =============================================================================

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ 
    status: 200, 
    description: 'Login successful',
    schema: {
      type: 'object',
      properties: {
        user: { type: 'object' },
        tokens: { 
          type: 'object',
          properties: {
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        sessionId: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @ApiResponse({ status: 429, description: 'Too many login attempts' })
  async login(
    @Body(ValidationPipe) loginDto: LoginDto,
    @Request() req: any,
  ): Promise<LoginResult> {
    const credentials: LoginCredentials = {
      email: loginDto.email,
      password: loginDto.password,
      deviceInfo: {
        deviceId: loginDto.deviceId,
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      },
    };

    const result = await this.authService.login(credentials);

    // Remove sensitive data from user object
    const { password, ...userWithoutPassword } = result.user;
    
    return {
      ...result,
      user: userWithoutPassword as User,
    };
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({ 
    status: 201, 
    description: 'Registration successful',
    schema: {
      type: 'object',
      properties: {
        user: { type: 'object' },
        requiresEmailVerification: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Registration failed' })
  async register(
    @Body(ValidationPipe) registerDto: RegisterDto,
    @Request() req: any,
  ): Promise<{ user: Partial<User>; requiresEmailVerification: boolean; message: string }> {
    const registerData: RegisterData = {
      email: registerDto.email,
      password: registerDto.password,
      firstName: registerDto.firstName,
      lastName: registerDto.lastName,
      deviceInfo: {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      },
    };

    const result = await this.authService.register(registerData);

    // Remove sensitive data
    const { password, ...userWithoutPassword } = result.user;

    return {
      user: userWithoutPassword,
      requiresEmailVerification: result.requiresEmailVerification,
      message: result.requiresEmailVerification 
        ? 'Registration successful. Please check your email to verify your account.'
        : 'Registration successful. You can now log in.',
    };
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Token refresh successful',
    schema: {
      type: 'object',
      properties: {
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' },
        expiresIn: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refreshToken(
    @Body(ValidationPipe) refreshTokenDto: RefreshTokenDto,
    @Request() req: any,
  ): Promise<any> {
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
    };

    const tokens = await this.authService.refreshTokens(
      refreshTokenDto.refreshToken,
      deviceInfo,
    );

    return tokens;
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(
    @GetUser() user: User,
    @Body() body: { refreshToken?: string; sessionId?: string },
  ): Promise<{ message: string }> {
    const sessionId = body.sessionId || user.id; // fallback to user ID
    
    await this.authService.logout(sessionId, body.refreshToken);
    
    return { message: 'Logout successful' };
  }

  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid verification token' })
  async verifyEmail(
    @Body(ValidationPipe) verifyEmailDto: VerifyEmailDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    
    await this.authService.verifyEmail(verifyEmailDto.token, ipAddress);
    
    return { message: 'Email verified successfully' };
  }

  @Post('password-reset/request')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiResponse({ status: 429, description: 'Too many password reset requests' })
  async requestPasswordReset(
    @Body(ValidationPipe) passwordResetRequestDto: PasswordResetRequestDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const requestData = {
      email: passwordResetRequestDto.email,
      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'],
    };

    await this.authService.requestPasswordReset(requestData);
    
    return { message: 'If an account with this email exists, a password reset link has been sent.' };
  }

  @Post('password-reset/confirm')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Confirm password reset' })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  @ApiResponse({ status: 400, description: 'Invalid or expired reset token' })
  async confirmPasswordReset(
    @Body(ValidationPipe) passwordResetConfirmDto: PasswordResetConfirmDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const confirmData = {
      token: passwordResetConfirmDto.token,
      newPassword: passwordResetConfirmDto.newPassword,
      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'],
    };

    await this.authService.confirmPasswordReset(confirmData);
    
    return { message: 'Password reset successful' };
  }

  @Put('change-password')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid current password' })
  async changePassword(
    @GetUser() user: User,
    @Body(ValidationPipe) changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    await this.authService.changePassword(
      user.id,
      changePasswordDto.currentPassword,
      changePasswordDto.newPassword,
    );
    
    return { message: 'Password changed successfully' };
  }

  // =============================================================================
  // USER PROFILE ENDPOINTS
  // =============================================================================

  @Get('profile')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully' })
  async getProfile(@GetUser() user: User): Promise<Partial<User>> {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  @Get('sessions')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user sessions' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  async getSessions(@GetUser() user: User): Promise<any[]> {
    return await this.sessionService.getUserSessions(user.id);
  }

  @Delete('sessions/:sessionId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Terminate a specific session' })
  @ApiResponse({ status: 200, description: 'Session terminated successfully' })
  async terminateSession(
    @GetUser() user: User,
    @Param('sessionId', ParseUUIDPipe) sessionId: string,
  ): Promise<{ message: string }> {
    await this.sessionService.terminateSession(sessionId, 'User requested termination');
    return { message: 'Session terminated successfully' };
  }

  @Delete('sessions')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Terminate all user sessions' })
  @ApiResponse({ status: 200, description: 'All sessions terminated successfully' })
  async terminateAllSessions(
    @GetUser() user: User,
  ): Promise<{ message: string }> {
    await this.sessionService.terminateAllUserSessions(
      user.id,
      undefined,
      'User requested termination of all sessions',
    );
    return { message: 'All sessions terminated successfully' };
  }

  // =============================================================================
  // ROLE MANAGEMENT ENDPOINTS (Admin only)
  // =============================================================================

  @Post('roles')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createRole(
    @Body(ValidationPipe) createRoleDto: CreateRoleDto,
  ): Promise<any> {
    return await this.roleService.createRole(createRoleDto);
  }

  @Get('roles')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'hr')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'Roles retrieved successfully' })
  async getRoles(): Promise<any[]> {
    return await this.roleService.getAllRoles();
  }

  @Put('roles/:roleId')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update role' })
  @ApiResponse({ status: 200, description: 'Role updated successfully' })
  async updateRole(
    @Param('roleId', ParseUUIDPipe) roleId: string,
    @Body(ValidationPipe) updateRoleDto: UpdateRoleDto,
  ): Promise<any> {
    return await this.roleService.updateRole(roleId, updateRoleDto);
  }

  @Delete('roles/:roleId')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete role' })
  @ApiResponse({ status: 200, description: 'Role deleted successfully' })
  async deleteRole(
    @Param('roleId', ParseUUIDPipe) roleId: string,
  ): Promise<{ message: string }> {
    await this.roleService.deleteRole(roleId);
    return { message: 'Role deleted successfully' };
  }

  @Post('users/:userId/roles')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'hr')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiResponse({ status: 200, description: 'Role assigned successfully' })
  async assignRole(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body(ValidationPipe) assignRoleDto: AssignRoleDto,
  ): Promise<{ message: string }> {
    await this.roleService.assignRoleToUser(userId, assignRoleDto.roleId);
    return { message: 'Role assigned successfully' };
  }

  @Delete('users/:userId/roles/:roleId')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'hr')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove role from user' })
  @ApiResponse({ status: 200, description: 'Role removed successfully' })
  async removeRole(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('roleId', ParseUUIDPipe) roleId: string,
  ): Promise<{ message: string }> {
    await this.roleService.removeRoleFromUser(userId, roleId);
    return { message: 'Role removed successfully' };
  }

  // =============================================================================
  // ADMIN ENDPOINTS
  // =============================================================================

  @Get('admin/security-stats')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get security statistics' })
  @ApiResponse({ status: 200, description: 'Security statistics retrieved successfully' })
  async getSecurityStats(): Promise<any> {
    return await this.securityService.getSecurityStatistics();
  }

  @Get('admin/auth-attempts')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get authentication attempts' })
  @ApiResponse({ status: 200, description: 'Authentication attempts retrieved successfully' })
  async getAuthAttempts(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
  ): Promise<any> {
    return await this.securityService.getAuthAttempts(page, limit);
  }

  @Get('admin/active-sessions')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get active sessions' })
  @ApiResponse({ status: 200, description: 'Active sessions retrieved successfully' })
  async getActiveSessions(): Promise<any[]> {
    return await this.sessionService.getActiveSessions();
  }
}