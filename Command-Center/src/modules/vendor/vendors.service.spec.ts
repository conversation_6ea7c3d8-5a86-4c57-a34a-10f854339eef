import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VendorsService } from './vendors.service';
import { Vendor } from './entities/vendor.entity';
import { Contract } from './entities/contract.entity';
import { VendorReview } from './entities/vendor-review.entity';
import { Proposal } from './entities/proposal.entity';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateVendorDto } from './dto/create-vendor.dto';

describe('VendorsService', () => {
  let service: VendorsService;
  let vendorRepository: Repository<Vendor>;
  let contractRepository: Repository<Contract>;
  let reviewRepository: Repository<VendorReview>;
  let proposalRepository: Repository<Proposal>;

  const mockVendor = {
    id: '1',
    name: 'Test Vendor',
    email: '<EMAIL>',
    phone: '1234567890',
    website: 'https://test.com',
    category: 'training',
    status: 'active',
    rating: 4.5,
    totalReviews: 10,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockContract = {
    id: '1',
    vendorId: '1',
    vendor: mockVendor,
    contractNumber: 'C-2024-001',
    title: 'Training Services',
    value: 50000,
    startDate: new Date(),
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    status: 'active',
    createdAt: new Date(),
  };

  const mockReview = {
    id: '1',
    vendorId: '1',
    vendor: mockVendor,
    userId: 'user1',
    rating: 5,
    comment: 'Excellent service',
    serviceType: 'training',
    createdAt: new Date(),
  };

  const mockProposal = {
    id: '1',
    vendorId: '1',
    vendor: mockVendor,
    title: 'New Training Program',
    description: 'Comprehensive training solution',
    amount: 25000,
    status: 'pending',
    submittedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VendorsService,
        {
          provide: getRepositoryToken(Vendor),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Contract),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(VendorReview),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Proposal),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<VendorsService>(VendorsService);
    vendorRepository = module.get<Repository<Vendor>>(getRepositoryToken(Vendor));
    contractRepository = module.get<Repository<Contract>>(getRepositoryToken(Contract));
    reviewRepository = module.get<Repository<VendorReview>>(getRepositoryToken(VendorReview));
    proposalRepository = module.get<Repository<Proposal>>(getRepositoryToken(Proposal));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createVendor', () => {
    it('should create a new vendor', async () => {
      const createVendorDto: CreateVendorDto = {
        name: 'New Vendor',
        email: '<EMAIL>',
        phone: '9876543210',
        category: 'training',
        description: 'New vendor description',
        services: ['Training', 'Consulting'],
      };

      const savedVendor = { ...createVendorDto, id: '2', status: 'active' };
      jest.spyOn(vendorRepository, 'create').mockReturnValue(savedVendor as any);
      jest.spyOn(vendorRepository, 'save').mockResolvedValue(savedVendor as any);

      const result = await service.createVendor(createVendorDto);

      expect(result).toEqual(savedVendor);
      expect(vendorRepository.create).toHaveBeenCalledWith({
        ...createVendorDto,
        status: 'active',
      });
    });

    it('should throw error if vendor with email already exists', async () => {
      const createVendorDto: CreateVendorDto = {
        name: 'Duplicate Vendor',
        email: '<EMAIL>',
        phone: '1234567890',
        category: 'training',
      };

      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(mockVendor as any);

      await expect(service.createVendor(createVendorDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAllVendors', () => {
    it('should return filtered vendors', async () => {
      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockVendor], 1]),
      };

      jest.spyOn(vendorRepository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);

      const result = await service.findAllVendors({
        category: 'training',
        status: 'active',
        minRating: 4,
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: [mockVendor],
        total: 1,
        page: 1,
        limit: 10,
      });
      expect(queryBuilder.where).toHaveBeenCalled();
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('vendor.rating >= :minRating', { minRating: 4 });
    });
  });

  describe('findVendorById', () => {
    it('should return a vendor by id', async () => {
      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(mockVendor as any);

      const result = await service.findVendorById('1');

      expect(result).toEqual(mockVendor);
      expect(vendorRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['contracts', 'reviews', 'proposals'],
      });
    });

    it('should throw NotFoundException if vendor not found', async () => {
      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(null);

      await expect(service.findVendorById('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createContract', () => {
    it('should create a new contract', async () => {
      const createContractDto = {
        vendorId: '1',
        title: 'New Contract',
        value: 75000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        terms: 'Payment terms',
      };

      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(mockVendor as any);
      
      const contractNumber = 'C-2024-002';
      const queryBuilder = {
        select: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue({ contractNumber: 'C-2024-001' }),
      };
      jest.spyOn(contractRepository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);

      const savedContract = { 
        ...createContractDto, 
        id: '2', 
        contractNumber,
        status: 'active' 
      };
      jest.spyOn(contractRepository, 'create').mockReturnValue(savedContract as any);
      jest.spyOn(contractRepository, 'save').mockResolvedValue(savedContract as any);

      const result = await service.createContract(createContractDto);

      expect(result).toEqual(savedContract);
      expect(vendorRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
    });

    it('should throw NotFoundException if vendor not found', async () => {
      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(null);

      await expect(service.createContract({ vendorId: '999' } as any)).rejects.toThrow(NotFoundException);
    });
  });

  describe('addVendorReview', () => {
    it('should add a vendor review and update rating', async () => {
      const createReviewDto = {
        vendorId: '1',
        userId: 'user1',
        rating: 5,
        comment: 'Great service',
        serviceType: 'training',
      };

      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(mockVendor as any);
      jest.spyOn(reviewRepository, 'findOne').mockResolvedValue(null);
      
      const savedReview = { ...createReviewDto, id: '2' };
      jest.spyOn(reviewRepository, 'create').mockReturnValue(savedReview as any);
      jest.spyOn(reviewRepository, 'save').mockResolvedValue(savedReview as any);

      const avgQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ avgRating: 4.6, count: 11 }),
      };
      jest.spyOn(reviewRepository, 'createQueryBuilder').mockReturnValue(avgQueryBuilder as any);

      jest.spyOn(vendorRepository, 'update').mockResolvedValue({} as any);

      const result = await service.addVendorReview(createReviewDto);

      expect(result).toEqual(savedReview);
      expect(vendorRepository.update).toHaveBeenCalledWith('1', {
        rating: 4.6,
        totalReviews: 11,
      });
    });

    it('should throw error if user already reviewed vendor', async () => {
      const createReviewDto = {
        vendorId: '1',
        userId: 'user1',
        rating: 5,
        comment: 'Great service',
        serviceType: 'training',
      };

      jest.spyOn(vendorRepository, 'findOne').mockResolvedValue(mockVendor as any);
      jest.spyOn(reviewRepository, 'findOne').mockResolvedValue(mockReview as any);

      await expect(service.addVendorReview(createReviewDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getVendorMetrics', () => {
    it('should return vendor performance metrics', async () => {
      const contractQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { status: 'active', count: '5', totalValue: '250000' },
          { status: 'completed', count: '10', totalValue: '500000' },
        ]),
      };

      const proposalQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalProposals: '20',
          acceptedProposals: '15',
        }),
      };

      jest.spyOn(contractRepository, 'createQueryBuilder')
        .mockReturnValueOnce(contractQueryBuilder as any);
      jest.spyOn(proposalRepository, 'createQueryBuilder')
        .mockReturnValue(proposalQueryBuilder as any);

      const result = await service.getVendorMetrics({ vendorId: '1' });

      expect(result).toEqual({
        activeContracts: 5,
        completedContracts: 10,
        totalContractValue: 750000,
        averageContractValue: 50000,
        proposalAcceptanceRate: 75,
        totalProposals: 20,
      });
    });
  });

  describe('updateProposalStatus', () => {
    it('should update proposal status to accepted', async () => {
      jest.spyOn(proposalRepository, 'findOne').mockResolvedValue(mockProposal as any);
      jest.spyOn(proposalRepository, 'save').mockResolvedValue({
        ...mockProposal,
        status: 'accepted',
        reviewedAt: new Date(),
        reviewedBy: 'user1',
        reviewNotes: 'Approved',
      } as any);

      const result = await service.updateProposalStatus('1', {
        status: 'accepted',
        reviewNotes: 'Approved',
        reviewedBy: 'user1',
      });

      expect(result.status).toBe('accepted');
      expect(result.reviewedAt).toBeDefined();
    });

    it('should throw NotFoundException if proposal not found', async () => {
      jest.spyOn(proposalRepository, 'findOne').mockResolvedValue(null);

      await expect(
        service.updateProposalStatus('999', { status: 'accepted' } as any)
      ).rejects.toThrow(NotFoundException);
    });
  });
});