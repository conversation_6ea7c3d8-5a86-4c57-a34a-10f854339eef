import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { VendorService } from './services/vendor.service';
import { ProposalService } from './services/proposal.service';
import { ReviewService } from './services/review.service';
import { VendorController } from './controllers/vendor.controller';
import { ProposalController } from './controllers/proposal.controller';
import { ReviewController } from './controllers/review.controller';
import { VendorAnalyticsService } from './services/vendor-analytics.service';
import { VendorWorkflowService } from './services/vendor-workflow.service';
import { VendorGateway } from './gateways/vendor.gateway';

@Module({
  imports: [PrismaModule],
  controllers: [
    VendorController,
    ProposalController,
    ReviewController,
  ],
  providers: [
    VendorService,
    ProposalService,
    ReviewService,
    VendorAnalyticsService,
    VendorWorkflowService,
    VendorGateway,
  ],
  exports: [
    VendorService,
    ProposalService,
    ReviewService,
    VendorAnalyticsService,
    VendorWorkflowService,
  ],
})
export class VendorModule {}