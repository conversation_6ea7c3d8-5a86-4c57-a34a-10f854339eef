{"name": "command-center", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration": "jest --config ./src/modules/integration/test/e2e/jest-e2e.json", "test:integration:watch": "jest --config ./src/modules/integration/test/e2e/jest-e2e.json --watch", "test:integration:coverage": "jest --config ./src/modules/integration/test/e2e/jest-e2e.json --coverage", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:build": "docker build -t command-center .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f app", "typeorm": "typeorm-ts-node-commonjs", "typeorm:migration:create": "typeorm migration:create", "typeorm:migration:generate": "typeorm-ts-node-commonjs migration:generate -d ./src/database/typeorm/data-source.ts", "typeorm:migration:run": "typeorm-ts-node-commonjs migration:run -d ./src/database/typeorm/data-source.ts", "typeorm:migration:revert": "typeorm-ts-node-commonjs migration:revert -d ./src/database/typeorm/data-source.ts", "typeorm:check": "typeorm-ts-node-commonjs query 'SELECT 1' -d ./src/database/typeorm/data-source.ts", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:migrate:reset": "prisma migrate reset", "prisma:studio": "prisma studio", "prisma:db:push": "prisma db push", "prisma:db:pull": "prisma db pull", "test:e2e:ui": "playwright test", "test:e2e:ui:debug": "playwright test --debug", "test:e2e:ui:headed": "playwright test --headed", "test:e2e:ui:report": "playwright show-report", "playwright:install": "playwright install"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@huggingface/inference": "^4.4.0", "@kubernetes/client-node": "^1.3.0", "@langchain/core": "^0.3.62", "@langchain/ollama": "^0.2.3", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.3", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/core": "^2.0.1", "@opentelemetry/exporter-trace-otlp-grpc": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0", "@prisma/client": "^5.8.1", "@qdrant/js-client-rest": "^1.14.1", "@types/dockerode": "^3.3.42", "@types/express-session": "^1.18.2", "@types/helmet": "^0.0.48", "@types/multer": "^2.0.0", "@types/uuid": "^10.0.0", "@xenova/transformers": "^2.17.2", "archiver": "^7.0.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cache-manager": "^7.0.1", "cache-manager-redis-store": "^3.0.1", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "connect-redis": "^9.0.0", "csv-parser": "^3.0.0", "dockerode": "^4.0.7", "docx": "^8.5.0", "dotenv": "^17.0.1", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "file-type": "^21.0.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.25.0", "jsdom": "^25.0.1", "json-schema": "^0.4.0", "langchain": "^0.3.29", "mailparser": "^3.7.1", "mammoth": "^1.9.1", "markdown-it": "^14.1.0", "mime-types": "^3.0.1", "minio": "^8.0.5", "multer": "^2.0.1", "node-clamav": "^1.0.11", "node-html-parser": "^6.1.13", "office-text-extractor": "^3.0.2", "ollama": "^0.5.16", "openai": "^5.8.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pdf2pic": "^3.1.3", "pdfjs-dist": "^4.8.69", "pg": "^8.16.3", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sharp": "^0.34.2", "socket.io": "^4.8.1", "tesseract.js": "^5.1.1", "turndown": "^7.2.0", "typeorm": "^0.3.25", "uuid": "^11.1.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "yaml": "^2.6.1", "zod": "^4.0.2"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/bcrypt": "^5.0.2", "@types/bull": "^3.15.9", "@types/cache-manager": "^4.0.6", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^29.7.0", "prettier": "^3.6.2", "prisma": "^5.8.1", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}