import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { getOptions, testConfig } from './k6.config.js';

// Custom metrics
const submissionCreationDuration = new Trend('submission_creation_duration');
const submissionUpdateDuration = new Trend('submission_update_duration');
const analyticsLoadDuration = new Trend('analytics_load_duration');
const digestGenerationDuration = new Trend('digest_generation_duration');

// Export test options
export const options = getOptions(__ENV.SCENARIO || 'load');

// Helper function to authenticate
function authenticate(email, password) {
  const res = http.post(
    `${testConfig.baseUrl}/auth/login`,
    JSON.stringify({ email, password }),
    { headers: testConfig.headers }
  );
  
  if (res.status === 200) {
    const tokens = JSON.parse(res.body).tokens;
    return {
      ...testConfig.headers,
      'Authorization': `Bearer ${tokens.accessToken}`,
    };
  }
  return null;
}

// Helper function to generate random wins data
function generateWinsData() {
  const categories = ['Technical', 'Process', 'Learning', 'Leadership', 'Other'];
  const impactLevels = ['high', 'medium', 'low'];
  
  return {
    achievements: [
      {
        title: `Achievement ${Date.now()}`,
        description: 'Load test achievement description',
        category: categories[Math.floor(Math.random() * categories.length)],
        impact: 'Load test impact statement'
      }
    ],
    recognitions: Math.random() > 0.5 ? [
      {
        recipientName: 'Test Recipient',
        recipientRole: 'Test Role',
        reason: 'Load test recognition',
        impactLevel: impactLevels[Math.floor(Math.random() * impactLevels.length)]
      }
    ] : [],
    costInitiatives: Math.random() > 0.7 ? [
      {
        description: 'Load test cost saving initiative',
        estimatedSavings: Math.floor(Math.random() * 10000) + 1000,
        implementationDate: new Date().toISOString(),
        category: 'Infrastructure'
      }
    ] : [],
    trainingIdeas: Math.random() > 0.8 ? [
      {
        title: 'Load test training idea',
        targetAudience: 'Test Team',
        expectedOutcome: 'Test outcome',
        estimatedCost: Math.floor(Math.random() * 5000) + 500
      }
    ] : [],
    progressUpdates: Math.random() > 0.6 ? [
      {
        projectName: 'Test Project',
        milestone: 'Test Milestone',
        completionPercentage: Math.floor(Math.random() * 100),
        nextSteps: 'Test next steps'
      }
    ] : [],
    weeklyHighlight: 'Load test weekly highlight',
    challenges: Math.random() > 0.5 ? 'Load test challenges' : '',
    nextWeekFocus: 'Load test next week focus'
  };
}

// Setup function
export function setup() {
  console.log('Setting up wins test data...');
  
  // Create test users in different departments
  const users = [];
  const managers = [];
  const departments = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'];
  
  // Create regular users
  for (let i = 0; i < 10; i++) {
    const userData = {
      email: `wins-user${i}@example.com`,
      password: 'TestPassword123!',
      department: departments[i % departments.length]
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        email: userData.email,
        password: userData.password,
        firstName: 'Wins',
        lastName: `User${i}`,
      }),
      { headers: testConfig.headers }
    );
    
    users.push(userData);
  }
  
  // Create managers
  for (let i = 0; i < 2; i++) {
    const managerData = {
      email: `wins-manager${i}@example.com`,
      password: 'TestPassword123!',
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        ...managerData,
        firstName: 'Wins',
        lastName: `Manager${i}`,
      }),
      { headers: testConfig.headers }
    );
    
    managers.push(managerData);
  }
  
  // Create some initial submissions
  const authHeaders = authenticate(users[0].email, users[0].password);
  const submissions = [];
  
  if (authHeaders) {
    // Create past week submission
    const weekStartDate = new Date();
    weekStartDate.setDate(weekStartDate.getDate() - 7);
    
    const res = http.post(
      `${testConfig.baseUrl}/wins/submissions`,
      JSON.stringify({
        userId: users[0].id,
        weekStartDate: weekStartDate.toISOString(),
        weekEndDate: new Date().toISOString(),
        ...generateWinsData()
      }),
      { headers: authHeaders }
    );
    
    if (res.status === 201) {
      submissions.push(JSON.parse(res.body));
    }
  }
  
  return { users, managers, submissions, departments };
}

// Main test function
export default function(data) {
  const isManager = Math.random() < 0.15; // 15% managers, 85% regular users
  const user = isManager 
    ? data.managers[Math.floor(Math.random() * data.managers.length)]
    : data.users[Math.floor(Math.random() * data.users.length)];
  
  const authHeaders = authenticate(user.email, user.password);
  if (!authHeaders) return;
  
  // Scenario 1: View dashboard and recent wins (80% of users)
  if (Math.random() < 0.8) {
    group('View Dashboard', () => {
      // Load dashboard
      const dashboardRes = http.get(
        `${testConfig.baseUrl}/wins/dashboard`,
        {
          headers: authHeaders,
          tags: { endpoint: 'wins-dashboard' },
        }
      );
      
      check(dashboardRes, {
        'dashboard loads successfully': (r) => r.status === 200,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // View recent submissions
      const recentRes = http.get(
        `${testConfig.baseUrl}/wins/submissions?limit=10&status=submitted`,
        {
          headers: authHeaders,
          tags: { endpoint: 'recent-submissions' },
        }
      );
      
      check(recentRes, {
        'recent submissions load': (r) => r.status === 200,
        'returns submission data': (r) => {
          const body = JSON.parse(r.body);
          return body.submissions && Array.isArray(body.submissions);
        },
      });
    });
  }
  
  // Scenario 2: Create/Update submission (60% of regular users)
  if (!isManager && Math.random() < 0.6) {
    group('Submission Management', () => {
      // Check if user has draft
      const draftsRes = http.get(
        `${testConfig.baseUrl}/wins/submissions?userId=${user.id}&status=draft`,
        {
          headers: authHeaders,
          tags: { endpoint: 'check-drafts' },
        }
      );
      
      const hasDraft = draftsRes.status === 200 && 
        JSON.parse(draftsRes.body).submissions.length > 0;
      
      if (hasDraft) {
        // Update existing draft
        const draft = JSON.parse(draftsRes.body).submissions[0];
        const updateStart = Date.now();
        
        const updateRes = http.put(
          `${testConfig.baseUrl}/wins/submissions/${draft.id}`,
          JSON.stringify({
            ...generateWinsData(),
            status: Math.random() > 0.5 ? 'submitted' : 'draft'
          }),
          {
            headers: authHeaders,
            tags: { endpoint: 'submission-update' },
          }
        );
        submissionUpdateDuration.add(Date.now() - updateStart);
        
        check(updateRes, {
          'submission update successful': (r) => r.status === 200,
        });
      } else {
        // Create new submission
        const weekStartDate = new Date();
        weekStartDate.setDate(weekStartDate.getDate() - 7);
        
        const createStart = Date.now();
        const createRes = http.post(
          `${testConfig.baseUrl}/wins/submissions`,
          JSON.stringify({
            userId: user.id,
            weekStartDate: weekStartDate.toISOString(),
            weekEndDate: new Date().toISOString(),
            ...generateWinsData()
          }),
          {
            headers: authHeaders,
            tags: { endpoint: 'submission-create' },
          }
        );
        submissionCreationDuration.add(Date.now() - createStart);
        
        check(createRes, {
          'submission creation successful or duplicate': (r) => 
            r.status === 201 || (r.status === 400 && r.body.includes('already exists')),
        });
      }
      
      sleep(Math.random() * 3 + 2);
    });
  }
  
  // Scenario 3: View team submissions (40% of users)
  if (Math.random() < 0.4) {
    group('Team View', () => {
      // Get team submissions
      const teamRes = http.get(
        `${testConfig.baseUrl}/wins/submissions?department=${user.department}&limit=20`,
        {
          headers: authHeaders,
          tags: { endpoint: 'team-submissions' },
        }
      );
      
      check(teamRes, {
        'team submissions load': (r) => r.status === 200,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // View specific team member submission
      if (teamRes.status === 200) {
        const submissions = JSON.parse(teamRes.body).submissions;
        if (submissions.length > 0) {
          const submission = submissions[Math.floor(Math.random() * submissions.length)];
          
          const detailRes = http.get(
            `${testConfig.baseUrl}/wins/submissions/${submission.id}`,
            {
              headers: authHeaders,
              tags: { endpoint: 'submission-detail' },
            }
          );
          
          check(detailRes, {
            'submission detail loads': (r) => r.status === 200,
          });
        }
      }
    });
  }
  
  // Scenario 4: View analytics (30% of users)
  if (Math.random() < 0.3) {
    group('Analytics', () => {
      const analyticsStart = Date.now();
      const analyticsRes = http.get(
        `${testConfig.baseUrl}/wins/analytics?period=month`,
        {
          headers: authHeaders,
          tags: { endpoint: 'wins-analytics' },
        }
      );
      analyticsLoadDuration.add(Date.now() - analyticsStart);
      
      check(analyticsRes, {
        'analytics load successfully': (r) => r.status === 200,
        'returns analytics data': (r) => {
          const body = JSON.parse(r.body);
          return body.totalSubmissions !== undefined && 
                 body.achievementsByCategory !== undefined;
        },
      });
      
      sleep(Math.random() * 2 + 1);
      
      // View leaderboard
      const leaderboardRes = http.get(
        `${testConfig.baseUrl}/wins/leaderboard?period=week`,
        {
          headers: authHeaders,
          tags: { endpoint: 'leaderboard' },
        }
      );
      
      check(leaderboardRes, {
        'leaderboard loads': (r) => r.status === 200,
      });
    });
  }
  
  // Scenario 5: Generate weekly digest (20% of users)
  if (Math.random() < 0.2) {
    group('Weekly Digest', () => {
      const digestStart = Date.now();
      const digestRes = http.get(
        `${testConfig.baseUrl}/wins/weekly-digest`,
        {
          headers: authHeaders,
          tags: { endpoint: 'weekly-digest' },
        }
      );
      digestGenerationDuration.add(Date.now() - digestStart);
      
      check(digestRes, {
        'digest generates successfully': (r) => r.status === 200,
        'returns digest data': (r) => {
          const body = JSON.parse(r.body);
          return body.weekStartDate && body.submissions && body.highlights;
        },
      });
    });
  }
  
  // Scenario 6: Manager approvals (managers only)
  if (isManager && Math.random() < 0.5) {
    group('Approval Workflow', () => {
      // Get pending approvals
      const pendingRes = http.get(
        `${testConfig.baseUrl}/wins/submissions?status=submitted&needsApproval=true`,
        {
          headers: authHeaders,
          tags: { endpoint: 'pending-approvals' },
        }
      );
      
      check(pendingRes, {
        'pending approvals load': (r) => r.status === 200,
      });
      
      // Approve a submission
      if (pendingRes.status === 200) {
        const pending = JSON.parse(pendingRes.body).submissions;
        if (pending.length > 0) {
          const submission = pending[0];
          
          const approveRes = http.put(
            `${testConfig.baseUrl}/wins/submissions/${submission.id}`,
            JSON.stringify({
              status: 'reviewed',
              reviewedBy: user.id,
              reviewNotes: 'Load test approval'
            }),
            {
              headers: authHeaders,
              tags: { endpoint: 'submission-approve' },
            }
          );
          
          check(approveRes, {
            'approval successful': (r) => r.status === 200,
          });
        }
      }
    });
  }
  
  // Scenario 7: Export data (10% of users)
  if (Math.random() < 0.1) {
    group('Data Export', () => {
      const exportRes = http.post(
        `${testConfig.baseUrl}/wins/export`,
        JSON.stringify({
          format: 'csv',
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString(),
          includeFields: ['achievements', 'recognitions', 'costInitiatives']
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'data-export' },
        }
      );
      
      check(exportRes, {
        'export initiated successfully': (r) => r.status === 201,
      });
    });
  }
  
  // Think time between iterations
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
}

// Teardown function
export function teardown(data) {
  console.log('Wins load test completed');
}