import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { getOptions, testConfig } from './k6.config.js';

// Custom metrics
const loginErrorRate = new Rate('login_errors');
const loginDuration = new Trend('login_duration');
const tokenRefreshDuration = new Trend('token_refresh_duration');

// Export test options
export const options = getOptions(__ENV.SCENARIO || 'load');

// Setup function - runs once before the test
export function setup() {
  console.log('Setting up test data...');
  
  // Register test users
  const users = [];
  for (const userData of testConfig.testUsers) {
    const res = http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        email: userData.email,
        password: userData.password,
        firstName: 'Performance',
        lastName: 'Test',
      }),
      { headers: testConfig.headers }
    );
    
    if (res.status === 201) {
      users.push(userData);
    }
  }
  
  return { users };
}

// Main test function - runs for each VU
export default function(data) {
  // Select a random user
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  // Test 1: Login
  const loginStart = Date.now();
  const loginRes = http.post(
    `${testConfig.baseUrl}/auth/login`,
    JSON.stringify({
      email: user.email,
      password: user.password,
    }),
    {
      headers: testConfig.headers,
      tags: { endpoint: 'login' },
    }
  );
  loginDuration.add(Date.now() - loginStart);
  
  const loginSuccess = check(loginRes, {
    'login successful': (r) => r.status === 200,
    'login returns tokens': (r) => {
      const body = JSON.parse(r.body);
      return body.tokens && body.tokens.accessToken && body.tokens.refreshToken;
    },
  });
  
  if (!loginSuccess) {
    loginErrorRate.add(1);
    return;
  }
  
  const tokens = JSON.parse(loginRes.body).tokens;
  const authHeaders = {
    ...testConfig.headers,
    'Authorization': `Bearer ${tokens.accessToken}`,
  };
  
  // Think time
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
  
  // Test 2: Access protected route
  const profileRes = http.get(
    `${testConfig.baseUrl}/auth/profile`,
    {
      headers: authHeaders,
      tags: { endpoint: 'profile' },
    }
  );
  
  check(profileRes, {
    'profile access successful': (r) => r.status === 200,
    'profile returns user data': (r) => {
      const body = JSON.parse(r.body);
      return body.email === user.email;
    },
  });
  
  // Think time
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
  
  // Test 3: Refresh token
  const refreshStart = Date.now();
  const refreshRes = http.post(
    `${testConfig.baseUrl}/auth/refresh`,
    JSON.stringify({
      refreshToken: tokens.refreshToken,
    }),
    {
      headers: testConfig.headers,
      tags: { endpoint: 'refresh' },
    }
  );
  tokenRefreshDuration.add(Date.now() - refreshStart);
  
  check(refreshRes, {
    'token refresh successful': (r) => r.status === 200,
    'refresh returns new tokens': (r) => {
      const body = JSON.parse(r.body);
      return body.tokens && body.tokens.accessToken;
    },
  });
  
  // Think time
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
  
  // Test 4: Logout
  const logoutRes = http.post(
    `${testConfig.baseUrl}/auth/logout`,
    JSON.stringify({}),
    {
      headers: authHeaders,
      tags: { endpoint: 'logout' },
    }
  );
  
  check(logoutRes, {
    'logout successful': (r) => r.status === 200,
  });
  
  // Test 5: Concurrent login attempts
  if (Math.random() < 0.1) { // 10% chance
    const responses = http.batch([
      ['POST', `${testConfig.baseUrl}/auth/login`, JSON.stringify({ email: user.email, password: user.password }), { headers: testConfig.headers }],
      ['POST', `${testConfig.baseUrl}/auth/login`, JSON.stringify({ email: user.email, password: user.password }), { headers: testConfig.headers }],
      ['POST', `${testConfig.baseUrl}/auth/login`, JSON.stringify({ email: user.email, password: user.password }), { headers: testConfig.headers }],
    ]);
    
    check(responses[0], {
      'concurrent login 1 successful': (r) => r.status === 200,
    });
  }
}

// Teardown function - runs once after the test
export function teardown(data) {
  console.log('Cleaning up test data...');
  
  // In a real scenario, you might want to delete test users
  // For now, we'll just log completion
  console.log('Test completed');
}