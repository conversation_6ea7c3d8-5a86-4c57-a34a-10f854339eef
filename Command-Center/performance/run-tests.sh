#!/bin/bash

# Load Testing Script for L&D Applications

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if k6 is installed
if ! command -v k6 &> /dev/null; then
    echo -e "${RED}k6 is not installed. Please install k6 first.${NC}"
    echo "Visit: https://k6.io/docs/getting-started/installation/"
    exit 1
fi

# Default values
BASE_URL="${BASE_URL:-http://localhost:3000}"
SCENARIO="${1:-smoke}"
TEST_MODULE="${2:-all}"

# Display usage
usage() {
    echo "Usage: $0 [scenario] [module]"
    echo ""
    echo "Scenarios:"
    echo "  smoke    - Quick test with minimal load (default)"
    echo "  load     - Standard load test"
    echo "  stress   - Find breaking point"
    echo "  spike    - Test sudden traffic spikes"
    echo "  soak     - Long duration stability test"
    echo ""
    echo "Modules:"
    echo "  all        - Run all tests (default)"
    echo "  auth       - Authentication tests only"
    echo "  training   - Training module tests only"
    echo "  vendors    - Vendors module tests only"
    echo "  wins       - Wins module tests only"
    echo "  integrated - Integrated user journey tests"
    echo ""
    echo "Environment Variables:"
    echo "  BASE_URL - API base URL (default: http://localhost:3000)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run smoke test for all modules"
    echo "  $0 load training      # Run load test for training module"
    echo "  $0 stress             # Run stress test for all modules"
}

# Validate scenario
case $SCENARIO in
    smoke|load|stress|spike|soak)
        ;;
    help|--help|-h)
        usage
        exit 0
        ;;
    *)
        echo -e "${RED}Invalid scenario: $SCENARIO${NC}"
        usage
        exit 1
        ;;
esac

# Create results directory
RESULTS_DIR="results/$(date +%Y%m%d_%H%M%S)_${SCENARIO}"
mkdir -p "$RESULTS_DIR"

# Function to run a test
run_test() {
    local test_name=$1
    local test_file=$2
    
    echo -e "${YELLOW}Running $test_name test (Scenario: $SCENARIO)...${NC}"
    
    # Run k6 test with HTML report
    k6 run \
        -e BASE_URL="$BASE_URL" \
        -e SCENARIO="$SCENARIO" \
        --out json="$RESULTS_DIR/${test_name}_results.json" \
        --summary-export="$RESULTS_DIR/${test_name}_summary.json" \
        "$test_file"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $test_name test completed successfully${NC}"
    else
        echo -e "${RED}✗ $test_name test failed${NC}"
    fi
    
    echo ""
}

# Function to generate HTML report
generate_report() {
    echo -e "${YELLOW}Generating HTML report...${NC}"
    
    # Create index.html with results
    cat > "$RESULTS_DIR/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Load Test Results - $SCENARIO - $(date)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .module { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { margin: 10px 0; }
        .pass { color: green; }
        .fail { color: red; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Load Test Results</h1>
    <p><strong>Scenario:</strong> $SCENARIO</p>
    <p><strong>Date:</strong> $(date)</p>
    <p><strong>Base URL:</strong> $BASE_URL</p>
    
    <h2>Test Modules</h2>
EOF

    # Add results for each module
    for summary_file in "$RESULTS_DIR"/*_summary.json; do
        if [ -f "$summary_file" ]; then
            module_name=$(basename "$summary_file" _summary.json)
            echo "<div class='module'>" >> "$RESULTS_DIR/index.html"
            echo "<h3>$module_name</h3>" >> "$RESULTS_DIR/index.html"
            
            # Parse and display key metrics using jq if available
            if command -v jq &> /dev/null; then
                echo "<table>" >> "$RESULTS_DIR/index.html"
                echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>" >> "$RESULTS_DIR/index.html"
                
                # Extract key metrics
                jq -r '.metrics | to_entries | .[] | 
                    select(.key | test("http_req_duration|http_req_failed|checks")) | 
                    "<tr><td>\(.key)</td><td>\(.value.values | if . then . else empty end)</td><td>-</td></tr>"' \
                    "$summary_file" >> "$RESULTS_DIR/index.html" 2>/dev/null || true
                
                echo "</table>" >> "$RESULTS_DIR/index.html"
            else
                echo "<pre>$(cat "$summary_file")</pre>" >> "$RESULTS_DIR/index.html"
            fi
            
            echo "</div>" >> "$RESULTS_DIR/index.html"
        fi
    done

    cat >> "$RESULTS_DIR/index.html" << EOF
</body>
</html>
EOF

    echo -e "${GREEN}HTML report generated: $RESULTS_DIR/index.html${NC}"
}

# Start testing
echo -e "${GREEN}Starting L&D Load Tests${NC}"
echo "================================"
echo "Scenario: $SCENARIO"
echo "Module: $TEST_MODULE"
echo "Base URL: $BASE_URL"
echo "Results: $RESULTS_DIR"
echo "================================"
echo ""

# Run tests based on module selection
case $TEST_MODULE in
    all)
        run_test "auth" "auth.load.js"
        run_test "training" "training.load.js"
        run_test "vendors" "vendors.load.js"
        run_test "wins" "wins.load.js"
        run_test "integrated" "integrated.load.js"
        ;;
    auth)
        run_test "auth" "auth.load.js"
        ;;
    training)
        run_test "training" "training.load.js"
        ;;
    vendors)
        run_test "vendors" "vendors.load.js"
        ;;
    wins)
        run_test "wins" "wins.load.js"
        ;;
    integrated)
        run_test "integrated" "integrated.load.js"
        ;;
    *)
        echo -e "${RED}Invalid module: $TEST_MODULE${NC}"
        usage
        exit 1
        ;;
esac

# Generate report
generate_report

# Display summary
echo ""
echo -e "${GREEN}Load testing completed!${NC}"
echo "================================"
echo "Results saved to: $RESULTS_DIR"
echo "View report: open $RESULTS_DIR/index.html"
echo ""

# Check if any tests failed
if grep -q "✗" "$RESULTS_DIR"/*.json 2>/dev/null; then
    echo -e "${RED}Some tests failed. Please check the results.${NC}"
    exit 1
else
    echo -e "${GREEN}All tests passed!${NC}"
    exit 0
fi