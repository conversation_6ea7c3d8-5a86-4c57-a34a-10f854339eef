# Performance Testing for L&D Applications

This directory contains performance and load testing scripts using k6 for the L&D applications suite.

## Overview

The performance tests simulate realistic user behavior across all three L&D applications:
- Training Need Analysis
- Vendor Management
- Wins of Week

## Test Scenarios

### 1. Smoke Test
- **Duration**: 1 minute
- **VUs (Virtual Users)**: 1
- **Purpose**: Verify system works under minimal load

### 2. Load Test
- **Duration**: 16 minutes
- **VUs**: Ramps from 0 → 10 → 20 → 0
- **Purpose**: Assess performance under normal expected load

### 3. Stress Test
- **Duration**: 28 minutes
- **VUs**: Ramps from 0 → 20 → 50 → 100 → 200 → 0
- **Purpose**: Find system breaking point

### 4. Spike Test
- **Duration**: ~10 minutes
- **VUs**: Sudden spike from 5 → 100 users
- **Purpose**: Assess performance under sudden traffic spikes

### 5. Soak Test
- **Duration**: 2 hours
- **VUs**: Constant 50 users
- **Purpose**: Verify system stability over extended period

## Test Modules

### Authentication (`auth.load.js`)
Tests authentication workflows:
- User login/logout
- Token refresh
- Profile access
- Concurrent login attempts

### Training (`training.load.js`)
Tests training management:
- Course browsing and search
- Course enrollment
- Assessment completion
- Recommendation viewing
- Course creation (managers)

### Vendors (`vendors.load.js`)
Tests vendor management:
- Vendor browsing and search
- Vendor registration
- Proposal creation
- Review submission
- Vendor comparison
- Analytics viewing

### Wins (`wins.load.js`)
Tests wins submission:
- Dashboard viewing
- Submission creation/update
- Team submissions
- Analytics and leaderboard
- Weekly digest generation
- Approval workflow

### Integrated (`integrated.load.js`)
Simulates realistic user journeys across all applications with three personas:
- **Employee** (60%): Regular users creating submissions, enrolling in courses
- **Manager** (25%): Reviewing teams, approving submissions, viewing analytics
- **Admin** (15%): Managing vendors, generating reports, system configuration

## Prerequisites

1. Install k6:
```bash
# macOS
brew install k6

# Linux
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6

# Windows
choco install k6
```

2. Ensure all services are running:
```bash
# Backend API
npm run start:dev

# Or use Docker
docker-compose up -d
```

## Running Tests

### Quick Start
```bash
# Run smoke test for all modules
./run-tests.sh

# Run load test for specific module
./run-tests.sh load training

# Run stress test for all modules
./run-tests.sh stress
```

### Manual Execution
```bash
# Run specific test with custom options
k6 run -e BASE_URL=http://localhost:3000 -e SCENARIO=load auth.load.js

# Run with custom VUs and duration
k6 run --vus 10 --duration 30s auth.load.js

# Run with output to InfluxDB
k6 run --out influxdb=http://localhost:8086/k6 training.load.js
```

### Environment Variables
- `BASE_URL`: API base URL (default: `http://localhost:3000`)
- `SCENARIO`: Test scenario to run (smoke, load, stress, spike, soak)

## Performance Thresholds

Default thresholds configured in `k6.config.js`:

| Metric | Threshold | Description |
|--------|-----------|-------------|
| `http_req_failed` | < 1% | HTTP error rate |
| `http_req_duration` | p(95) < 500ms | 95% of requests under 500ms |
| `http_req_duration{endpoint:login}` | p(95) < 200ms | Login endpoint performance |
| `http_req_duration{endpoint:courses}` | p(95) < 300ms | Courses endpoint performance |
| `checks` | > 99% | Check success rate |

## Test Results

Results are saved in `results/` directory with timestamp:
```
results/
└── 20240115_143022_load/
    ├── auth_results.json
    ├── auth_summary.json
    ├── training_results.json
    ├── training_summary.json
    ├── vendors_results.json
    ├── vendors_summary.json
    ├── wins_results.json
    ├── wins_summary.json
    ├── integrated_results.json
    ├── integrated_summary.json
    └── index.html
```

View HTML report:
```bash
open results/20240115_143022_load/index.html
```

## Analyzing Results

### Key Metrics to Monitor

1. **Response Times**
   - `http_req_duration`: Overall response time
   - `http_req_waiting`: Time to first byte
   - `http_req_connecting`: Connection time

2. **Throughput**
   - `http_reqs`: Total requests per second
   - `data_received`: Data transfer rate
   - `data_sent`: Upload rate

3. **Error Rates**
   - `http_req_failed`: Failed requests percentage
   - `checks`: Custom check failure rate

4. **Custom Metrics**
   - `login_duration`: Authentication performance
   - `course_creation_duration`: Course creation time
   - `submission_creation_duration`: Wins submission time
   - `user_journey_duration`: Complete user workflow time

### Using Grafana Dashboard

1. Start monitoring stack:
```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

2. Configure k6 to output to InfluxDB:
```bash
k6 run --out influxdb=http://localhost:8086/k6 integrated.load.js
```

3. Access Grafana: http://localhost:3000
   - Username: admin
   - Password: admin

4. Import k6 dashboard (ID: 2587)

## Best Practices

1. **Test Data Management**
   - Tests create their own test users in setup()
   - Cleanup is handled in teardown()
   - Use unique identifiers to avoid conflicts

2. **Realistic Scenarios**
   - Think time between requests (1-3 seconds)
   - Persona-based behavior patterns
   - Probability-based activity execution

3. **Progressive Testing**
   - Start with smoke tests
   - Move to load tests
   - Only run stress tests when needed
   - Use soak tests before major releases

4. **Monitoring During Tests**
   - Watch application logs
   - Monitor system resources (CPU, memory, disk)
   - Check database connection pool
   - Monitor external service dependencies

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure backend is running
   - Check BASE_URL environment variable
   - Verify firewall settings

2. **High Error Rate**
   - Check if test data setup completed
   - Verify authentication is working
   - Look for rate limiting

3. **Timeout Errors**
   - Increase timeout in test options
   - Check database query performance
   - Monitor network latency

### Debug Mode

Run tests with extended logging:
```bash
k6 run --http-debug auth.load.js
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Performance Tests

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Start services
        run: docker-compose up -d
        
      - name: Wait for services
        run: sleep 30
        
      - name: Run load tests
        run: |
          docker run --rm \
            --network host \
            -v $PWD/performance:/scripts \
            loadimpact/k6 run \
            -e BASE_URL=http://localhost:3000 \
            -e SCENARIO=load \
            /scripts/integrated.load.js
            
      - name: Upload results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance/results/
```

## Performance Optimization Tips

Based on load test results, consider:

1. **Database Optimization**
   - Add missing indexes
   - Optimize slow queries
   - Implement query result caching

2. **Application Level**
   - Implement request caching
   - Use pagination for large datasets
   - Optimize N+1 query problems

3. **Infrastructure**
   - Scale horizontally with load balancer
   - Implement Redis caching layer
   - Use CDN for static assets

4. **API Design**
   - Implement GraphQL for flexible queries
   - Use batch endpoints
   - Add response compression

## Reporting Issues

When reporting performance issues:

1. Include test scenario and configuration
2. Attach relevant result files
3. Note system specifications
4. Include application logs during test
5. Specify expected vs actual performance