// K6 Configuration for Load Testing

export const scenarios = {
  // Smoke test: Verify system works under minimal load
  smoke: {
    executor: 'constant-vus',
    vus: 1,
    duration: '1m',
  },

  // Load test: Assess performance under normal expected load
  load: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '2m', target: 10 }, // Ramp up to 10 users
      { duration: '5m', target: 10 }, // Stay at 10 users
      { duration: '2m', target: 20 }, // Ramp up to 20 users
      { duration: '5m', target: 20 }, // Stay at 20 users
      { duration: '2m', target: 0 },  // Ramp down to 0 users
    ],
    gracefulRampDown: '30s',
  },

  // Stress test: Find breaking point
  stress: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '2m', target: 20 },
      { duration: '5m', target: 20 },
      { duration: '2m', target: 50 },
      { duration: '5m', target: 50 },
      { duration: '2m', target: 100 },
      { duration: '5m', target: 100 },
      { duration: '2m', target: 200 },
      { duration: '5m', target: 200 },
      { duration: '5m', target: 0 },
    ],
    gracefulRampDown: '30s',
  },

  // Spike test: Assess performance under sudden traffic spikes
  spike: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '10s', target: 5 },
      { duration: '1m', target: 5 },
      { duration: '10s', target: 100 }, // Spike to 100 users
      { duration: '3m', target: 100 },  // Stay at 100 users
      { duration: '10s', target: 5 },   // Scale down
      { duration: '3m', target: 5 },
      { duration: '10s', target: 0 },
    ],
    gracefulRampDown: '30s',
  },

  // Soak test: Verify system stability over extended period
  soak: {
    executor: 'constant-vus',
    vus: 50,
    duration: '2h',
  },
};

// Performance thresholds
export const thresholds = {
  // HTTP errors should be less than 1%
  http_req_failed: ['rate<0.01'],
  
  // 95% of requests should complete within 500ms
  http_req_duration: ['p(95)<500'],
  
  // Specific endpoint thresholds
  'http_req_duration{endpoint:login}': ['p(95)<200'],
  'http_req_duration{endpoint:courses}': ['p(95)<300'],
  'http_req_duration{endpoint:vendors}': ['p(95)<250'],
  'http_req_duration{endpoint:submissions}': ['p(95)<400'],
  
  // Checks should pass 99% of the time
  checks: ['rate>0.99'],
  
  // Iteration duration
  iteration_duration: ['p(95)<1000'],
};

// Test configuration
export const testConfig = {
  // API endpoints
  baseUrl: __ENV.BASE_URL || 'http://localhost:3000',
  
  // Test data
  testUsers: [
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
  ],
  
  // Headers
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Think time between requests (in seconds)
  thinkTime: {
    min: 1,
    max: 3,
  },
};

// Export options based on scenario
export function getOptions(scenario) {
  return {
    scenarios: {
      [scenario]: scenarios[scenario],
    },
    thresholds: thresholds,
    setupTimeout: '10m',
    teardownTimeout: '5m',
  };
}