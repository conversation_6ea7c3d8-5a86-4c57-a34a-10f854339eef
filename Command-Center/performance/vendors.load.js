import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { getOptions, testConfig } from './k6.config.js';

// Custom metrics
const vendorCreationDuration = new Trend('vendor_creation_duration');
const proposalCreationDuration = new Trend('proposal_creation_duration');
const vendorSearchDuration = new Trend('vendor_search_duration');
const reviewSubmissionDuration = new Trend('review_submission_duration');

// Export test options
export const options = getOptions(__ENV.SCENARIO || 'load');

// Helper function to authenticate
function authenticate(email, password) {
  const res = http.post(
    `${testConfig.baseUrl}/auth/login`,
    JSON.stringify({ email, password }),
    { headers: testConfig.headers }
  );
  
  if (res.status === 200) {
    const tokens = JSON.parse(res.body).tokens;
    return {
      ...testConfig.headers,
      'Authorization': `Bearer ${tokens.accessToken}`,
    };
  }
  return null;
}

// Setup function
export function setup() {
  console.log('Setting up vendor test data...');
  
  // Create test users
  const users = [];
  const managers = [];
  
  // Create vendor managers
  for (let i = 0; i < 3; i++) {
    const userData = {
      email: `vendor-manager${i}@example.com`,
      password: 'TestPassword123!',
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        ...userData,
        firstName: 'Vendor',
        lastName: `Manager${i}`,
      }),
      { headers: testConfig.headers }
    );
    
    managers.push(userData);
  }
  
  // Create regular users
  for (let i = 0; i < 5; i++) {
    const userData = {
      email: `vendor-user${i}@example.com`,
      password: 'TestPassword123!',
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        ...userData,
        firstName: 'Vendor',
        lastName: `User${i}`,
      }),
      { headers: testConfig.headers }
    );
    
    users.push(userData);
  }
  
  // Create initial vendors
  const authHeaders = authenticate(managers[0].email, managers[0].password);
  const vendors = [];
  
  if (authHeaders) {
    const vendorCategories = ['Training', 'Assessment', 'Certification', 'Content', 'Consulting'];
    
    for (let i = 0; i < 5; i++) {
      const res = http.post(
        `${testConfig.baseUrl}/vendors`,
        JSON.stringify({
          companyName: `Test Vendor ${i}`,
          contactPerson: `Contact ${i}`,
          phoneNumbers: [
            { type: 'primary', number: `+*********${i}` }
          ],
          email: `vendor${i}@example.com`,
          website: `https://vendor${i}.com`,
          category: vendorCategories[i % vendorCategories.length],
          certifications: ['ISO 9001', 'ISO 27001'],
        }),
        { headers: authHeaders }
      );
      
      if (res.status === 201) {
        vendors.push(JSON.parse(res.body));
      }
    }
  }
  
  return { users, managers, vendors };
}

// Main test function
export default function(data) {
  const isManager = Math.random() < 0.3; // 30% managers, 70% regular users
  const user = isManager 
    ? data.managers[Math.floor(Math.random() * data.managers.length)]
    : data.users[Math.floor(Math.random() * data.users.length)];
  
  const authHeaders = authenticate(user.email, user.password);
  if (!authHeaders) return;
  
  // Scenario 1: Browse and search vendors (70% of users)
  if (Math.random() < 0.7) {
    group('Browse Vendors', () => {
      // List vendors
      const searchStart = Date.now();
      const listRes = http.get(
        `${testConfig.baseUrl}/vendors?page=1&limit=10`,
        {
          headers: authHeaders,
          tags: { endpoint: 'vendors' },
        }
      );
      vendorSearchDuration.add(Date.now() - searchStart);
      
      check(listRes, {
        'vendor list successful': (r) => r.status === 200,
        'returns vendor data': (r) => {
          const body = JSON.parse(r.body);
          return body.vendors && Array.isArray(body.vendors);
        },
      });
      
      sleep(Math.random() * 2 + 1);
      
      // Search by category
      const categories = ['Training', 'Assessment', 'Certification'];
      const category = categories[Math.floor(Math.random() * categories.length)];
      
      const categoryRes = http.get(
        `${testConfig.baseUrl}/vendors?category=${category}`,
        {
          headers: authHeaders,
          tags: { endpoint: 'vendors-category' },
        }
      );
      
      check(categoryRes, {
        'category filter successful': (r) => r.status === 200,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // View vendor details
      if (data.vendors.length > 0) {
        const vendor = data.vendors[Math.floor(Math.random() * data.vendors.length)];
        const detailRes = http.get(
          `${testConfig.baseUrl}/vendors/${vendor.id}`,
          {
            headers: authHeaders,
            tags: { endpoint: 'vendor-detail' },
          }
        );
        
        check(detailRes, {
          'vendor detail successful': (r) => r.status === 200,
        });
      }
    });
  }
  
  // Scenario 2: Create vendor (managers only, 20%)
  if (isManager && Math.random() < 0.2) {
    group('Vendor Management', () => {
      const vendorStart = Date.now();
      const newVendorRes = http.post(
        `${testConfig.baseUrl}/vendors`,
        JSON.stringify({
          companyName: `Load Test Vendor ${Date.now()}`,
          contactPerson: 'Load Test Contact',
          phoneNumbers: [
            { type: 'primary', number: '+*********0' }
          ],
          email: `loadtest${Date.now()}@vendor.com`,
          category: 'Training',
          certifications: ['Test Cert'],
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'vendor-create' },
        }
      );
      vendorCreationDuration.add(Date.now() - vendorStart);
      
      const vendorCreated = check(newVendorRes, {
        'vendor creation successful': (r) => r.status === 201,
      });
      
      if (vendorCreated) {
        const newVendor = JSON.parse(newVendorRes.body);
        data.vendors.push(newVendor);
      }
    });
  }
  
  // Scenario 3: Create proposal (30% of users with vendors)
  if (Math.random() < 0.3 && data.vendors.length > 0) {
    group('Proposal Management', () => {
      const vendor = data.vendors[Math.floor(Math.random() * data.vendors.length)];
      
      const proposalStart = Date.now();
      const proposalRes = http.post(
        `${testConfig.baseUrl}/vendors/${vendor.id}/proposals`,
        JSON.stringify({
          title: `Load Test Proposal ${Date.now()}`,
          description: 'Proposal created during load testing',
          costs: [
            { item: 'Service 1', amount: 1000, quantity: 5 },
            { item: 'Service 2', amount: 2000, quantity: 2 }
          ],
          totalCost: 9000,
          validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
          attachments: [],
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'proposal-create' },
        }
      );
      proposalCreationDuration.add(Date.now() - proposalStart);
      
      check(proposalRes, {
        'proposal creation successful': (r) => r.status === 201,
      });
      
      sleep(Math.random() * 3 + 2);
      
      // List proposals
      const proposalsRes = http.get(
        `${testConfig.baseUrl}/vendors/proposals?vendorId=${vendor.id}`,
        {
          headers: authHeaders,
          tags: { endpoint: 'proposals-list' },
        }
      );
      
      check(proposalsRes, {
        'proposals list successful': (r) => r.status === 200,
      });
    });
  }
  
  // Scenario 4: Submit review (40% of users)
  if (Math.random() < 0.4 && data.vendors.length > 0) {
    group('Vendor Reviews', () => {
      const vendor = data.vendors[Math.floor(Math.random() * data.vendors.length)];
      
      const reviewStart = Date.now();
      const reviewRes = http.post(
        `${testConfig.baseUrl}/vendors/${vendor.id}/reviews`,
        JSON.stringify({
          rating: Math.floor(Math.random() * 3) + 3, // 3-5 rating
          reviewerName: user.firstName + ' ' + user.lastName,
          reviewerRole: 'Test Role',
          comment: 'Load test review comment',
          strengths: ['Good service', 'Professional'],
          improvements: ['Could be faster'],
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'review-submit' },
        }
      );
      reviewSubmissionDuration.add(Date.now() - reviewStart);
      
      check(reviewRes, {
        'review submission successful': (r) => r.status === 201,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // Get vendor reviews
      const reviewsRes = http.get(
        `${testConfig.baseUrl}/vendors/reviews?vendorId=${vendor.id}`,
        {
          headers: authHeaders,
          tags: { endpoint: 'reviews-list' },
        }
      );
      
      check(reviewsRes, {
        'reviews list successful': (r) => r.status === 200,
      });
    });
  }
  
  // Scenario 5: Compare vendors (20% of users)
  if (Math.random() < 0.2 && data.vendors.length >= 2) {
    group('Vendor Comparison', () => {
      const vendorIds = data.vendors
        .slice(0, 3)
        .map(v => v.id);
      
      const compareRes = http.post(
        `${testConfig.baseUrl}/vendors/compare`,
        JSON.stringify({ vendorIds }),
        {
          headers: authHeaders,
          tags: { endpoint: 'vendor-compare' },
        }
      );
      
      check(compareRes, {
        'vendor comparison successful': (r) => r.status === 201,
      });
    });
  }
  
  // Scenario 6: Get analytics (managers only, 30%)
  if (isManager && Math.random() < 0.3) {
    group('Vendor Analytics', () => {
      const analyticsRes = http.get(
        `${testConfig.baseUrl}/vendors/analytics`,
        {
          headers: authHeaders,
          tags: { endpoint: 'vendor-analytics' },
        }
      );
      
      check(analyticsRes, {
        'analytics successful': (r) => r.status === 200,
        'returns analytics data': (r) => {
          const body = JSON.parse(r.body);
          return body.totalVendors !== undefined && body.activeVendors !== undefined;
        },
      });
    });
  }
  
  // Think time between iterations
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
}

// Teardown function
export function teardown(data) {
  console.log('Vendors load test completed');
}