import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { getOptions, testConfig } from './k6.config.js';

// Custom metrics
const apiErrors = new Rate('api_errors');
const apiLatency = new Trend('api_latency');
const userJourneyDuration = new Trend('user_journey_duration');
const crossAppNavigations = new Counter('cross_app_navigations');

// Export test options with custom thresholds
export const options = {
  ...getOptions(__ENV.SCENARIO || 'load'),
  thresholds: {
    ...getOptions(__ENV.SCENARIO || 'load').thresholds,
    'user_journey_duration': ['p(95)<10000'], // 95% of user journeys complete within 10s
    'cross_app_navigations': ['count>100'], // At least 100 cross-app navigations
  }
};

// Helper functions
function authenticate(email, password) {
  const res = http.post(
    `${testConfig.baseUrl}/auth/login`,
    JSON.stringify({ email, password }),
    { headers: testConfig.headers }
  );
  
  if (res.status === 200) {
    const tokens = JSON.parse(res.body).tokens;
    return {
      tokens,
      headers: {
        ...testConfig.headers,
        'Authorization': `Bearer ${tokens.accessToken}`,
      }
    };
  }
  return null;
}

// User personas with realistic behavior patterns
const userPersonas = {
  employee: {
    weight: 0.6, // 60% of users
    activities: [
      { name: 'checkDashboard', probability: 0.9 },
      { name: 'viewTrainingRecommendations', probability: 0.7 },
      { name: 'createWinsSubmission', probability: 0.8 },
      { name: 'browseVendors', probability: 0.3 },
      { name: 'enrollInCourse', probability: 0.4 },
      { name: 'completeAssessment', probability: 0.3 },
    ]
  },
  manager: {
    weight: 0.25, // 25% of users
    activities: [
      { name: 'reviewTeamSubmissions', probability: 0.9 },
      { name: 'viewAnalytics', probability: 0.8 },
      { name: 'createTrainingCourse', probability: 0.3 },
      { name: 'evaluateVendor', probability: 0.5 },
      { name: 'approveSubmissions', probability: 0.7 },
    ]
  },
  admin: {
    weight: 0.15, // 15% of users
    activities: [
      { name: 'manageVendors', probability: 0.8 },
      { name: 'generateReports', probability: 0.6 },
      { name: 'configureTraining', probability: 0.5 },
      { name: 'viewSystemMetrics', probability: 0.9 },
    ]
  }
};

// Activity implementations
const activities = {
  checkDashboard: (auth) => {
    const res = http.get(`${testConfig.baseUrl}/dashboard`, {
      headers: auth.headers,
      tags: { activity: 'checkDashboard' }
    });
    check(res, { 'dashboard loads': (r) => r.status === 200 });
    return res.status === 200;
  },

  viewTrainingRecommendations: (auth, userId) => {
    const res = http.get(`${testConfig.baseUrl}/training/recommendations/${userId}`, {
      headers: auth.headers,
      tags: { activity: 'viewTrainingRecommendations' }
    });
    check(res, { 'recommendations load': (r) => r.status === 200 });
    crossAppNavigations.add(1);
    return res.status === 200;
  },

  createWinsSubmission: (auth, userId) => {
    const weekStartDate = new Date();
    weekStartDate.setDate(weekStartDate.getDate() - 7);
    
    const res = http.post(
      `${testConfig.baseUrl}/wins/submissions`,
      JSON.stringify({
        userId,
        weekStartDate: weekStartDate.toISOString(),
        weekEndDate: new Date().toISOString(),
        achievements: [{
          title: 'Weekly achievement',
          description: 'Integrated load test achievement',
          category: 'Technical',
          impact: 'Improved system performance'
        }],
        recognitions: [],
        weeklyHighlight: 'Completed load testing setup'
      }),
      {
        headers: auth.headers,
        tags: { activity: 'createWinsSubmission' }
      }
    );
    
    check(res, { 
      'submission created or exists': (r) => 
        r.status === 201 || r.status === 400 
    });
    crossAppNavigations.add(1);
    return res.status === 201;
  },

  browseVendors: (auth) => {
    const res = http.get(`${testConfig.baseUrl}/vendors?category=Training&limit=10`, {
      headers: auth.headers,
      tags: { activity: 'browseVendors' }
    });
    check(res, { 'vendors load': (r) => r.status === 200 });
    crossAppNavigations.add(1);
    return res.status === 200;
  },

  enrollInCourse: (auth, userId, courseId) => {
    const res = http.post(
      `${testConfig.baseUrl}/training/enrollments`,
      JSON.stringify({ userId, courseId }),
      {
        headers: auth.headers,
        tags: { activity: 'enrollInCourse' }
      }
    );
    
    check(res, { 
      'enrollment successful or already enrolled': (r) => 
        r.status === 201 || r.status === 400 
    });
    return res.status === 201;
  },

  completeAssessment: (auth, userId) => {
    const res = http.post(
      `${testConfig.baseUrl}/training/assessments/complete`,
      JSON.stringify({
        assessmentId: 'skill-assessment-1',
        userId,
        skillRatings: [
          { skillId: 'skill-1', rating: 4 },
          { skillId: 'skill-2', rating: 3 },
        ]
      }),
      {
        headers: auth.headers,
        tags: { activity: 'completeAssessment' }
      }
    );
    
    check(res, { 'assessment completed': (r) => r.status === 201 || r.status === 400 });
    return res.status === 201;
  },

  reviewTeamSubmissions: (auth) => {
    const res = http.get(`${testConfig.baseUrl}/wins/submissions?status=submitted&limit=10`, {
      headers: auth.headers,
      tags: { activity: 'reviewTeamSubmissions' }
    });
    check(res, { 'team submissions load': (r) => r.status === 200 });
    crossAppNavigations.add(1);
    return res.status === 200;
  },

  viewAnalytics: (auth) => {
    const requests = http.batch([
      ['GET', `${testConfig.baseUrl}/training/metrics`, null, { headers: auth.headers }],
      ['GET', `${testConfig.baseUrl}/vendors/analytics`, null, { headers: auth.headers }],
      ['GET', `${testConfig.baseUrl}/wins/analytics`, null, { headers: auth.headers }],
    ]);
    
    requests.forEach(res => {
      check(res, { 'analytics load': (r) => r.status === 200 });
    });
    crossAppNavigations.add(3);
    return requests.every(r => r.status === 200);
  },

  createTrainingCourse: (auth) => {
    const res = http.post(
      `${testConfig.baseUrl}/training/courses`,
      JSON.stringify({
        title: `Integrated Test Course ${Date.now()}`,
        description: 'Course created during integrated load test',
        provider: 'Internal',
        type: 'workshop',
        duration: 8,
        cost: 0,
        difficulty: 'intermediate'
      }),
      {
        headers: auth.headers,
        tags: { activity: 'createTrainingCourse' }
      }
    );
    
    check(res, { 'course created': (r) => r.status === 201 });
    return res.status === 201;
  },

  evaluateVendor: (auth, vendorId) => {
    const res = http.post(
      `${testConfig.baseUrl}/vendors/${vendorId}/reviews`,
      JSON.stringify({
        rating: 4,
        reviewerName: 'Load Test Manager',
        reviewerRole: 'Manager',
        comment: 'Good vendor performance',
        strengths: ['Reliable', 'Professional'],
        improvements: ['Communication']
      }),
      {
        headers: auth.headers,
        tags: { activity: 'evaluateVendor' }
      }
    );
    
    check(res, { 'review submitted': (r) => r.status === 201 });
    return res.status === 201;
  },

  approveSubmissions: (auth) => {
    const pendingRes = http.get(
      `${testConfig.baseUrl}/wins/submissions?status=submitted&limit=5`,
      { headers: auth.headers }
    );
    
    if (pendingRes.status === 200) {
      const submissions = JSON.parse(pendingRes.body).submissions;
      if (submissions.length > 0) {
        const submission = submissions[0];
        const approveRes = http.put(
          `${testConfig.baseUrl}/wins/submissions/${submission.id}`,
          JSON.stringify({ status: 'reviewed' }),
          {
            headers: auth.headers,
            tags: { activity: 'approveSubmissions' }
          }
        );
        check(approveRes, { 'submission approved': (r) => r.status === 200 });
      }
    }
    return true;
  },

  manageVendors: (auth) => {
    const res = http.post(
      `${testConfig.baseUrl}/vendors`,
      JSON.stringify({
        companyName: `Test Vendor ${Date.now()}`,
        contactPerson: 'Admin Test',
        phoneNumbers: [{ type: 'primary', number: '+**********' }],
        email: `vendor${Date.now()}@test.com`,
        category: 'Training'
      }),
      {
        headers: auth.headers,
        tags: { activity: 'manageVendors' }
      }
    );
    
    check(res, { 'vendor created': (r) => r.status === 201 });
    crossAppNavigations.add(1);
    return res.status === 201;
  },

  generateReports: (auth) => {
    const res = http.post(
      `${testConfig.baseUrl}/reports/generate`,
      JSON.stringify({
        type: 'training-overview',
        period: 'month',
        format: 'pdf'
      }),
      {
        headers: auth.headers,
        tags: { activity: 'generateReports' }
      }
    );
    
    check(res, { 'report generated': (r) => r.status === 201 || r.status === 202 });
    return res.status === 201 || res.status === 202;
  },

  configureTraining: (auth) => {
    const res = http.put(
      `${testConfig.baseUrl}/training/configuration`,
      JSON.stringify({
        assessmentFrequency: 'quarterly',
        recommendationEngine: 'enabled',
        autoEnrollment: true
      }),
      {
        headers: auth.headers,
        tags: { activity: 'configureTraining' }
      }
    );
    
    check(res, { 'configuration updated': (r) => r.status === 200 });
    return res.status === 200;
  },

  viewSystemMetrics: (auth) => {
    const res = http.get(`${testConfig.baseUrl}/system/metrics`, {
      headers: auth.headers,
      tags: { activity: 'viewSystemMetrics' }
    });
    check(res, { 'metrics load': (r) => r.status === 200 });
    return res.status === 200;
  }
};

// Setup function
export function setup() {
  console.log('Setting up integrated load test...');
  
  const testData = {
    users: [],
    courses: [],
    vendors: []
  };
  
  // Create test users for each persona
  const personas = ['employee', 'manager', 'admin'];
  for (let i = 0; i < 10; i++) {
    const persona = personas[i % 3];
    const userData = {
      email: `${persona}${i}@loadtest.com`,
      password: 'TestPassword123!',
      persona: persona,
      id: `user-${i}`
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        email: userData.email,
        password: userData.password,
        firstName: persona,
        lastName: `User${i}`
      }),
      { headers: testConfig.headers }
    );
    
    testData.users.push(userData);
  }
  
  // Create initial test data
  const auth = authenticate(testData.users[1].email, testData.users[1].password);
  if (auth) {
    // Create a course
    const courseRes = http.post(
      `${testConfig.baseUrl}/training/courses`,
      JSON.stringify({
        title: 'Load Test Course',
        description: 'Initial course for testing',
        provider: 'Internal',
        type: 'online',
        duration: 8,
        cost: 0,
        difficulty: 'beginner'
      }),
      { headers: auth.headers }
    );
    
    if (courseRes.status === 201) {
      testData.courses.push(JSON.parse(courseRes.body));
    }
    
    // Create a vendor
    const vendorRes = http.post(
      `${testConfig.baseUrl}/vendors`,
      JSON.stringify({
        companyName: 'Load Test Vendor',
        contactPerson: 'Test Contact',
        phoneNumbers: [{ type: 'primary', number: '+**********' }],
        email: '<EMAIL>',
        category: 'Training'
      }),
      { headers: auth.headers }
    );
    
    if (vendorRes.status === 201) {
      testData.vendors.push(JSON.parse(vendorRes.body));
    }
  }
  
  return testData;
}

// Main test function - simulates realistic user journeys
export default function(data) {
  // Select user persona based on weights
  const random = Math.random();
  let selectedPersona;
  let cumulativeWeight = 0;
  
  for (const [persona, config] of Object.entries(userPersonas)) {
    cumulativeWeight += config.weight;
    if (random < cumulativeWeight) {
      selectedPersona = { name: persona, ...config };
      break;
    }
  }
  
  // Select user from test data
  const user = data.users.find(u => u.persona === selectedPersona.name) || data.users[0];
  
  // Authenticate
  const auth = authenticate(user.email, user.password);
  if (!auth) {
    apiErrors.add(1);
    return;
  }
  
  // Start user journey
  const journeyStart = Date.now();
  
  // Execute persona activities based on probabilities
  selectedPersona.activities.forEach(activity => {
    if (Math.random() < activity.probability) {
      sleep(Math.random() * 2 + 1); // Think time
      
      const activityStart = Date.now();
      try {
        // Execute activity with appropriate parameters
        let success = false;
        switch (activity.name) {
          case 'enrollInCourse':
            if (data.courses.length > 0) {
              success = activities[activity.name](auth, user.id, data.courses[0].id);
            }
            break;
          case 'evaluateVendor':
            if (data.vendors.length > 0) {
              success = activities[activity.name](auth, data.vendors[0].id);
            }
            break;
          case 'viewTrainingRecommendations':
          case 'createWinsSubmission':
          case 'completeAssessment':
            success = activities[activity.name](auth, user.id);
            break;
          default:
            success = activities[activity.name](auth);
        }
        
        apiLatency.add(Date.now() - activityStart, { activity: activity.name });
        
        if (!success) {
          apiErrors.add(1);
        }
      } catch (error) {
        apiErrors.add(1);
        console.error(`Activity ${activity.name} failed:`, error);
      }
    }
  });
  
  userJourneyDuration.add(Date.now() - journeyStart);
  
  // Extended think time between user sessions
  sleep(Math.random() * 5 + 3);
}

// Teardown function
export function teardown(data) {
  console.log('Integrated load test completed');
  console.log(`Total cross-app navigations: ${crossAppNavigations.value}`);
}