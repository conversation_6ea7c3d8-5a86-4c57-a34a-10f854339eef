import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { getOptions, testConfig } from './k6.config.js';

// Custom metrics
const courseCreationDuration = new Trend('course_creation_duration');
const enrollmentDuration = new Trend('enrollment_duration');
const assessmentDuration = new Trend('assessment_duration');
const searchDuration = new Trend('search_duration');

// Export test options
export const options = getOptions(__ENV.SCENARIO || 'load');

// Helper function to authenticate
function authenticate(email, password) {
  const res = http.post(
    `${testConfig.baseUrl}/auth/login`,
    JSON.stringify({ email, password }),
    { headers: testConfig.headers }
  );
  
  if (res.status === 200) {
    const tokens = JSON.parse(res.body).tokens;
    return {
      ...testConfig.headers,
      'Authorization': `Bearer ${tokens.accessToken}`,
    };
  }
  return null;
}

// Setup function
export function setup() {
  console.log('Setting up training test data...');
  
  // Create test users
  const users = [];
  const managers = [];
  
  // Create regular users
  for (let i = 0; i < 5; i++) {
    const userData = {
      email: `training-user${i}@example.com`,
      password: 'TestPassword123!',
    };
    
    http.post(
      `${testConfig.baseUrl}/auth/register`,
      JSON.stringify({
        ...userData,
        firstName: 'Training',
        lastName: `User${i}`,
      }),
      { headers: testConfig.headers }
    );
    
    users.push(userData);
  }
  
  // Create manager
  const managerData = {
    email: '<EMAIL>',
    password: 'TestPassword123!',
  };
  
  http.post(
    `${testConfig.baseUrl}/auth/register`,
    JSON.stringify({
      ...managerData,
      firstName: 'Training',
      lastName: 'Manager',
    }),
    { headers: testConfig.headers }
  );
  
  managers.push(managerData);
  
  // Create some initial courses
  const authHeaders = authenticate(managerData.email, managerData.password);
  const courses = [];
  
  if (authHeaders) {
    for (let i = 0; i < 3; i++) {
      const res = http.post(
        `${testConfig.baseUrl}/training/courses`,
        JSON.stringify({
          title: `Performance Test Course ${i}`,
          description: 'Course for load testing',
          provider: 'Internal',
          type: 'workshop',
          duration: 16,
          cost: 500,
          capacity: 100,
          prerequisites: [],
          difficulty: 'intermediate',
        }),
        { headers: authHeaders }
      );
      
      if (res.status === 201) {
        courses.push(JSON.parse(res.body));
      }
    }
  }
  
  return { users, managers, courses };
}

// Main test function
export default function(data) {
  const isManager = Math.random() < 0.2; // 20% managers, 80% regular users
  const user = isManager 
    ? data.managers[Math.floor(Math.random() * data.managers.length)]
    : data.users[Math.floor(Math.random() * data.users.length)];
  
  const authHeaders = authenticate(user.email, user.password);
  if (!authHeaders) return;
  
  // Scenario 1: Browse courses (60% of users)
  if (Math.random() < 0.6) {
    group('Browse Courses', () => {
      // List courses
      const searchStart = Date.now();
      const listRes = http.get(
        `${testConfig.baseUrl}/training/courses?page=1&limit=10`,
        {
          headers: authHeaders,
          tags: { endpoint: 'courses' },
        }
      );
      searchDuration.add(Date.now() - searchStart);
      
      check(listRes, {
        'course list successful': (r) => r.status === 200,
        'returns course data': (r) => {
          const body = JSON.parse(r.body);
          return body.courses && Array.isArray(body.courses);
        },
      });
      
      sleep(Math.random() * 2 + 1);
      
      // Search courses
      const searchRes = http.get(
        `${testConfig.baseUrl}/training/courses?search=test&difficulty=intermediate`,
        {
          headers: authHeaders,
          tags: { endpoint: 'courses-search' },
        }
      );
      
      check(searchRes, {
        'course search successful': (r) => r.status === 200,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // View course details
      if (data.courses.length > 0) {
        const course = data.courses[Math.floor(Math.random() * data.courses.length)];
        const detailRes = http.get(
          `${testConfig.baseUrl}/training/courses/${course.id}`,
          {
            headers: authHeaders,
            tags: { endpoint: 'course-detail' },
          }
        );
        
        check(detailRes, {
          'course detail successful': (r) => r.status === 200,
        });
      }
    });
  }
  
  // Scenario 2: Enroll in course (30% of regular users)
  if (!isManager && Math.random() < 0.3 && data.courses.length > 0) {
    group('Course Enrollment', () => {
      const course = data.courses[Math.floor(Math.random() * data.courses.length)];
      
      const enrollStart = Date.now();
      const enrollRes = http.post(
        `${testConfig.baseUrl}/training/enrollments`,
        JSON.stringify({
          userId: user.id,
          courseId: course.id,
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'enrollment' },
        }
      );
      enrollmentDuration.add(Date.now() - enrollStart);
      
      check(enrollRes, {
        'enrollment successful or already enrolled': (r) => 
          r.status === 201 || (r.status === 400 && r.body.includes('already enrolled')),
      });
      
      sleep(Math.random() * 3 + 2);
      
      // Check my enrollments
      const myEnrollmentsRes = http.get(
        `${testConfig.baseUrl}/training/enrollments?userId=${user.id}`,
        {
          headers: authHeaders,
          tags: { endpoint: 'my-enrollments' },
        }
      );
      
      check(myEnrollmentsRes, {
        'get enrollments successful': (r) => r.status === 200,
      });
    });
  }
  
  // Scenario 3: Complete assessment (20% of users)
  if (Math.random() < 0.2) {
    group('Skill Assessment', () => {
      // Get assessments
      const assessmentsRes = http.get(
        `${testConfig.baseUrl}/training/assessments`,
        {
          headers: authHeaders,
          tags: { endpoint: 'assessments' },
        }
      );
      
      check(assessmentsRes, {
        'get assessments successful': (r) => r.status === 200,
      });
      
      sleep(Math.random() * 2 + 1);
      
      // Simulate assessment completion
      const assessmentStart = Date.now();
      const assessmentData = {
        assessmentId: 'test-assessment-1',
        skillRatings: [
          { skillId: 'skill-1', rating: Math.floor(Math.random() * 5) + 1 },
          { skillId: 'skill-2', rating: Math.floor(Math.random() * 5) + 1 },
          { skillId: 'skill-3', rating: Math.floor(Math.random() * 5) + 1 },
        ],
      };
      
      const completeRes = http.post(
        `${testConfig.baseUrl}/training/assessments/complete`,
        JSON.stringify(assessmentData),
        {
          headers: authHeaders,
          tags: { endpoint: 'assessment-complete' },
        }
      );
      assessmentDuration.add(Date.now() - assessmentStart);
      
      check(completeRes, {
        'assessment completion successful or already completed': (r) => 
          r.status === 201 || r.status === 400,
      });
    });
  }
  
  // Scenario 4: Manager creates course (managers only)
  if (isManager && Math.random() < 0.3) {
    group('Course Management', () => {
      const courseStart = Date.now();
      const newCourseRes = http.post(
        `${testConfig.baseUrl}/training/courses`,
        JSON.stringify({
          title: `Load Test Course ${Date.now()}`,
          description: 'Course created during load testing',
          provider: 'Internal',
          type: 'online',
          duration: 8,
          cost: 300,
          capacity: 50,
          prerequisites: [],
          difficulty: 'beginner',
        }),
        {
          headers: authHeaders,
          tags: { endpoint: 'course-create' },
        }
      );
      courseCreationDuration.add(Date.now() - courseStart);
      
      check(newCourseRes, {
        'course creation successful': (r) => r.status === 201,
      });
      
      sleep(Math.random() * 3 + 2);
      
      // Get training metrics
      const metricsRes = http.get(
        `${testConfig.baseUrl}/training/metrics`,
        {
          headers: authHeaders,
          tags: { endpoint: 'training-metrics' },
        }
      );
      
      check(metricsRes, {
        'get metrics successful': (r) => r.status === 200,
      });
    });
  }
  
  // Scenario 5: View recommendations (40% of users)
  if (Math.random() < 0.4) {
    group('Recommendations', () => {
      const recsRes = http.get(
        `${testConfig.baseUrl}/training/recommendations/${user.id}`,
        {
          headers: authHeaders,
          tags: { endpoint: 'recommendations' },
        }
      );
      
      check(recsRes, {
        'get recommendations successful': (r) => r.status === 200,
      });
    });
  }
  
  // Think time between iterations
  sleep(Math.random() * (testConfig.thinkTime.max - testConfig.thinkTime.min) + testConfig.thinkTime.min);
}

// Teardown function
export function teardown(data) {
  console.log('Training load test completed');
}