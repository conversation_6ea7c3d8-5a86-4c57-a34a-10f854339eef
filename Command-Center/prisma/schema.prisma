// Prisma schema for Command Center
// This schema coexists with TypeORM for specific use cases

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["prisma", "public"]
}

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema", "fullTextSearch", "postgresqlExtensions"]
}

// Analytics Models (Prisma-specific)
model Analytics {
  id          String   @id @default(uuid())
  event       String
  properties  Json
  userId      String?
  sessionId   String?
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())

  @@index([event, timestamp])
  @@index([userId])
  @@index([sessionId])
  @@schema("prisma")
}

model SearchIndex {
  id          String   @id @default(uuid())
  entityType  String
  entityId    String
  title       String
  content     String   @db.Text
  metadata    Json?
  vector      Float[]  @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([entityType, entityId])
  @@index([title])
  @@schema("prisma")
}

model AuditLog {
  id          String   @id @default(uuid())
  action      String
  entityType  String
  entityId    String
  userId      String
  changes     Json?
  metadata    Json?
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime @default(now())

  @@index([action, timestamp])
  @@index([entityType, entityId])
  @@index([userId])
  @@schema("prisma")
}

model Notification {
  id          String   @id @default(uuid())
  userId      String
  type        String
  title       String
  message     String
  data        Json?
  read        Boolean  @default(false)
  readAt      DateTime?
  createdAt   DateTime @default(now())

  @@index([userId, read])
  @@index([type])
  @@schema("prisma")
}

model Report {
  id          String   @id @default(uuid())
  name        String
  type        String
  parameters  Json
  schedule    String?
  lastRun     DateTime?
  nextRun     DateTime?
  status      String   @default("active")
  results     Json?
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type, status])
  @@index([createdBy])
  @@schema("prisma")
}

model Workflow {
  id          String   @id @default(uuid())
  name        String
  description String?
  definition  Json
  status      String   @default("draft")
  version     Int      @default(1)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  executions  WorkflowExecution[]

  @@index([status])
  @@index([createdBy])
  @@schema("prisma")
}

model WorkflowExecution {
  id          String   @id @default(uuid())
  workflowId  String
  status      String   @default("pending")
  input       Json?
  output      Json?
  error       Json?
  startedAt   DateTime @default(now())
  completedAt DateTime?

  workflow    Workflow @relation(fields: [workflowId], references: [id])

  @@index([workflowId, status])
  @@schema("prisma")
}

model FeatureFlag {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  enabled     Boolean  @default(false)
  rules       Json?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@schema("prisma")
}

model ApiKey {
  id          String   @id @default(uuid())
  name        String
  key         String   @unique
  hashedKey   String
  scopes      String[]
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  createdBy   String
  createdAt   DateTime @default(now())
  revokedAt   DateTime?

  @@index([key])
  @@index([createdBy])
  @@schema("prisma")
}

// =============================================================================
// L&D (Learning & Development) Models
// =============================================================================

// Core User Management
model User {
  id              String   @id @default(uuid())
  email           String   @unique
  name            String
  departmentId    String?
  roleId          String?
  managerId       String?
  joinDate        DateTime @default(now())
  status          String   @default("active")
  profilePicture  String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  department      Department? @relation(fields: [departmentId], references: [id])
  role            Role? @relation(fields: [roleId], references: [id])
  manager         User? @relation("UserManager", fields: [managerId], references: [id])
  reports         User[] @relation("UserManager")
  
  // L&D Relationships
  skills          UserSkill[]
  assessmentResponses AssessmentResponse[]
  trainingEnrollments TrainingEnrollment[]
  trainingRecommendations TrainingRecommendation[]
  weeklySubmissions WeeklySubmission[]
  activities      LearningActivity[]
  
  @@index([departmentId])
  @@index([roleId])
  @@index([managerId])
  @@index([email])
  @@schema("prisma")
}

// =============================================================================
// TRAINING NEED ANALYSIS MODELS
// =============================================================================

model Department {
  id            String   @id @default(uuid())
  name          String
  manager       String
  employeeCount Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  users         User[]
  roles         Role[]
  assessments   Assessment[]
  budgetAllocations BudgetAllocation[]
  
  @@index([name])
  @@schema("prisma")
}

model Role {
  id              String   @id @default(uuid())
  title           String
  departmentId    String
  level           String   // 'entry' | 'mid' | 'senior' | 'lead' | 'executive'
  requiredSkills  String[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  department      Department @relation(fields: [departmentId], references: [id])
  users           User[]
  assessments     Assessment[]
  learningPaths   LearningPath[]
  
  @@index([departmentId])
  @@index([level])
  @@schema("prisma")
}

model Skill {
  id          String   @id @default(uuid())
  name        String
  category    String
  description String
  levels      Json     // SkillLevel[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userSkills        UserSkill[]
  skillGaps         SkillGap[]
  trainingCourses   TrainingCourse[] @relation("CourseSkills")
  learningPaths     LearningPath[] @relation("PathSkills")
  
  @@index([category])
  @@index([name])
  @@schema("prisma")
}

model UserSkill {
  id            String   @id @default(uuid())
  userId        String
  skillId       String
  currentLevel  Int
  assessedDate  DateTime
  source        String   // 'self-assessment' | 'manager-review' | 'peer-review' | '360-review'
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  user          User @relation(fields: [userId], references: [id])
  skill         Skill @relation(fields: [skillId], references: [id])
  
  @@unique([userId, skillId])
  @@index([userId])
  @@index([skillId])
  @@schema("prisma")
}

model Assessment {
  id              String   @id @default(uuid())
  title           String
  description     String
  departmentId    String?
  roleId          String?
  createdBy       String
  createdDate     DateTime @default(now())
  status          String   @default("draft") // 'draft' | 'active' | 'completed' | 'archived'
  skillsAssessed  String[]
  dueDate         DateTime?
  updatedAt       DateTime @updatedAt

  // Relationships
  department      Department? @relation(fields: [departmentId], references: [id])
  role            Role? @relation(fields: [roleId], references: [id])
  responses       AssessmentResponse[]
  
  @@index([departmentId])
  @@index([roleId])
  @@index([status])
  @@schema("prisma")
}

model AssessmentResponse {
  id              String   @id @default(uuid())
  assessmentId    String
  userId          String
  completedDate   DateTime?
  status          String   @default("not-started") // 'not-started' | 'in-progress' | 'completed'
  skillRatings    Json     // SkillRating[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  assessment      Assessment @relation(fields: [assessmentId], references: [id])
  user            User @relation(fields: [userId], references: [id])
  
  @@unique([assessmentId, userId])
  @@index([assessmentId])
  @@index([userId])
  @@schema("prisma")
}

model SkillGap {
  id            String   @id @default(uuid())
  userId        String
  skillId       String
  currentLevel  Int
  requiredLevel Int
  gap           Int
  priority      String   // 'critical' | 'high' | 'medium' | 'low'
  impact        String   // 'high' | 'medium' | 'low'
  urgency       String   // 'immediate' | 'short-term' | 'long-term'
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  user          User @relation(fields: [userId], references: [id], map: "SkillGapUser")
  skill         Skill @relation(fields: [skillId], references: [id])
  recommendations TrainingRecommendation[]
  
  @@unique([userId, skillId])
  @@index([userId])
  @@index([skillId])
  @@index([priority])
  @@schema("prisma")
}

model TrainingCourse {
  id              String   @id @default(uuid())
  title           String
  description     String
  provider        String
  type            String   // 'internal' | 'external' | 'online' | 'workshop' | 'certification'
  duration        Int      // in hours
  cost            Float
  capacity        Int?
  prerequisites   String[]
  difficulty      String   // 'beginner' | 'intermediate' | 'advanced'
  rating          Float    @default(0)
  enrollments     Int      @default(0)
  status          String   @default("active") // 'active' | 'inactive' | 'coming-soon'
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  skills          Skill[] @relation("CourseSkills")
  enrollments_rel TrainingEnrollment[]
  recommendations TrainingRecommendation[]
  learningPaths   LearningPath[] @relation("PathCourses")
  
  @@index([provider])
  @@index([type])
  @@index([status])
  @@schema("prisma")
}

model LearningPath {
  id                  String   @id @default(uuid())
  title               String
  description         String
  roleId              String?
  estimatedDuration   Int      // in hours
  difficulty          String   // 'beginner' | 'intermediate' | 'advanced'
  completionCriteria  String[]
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relationships
  role                Role? @relation(fields: [roleId], references: [id])
  courses             TrainingCourse[] @relation("PathCourses")
  skillsAcquired      Skill[] @relation("PathSkills")
  
  @@index([roleId])
  @@schema("prisma")
}

model TrainingRecommendation {
  id                  String   @id @default(uuid())
  userId              String
  courseId            String
  skillGapId          String
  priority            Int
  reasoning           String
  expectedImpact      String   // 'high' | 'medium' | 'low'
  estimatedCompletion DateTime
  status              String   @default("pending") // 'pending' | 'accepted' | 'declined'
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relationships
  user                User @relation(fields: [userId], references: [id])
  course              TrainingCourse @relation(fields: [courseId], references: [id])
  skillGap            SkillGap @relation(fields: [skillGapId], references: [id])
  
  @@index([userId])
  @@index([courseId])
  @@index([skillGapId])
  @@schema("prisma")
}

model TrainingEnrollment {
  id              String   @id @default(uuid())
  userId          String
  courseId        String
  enrolledDate    DateTime @default(now())
  startDate       DateTime?
  completedDate   DateTime?
  status          String   @default("enrolled") // 'enrolled' | 'in-progress' | 'completed' | 'dropped' | 'failed'
  progress        Float    @default(0)
  score           Float?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  user            User @relation(fields: [userId], references: [id])
  course          TrainingCourse @relation(fields: [courseId], references: [id])
  
  @@unique([userId, courseId])
  @@index([userId])
  @@index([courseId])
  @@index([status])
  @@schema("prisma")
}

model LearningActivity {
  id          String   @id @default(uuid())
  type        String   // 'assessment-completed' | 'training-started' | 'training-completed' | 'skill-updated' | 'gap-identified'
  userId      String
  description String
  date        DateTime @default(now())
  metadata    Json?
  createdAt   DateTime @default(now())

  // Relationships
  user        User @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([type])
  @@index([date])
  @@schema("prisma")
}

model BudgetAllocation {
  id                    String   @id @default(uuid())
  departmentId          String
  year                  Int
  quarter               Int?
  totalBudget           Float
  allocatedBudget       Float
  spentBudget           Float
  trainingCategories    Json     // BudgetCategory[]
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relationships
  department            Department @relation(fields: [departmentId], references: [id])
  
  @@unique([departmentId, year, quarter])
  @@index([departmentId])
  @@index([year])
  @@schema("prisma")
}

// =============================================================================
// VENDOR MANAGEMENT MODELS
// =============================================================================

model Vendor {
  id              String   @id @default(uuid())
  companyName     String
  contactPerson   String
  phoneNumbers    Json     // PhoneNumber[]
  email           String
  website         String?
  category        String   // 'Training' | 'Assessment' | 'Certification' | 'Content' | 'Consulting'
  status          String   @default("active") // 'active' | 'inactive' | 'pending'
  rating          Float    @default(0)
  certifications  String[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  proposals       Proposal[]
  reviews         Review[]
  
  @@index([category])
  @@index([status])
  @@index([rating])
  @@schema("prisma")
}

model Proposal {
  id            String   @id @default(uuid())
  vendorId      String
  title         String
  description   String
  costs         Json     // CostItem[]
  totalCost     Float
  status        String   @default("draft") // 'draft' | 'submitted' | 'negotiation' | 'approved' | 'rejected'
  attachments   String[]
  validUntil    DateTime
  submittedAt   DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  vendor        Vendor @relation(fields: [vendorId], references: [id])
  reviews       Review[]
  
  @@index([vendorId])
  @@index([status])
  @@index([totalCost])
  @@schema("prisma")
}

model Review {
  id            String   @id @default(uuid())
  vendorId      String
  proposalId    String?
  rating        Int      // 1-5
  reviewerName  String
  reviewerRole  String
  comment       String
  strengths     String[]
  improvements  String[]
  createdAt     DateTime @default(now())

  // Relationships
  vendor        Vendor @relation(fields: [vendorId], references: [id])
  proposal      Proposal? @relation(fields: [proposalId], references: [id])
  
  @@index([vendorId])
  @@index([proposalId])
  @@index([rating])
  @@schema("prisma")
}

// =============================================================================
// WINS OF WEEK MODELS
// =============================================================================

model WeeklySubmission {
  id              String   @id @default(uuid())
  userId          String
  weekStartDate   DateTime
  weekEndDate     DateTime
  submissionDate  DateTime @default(now())
  achievements    Json     // Achievement[]
  recognitions    Json     // Recognition[]
  costInitiatives Json     // CostInitiative[]
  trainingIdeas   Json     // TrainingIdea[]
  progressUpdates Json     // ProgressUpdate[]
  weeklyHighlight String?
  challenges      String?
  nextWeekFocus   String?
  attachments     Json?    // Attachment[]
  status          String   @default("draft") // 'draft' | 'submitted' | 'reviewed'
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  user            User @relation(fields: [userId], references: [id])
  
  @@unique([userId, weekStartDate])
  @@index([userId])
  @@index([weekStartDate])
  @@index([status])
  @@schema("prisma")
}

model CompetencyFramework {
  id                  String   @id @default(uuid())
  name                String
  description         String
  skills              Json     // Skill IDs
  roles               Json     // Role IDs
  assessmentCriteria  String[]
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([name])
  @@schema("prisma")
}