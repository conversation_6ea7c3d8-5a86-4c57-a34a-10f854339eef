# Docker Deployment Guide for L&D Applications

This guide covers the Docker deployment setup for the L&D applications suite.

## Architecture Overview

The production deployment consists of:
- **Nginx**: Reverse proxy and load balancer
- **Backend API**: NestJS Command Center application
- **Frontend Apps**: Three TanStack Start applications
- **PostgreSQL**: Primary database
- **Redis**: Cache and session storage

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ RAM available
- 10GB+ disk space

## Quick Start

### 1. Clone and Navigate
```bash
cd /path/to/Luminar
```

### 2. Generate SSL Certificates
```bash
cd nginx/ssl
./generate-cert.sh
cd ../..
```

### 3. Configure Environment
```bash
cp .env.production .env
# Edit .env with your production values
```

### 4. Build Images
```bash
./scripts/build-docker.sh production latest
```

### 5. Start Services
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Service URLs

After deployment, services are available at:
- **Main Application**: https://localhost
- **Training App**: https://localhost/training
- **Vendors App**: https://localhost/vendors
- **Wins App**: https://localhost/wins
- **API**: https://localhost/api

## Production Configuration

### Environment Variables

Critical variables to configure in `.env`:

```env
# Database
DATABASE_PASSWORD=<strong-password>
DATABASE_NAME=command_center

# Redis
REDIS_PASSWORD=<strong-redis-password>

# JWT
JWT_SECRET=<random-secret-key>
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d

# Session
SESSION_SECRET=<random-session-secret>

# CORS (your domains)
CORS_ORIGINS=https://yourdomain.com
```

### SSL Certificates

For production, replace self-signed certificates with real ones:

1. **Let's Encrypt** (recommended):
```bash
# Install certbot
docker run -it --rm --name certbot \
  -v ./nginx/ssl:/etc/letsencrypt \
  -v ./nginx/certbot:/var/www/certbot \
  certbot/certbot certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d yourdomain.com \
  -d api.yourdomain.com
```

2. **Update nginx configuration**:
```nginx
ssl_certificate /etc/nginx/ssl/live/yourdomain.com/fullchain.pem;
ssl_certificate_key /etc/nginx/ssl/live/yourdomain.com/privkey.pem;
```

## Container Management

### Start Services
```bash
# All services
docker-compose -f docker-compose.prod.yml up -d

# Specific service
docker-compose -f docker-compose.prod.yml up -d backend
```

### Stop Services
```bash
# Stop all
docker-compose -f docker-compose.prod.yml down

# Stop and remove volumes (CAUTION: deletes data)
docker-compose -f docker-compose.prod.yml down -v
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f backend

# Last 100 lines
docker-compose -f docker-compose.prod.yml logs --tail=100 backend
```

### Scale Services
```bash
# Scale backend to 3 instances
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

## Health Checks

All services include health checks:

```bash
# Check service health
docker-compose -f docker-compose.prod.yml ps

# Manual health check
curl https://localhost/health
curl https://localhost/api/health
curl https://localhost/training/health
```

## Database Management

### Backup Database
```bash
# Create backup
docker exec ld-postgres pg_dump -U postgres command_center > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated daily backups
0 2 * * * docker exec ld-postgres pg_dump -U postgres command_center > /backup/db_$(date +\%Y\%m\%d).sql
```

### Restore Database
```bash
docker exec -i ld-postgres psql -U postgres command_center < backup.sql
```

### Run Migrations
```bash
# TypeORM migrations
docker exec ld-backend npm run typeorm:migration:run

# Prisma migrations
docker exec ld-backend npm run prisma:migrate:deploy
```

## Performance Optimization

### 1. Resource Limits
Add to docker-compose.prod.yml:
```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
```

### 2. Enable Swarm Mode
For production scaling:
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.prod.yml ld-apps
```

### 3. Configure Nginx Caching
Already configured in nginx.conf for:
- Static assets: 1 year cache
- API responses: 10 minute cache for GET requests
- Cache bypass headers respected

## Monitoring

### Basic Monitoring
```bash
# Resource usage
docker stats

# Service health
docker-compose -f docker-compose.prod.yml ps
```

### Advanced Monitoring Setup
See monitoring setup guide for:
- Prometheus metrics collection
- Grafana dashboards
- ELK stack for logs

## Troubleshooting

### Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs <service-name>

# Check events
docker events --since 10m
```

### Database Connection Issues
```bash
# Test connection
docker exec ld-backend npm run typeorm:check

# Check postgres logs
docker-compose -f docker-compose.prod.yml logs postgres
```

### Permission Issues
```bash
# Fix upload directory permissions
docker exec ld-backend chown -R nestjs:nodejs /app/uploads

# Fix log directory
docker exec ld-backend chown -R nestjs:nodejs /app/logs
```

### Memory Issues
```bash
# Check memory usage
docker system df

# Clean up
docker system prune -a --volumes
```

## Security Best Practices

1. **Use Secrets Management**
```yaml
# docker-compose.prod.yml
services:
  backend:
    secrets:
      - db_password
      - jwt_secret

secrets:
  db_password:
    external: true
  jwt_secret:
    external: true
```

2. **Network Isolation**
```yaml
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

3. **Read-only Filesystem**
```yaml
services:
  backend:
    read_only: true
    tmpfs:
      - /tmp
      - /app/temp
```

4. **Non-root User**
Already configured in Dockerfiles

## Backup Strategy

### Automated Backups
Create backup script:
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Database
docker exec ld-postgres pg_dump -U postgres command_center > $BACKUP_DIR/database.sql

# Uploads
docker cp ld-backend:/app/uploads $BACKUP_DIR/uploads

# Redis
docker exec ld-redis redis-cli --rdb $BACKUP_DIR/redis.rdb

# Compress
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### Schedule Backups
```cron
0 2 * * * /path/to/backup.sh
```

## Updates and Maintenance

### Update Process
1. **Build new images**
```bash
./scripts/build-docker.sh production v1.1.0
```

2. **Test in staging**
```bash
docker-compose -f docker-compose.staging.yml up -d
```

3. **Rolling update**
```bash
# Update one service at a time
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
# Test
docker-compose -f docker-compose.prod.yml up -d --no-deps training-frontend
# Continue for other services
```

### Rollback Process
```bash
# Tag current version
docker tag ld-backend:latest ld-backend:rollback

# If issues, revert
docker tag ld-backend:rollback ld-backend:latest
docker-compose -f docker-compose.prod.yml up -d backend
```

## Production Checklist

- [ ] Strong passwords in .env
- [ ] Real SSL certificates installed
- [ ] Firewall configured (only 80/443 open)
- [ ] Backup strategy implemented
- [ ] Monitoring configured
- [ ] Resource limits set
- [ ] Health checks verified
- [ ] Log rotation configured
- [ ] Security headers enabled
- [ ] CORS properly configured

## Support

For issues:
1. Check logs: `docker-compose logs -f`
2. Verify health: `curl https://localhost/health`
3. Check resources: `docker stats`
4. Review configuration: `.env` and `docker-compose.prod.yml`