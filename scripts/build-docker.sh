#!/bin/bash

# Docker Build Script for L&D Applications

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Default values
BUILD_ENV="${1:-production}"
TAG="${2:-latest}"
REGISTRY="${DOCKER_REGISTRY:-}"
PUSH="${3:-false}"

# Display usage
usage() {
    echo "Usage: $0 [environment] [tag] [push]"
    echo ""
    echo "Arguments:"
    echo "  environment  - Build environment (development|production) [default: production]"
    echo "  tag         - Docker image tag [default: latest]"
    echo "  push        - Push to registry (true|false) [default: false]"
    echo ""
    echo "Environment Variables:"
    echo "  DOCKER_REGISTRY - Docker registry URL (e.g., docker.io/myorg)"
    echo ""
    echo "Examples:"
    echo "  $0                     # Build all images for production"
    echo "  $0 development dev     # Build development images with 'dev' tag"
    echo "  $0 production v1.0.0 true  # Build and push production images"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Validate environment
case $BUILD_ENV in
    development|production)
        ;;
    help|--help|-h)
        usage
        exit 0
        ;;
    *)
        echo -e "${RED}Invalid environment: $BUILD_ENV${NC}"
        usage
        exit 1
        ;;
esac

# Function to build image
build_image() {
    local name=$1
    local context=$2
    local dockerfile=$3
    local port=$4
    
    echo -e "${BLUE}Building $name...${NC}"
    
    local image_name="ld-$name"
    if [ -n "$REGISTRY" ]; then
        image_name="$REGISTRY/ld-$name"
    fi
    
    # Build image
    docker build \
        --build-arg BUILD_ENV=$BUILD_ENV \
        --build-arg PORT=$port \
        -t "$image_name:$TAG" \
        -t "$image_name:latest" \
        -f "$dockerfile" \
        "$context"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $name built successfully${NC}"
        
        # Push if requested
        if [ "$PUSH" = "true" ] && [ -n "$REGISTRY" ]; then
            echo -e "${YELLOW}Pushing $name to registry...${NC}"
            docker push "$image_name:$TAG"
            docker push "$image_name:latest"
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✓ $name pushed successfully${NC}"
            else
                echo -e "${RED}✗ Failed to push $name${NC}"
                return 1
            fi
        fi
    else
        echo -e "${RED}✗ Failed to build $name${NC}"
        return 1
    fi
    
    echo ""
}

# Start building
echo -e "${GREEN}Starting Docker Build Process${NC}"
echo "================================"
echo "Environment: $BUILD_ENV"
echo "Tag: $TAG"
echo "Registry: ${REGISTRY:-local}"
echo "Push: $PUSH"
echo "================================"
echo ""

# Change to root directory
cd "$ROOT_DIR"

# Build backend
build_image "backend" "./Command-Center" "./Command-Center/Dockerfile" "3000"

# Build frontend applications
build_image "training" "./Training-need-analysis" "./Training-need-analysis/Dockerfile" "3001"
build_image "vendors" "./Vendors" "./Vendors/Dockerfile" "3002"
build_image "wins" "./Wins-of-Week" "./Wins-of-Week/Dockerfile" "3003"

# Build nginx if custom configuration exists
if [ -f "./nginx/Dockerfile" ]; then
    build_image "nginx" "./nginx" "./nginx/Dockerfile" "80"
fi

# Create docker-compose override for the environment
if [ "$BUILD_ENV" = "development" ]; then
    cp docker-compose.dev.yml docker-compose.override.yml
else
    cp docker-compose.prod.yml docker-compose.override.yml
fi

# Display summary
echo ""
echo -e "${GREEN}Build process completed!${NC}"
echo "================================"

# List built images
echo -e "${BLUE}Built images:${NC}"
docker images | grep "ld-" | grep "$TAG"

echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Copy .env.production to .env and update values"
echo "2. Generate SSL certificates for nginx"
echo "3. Run: docker-compose up -d"
echo "4. Check logs: docker-compose logs -f"

# Create convenience scripts
cat > "$ROOT_DIR/start-prod.sh" << 'EOF'
#!/bin/bash
docker-compose -f docker-compose.prod.yml up -d
EOF

cat > "$ROOT_DIR/stop-prod.sh" << 'EOF'
#!/bin/bash
docker-compose -f docker-compose.prod.yml down
EOF

cat > "$ROOT_DIR/logs-prod.sh" << 'EOF'
#!/bin/bash
docker-compose -f docker-compose.prod.yml logs -f "$@"
EOF

chmod +x "$ROOT_DIR/start-prod.sh" "$ROOT_DIR/stop-prod.sh" "$ROOT_DIR/logs-prod.sh"

echo ""
echo -e "${GREEN}Convenience scripts created:${NC}"
echo "  ./start-prod.sh - Start production services"
echo "  ./stop-prod.sh  - Stop production services"
echo "  ./logs-prod.sh  - View production logs"