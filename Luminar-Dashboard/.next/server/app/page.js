/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/ConnectionStatus.tsx */ \"(ssr)/./src/components/common/ConnectionStatus.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/ErrorBoundary.tsx */ \"(ssr)/./src/components/common/ErrorBoundary.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaG1lZGFiZHVsbGElMkZMdW1pbmFyJTJGTHVtaW5hci1EYXNoYm9hcmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21tYW5kLWNlbnRlci1kZXZlbG9wZXItZGFzaGJvYXJkLz9lY2RhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FobWVkYWJkdWxsYS9MdW1pbmFyL0x1bWluYXItRGFzaGJvYXJkL3NyYy9hcHAvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/memory-stick.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/git-branch.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle2,Clock,Cpu,Database,GitBranch,HardDrive,MemoryStick,Network,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardPage() {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        services: {\n            total: 12,\n            running: 10,\n            stopped: 1,\n            error: 1\n        },\n        resources: {\n            cpu: 45,\n            memory: 62,\n            disk: 78,\n            network: 23\n        },\n        docker: {\n            containers: 8,\n            images: 15,\n            volumes: 5,\n            networks: 3\n        },\n        database: {\n            connections: 25,\n            queries: 1234,\n            size: \"2.3 GB\",\n            uptime: \"5d 12h\"\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Simulate real-time updates\n        const interval = setInterval(()=>{\n            setStatus((prev)=>({\n                    ...prev,\n                    resources: {\n                        cpu: Math.min(100, Math.max(0, prev.resources.cpu + (Math.random() - 0.5) * 10)),\n                        memory: Math.min(100, Math.max(0, prev.resources.memory + (Math.random() - 0.5) * 5)),\n                        disk: prev.resources.disk,\n                        network: Math.min(100, Math.max(0, prev.resources.network + (Math.random() - 0.5) * 15))\n                    },\n                    database: {\n                        ...prev.database,\n                        connections: Math.max(0, prev.database.connections + Math.floor((Math.random() - 0.5) * 5)),\n                        queries: prev.database.queries + Math.floor(Math.random() * 10)\n                    }\n                }));\n        }, 3000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const statusCards = [\n        {\n            title: \"Services\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            stats: [\n                {\n                    label: \"Running\",\n                    value: status.services.running,\n                    color: \"text-green-500\"\n                },\n                {\n                    label: \"Stopped\",\n                    value: status.services.stopped,\n                    color: \"text-yellow-500\"\n                },\n                {\n                    label: \"Error\",\n                    value: status.services.error,\n                    color: \"text-red-500\"\n                }\n            ]\n        },\n        {\n            title: \"Docker\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            stats: [\n                {\n                    label: \"Containers\",\n                    value: status.docker.containers,\n                    color: \"text-blue-500\"\n                },\n                {\n                    label: \"Images\",\n                    value: status.docker.images,\n                    color: \"text-purple-500\"\n                },\n                {\n                    label: \"Volumes\",\n                    value: status.docker.volumes,\n                    color: \"text-indigo-500\"\n                }\n            ]\n        },\n        {\n            title: \"Database\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            stats: [\n                {\n                    label: \"Connections\",\n                    value: status.database.connections,\n                    color: \"text-green-500\"\n                },\n                {\n                    label: \"Size\",\n                    value: status.database.size,\n                    color: \"text-blue-500\"\n                },\n                {\n                    label: \"Uptime\",\n                    value: status.database.uptime,\n                    color: \"text-purple-500\"\n                }\n            ]\n        }\n    ];\n    const resourceMetrics = [\n        {\n            name: \"CPU Usage\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            value: status.resources.cpu,\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"Memory Usage\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            value: status.resources.memory,\n            color: \"bg-green-500\"\n        },\n        {\n            name: \"Disk Usage\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            value: status.resources.disk,\n            color: \"bg-yellow-500\"\n        },\n        {\n            name: \"Network I/O\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            value: status.resources.network,\n            color: \"bg-purple-500\"\n        }\n    ];\n    const recentEvents = [\n        {\n            id: 1,\n            type: \"success\",\n            message: \"Database backup completed successfully\",\n            time: \"5 min ago\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 2,\n            type: \"warning\",\n            message: \"High memory usage detected on worker-2\",\n            time: \"12 min ago\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 3,\n            type: \"error\",\n            message: \"Failed to connect to Redis cache\",\n            time: \"25 min ago\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 4,\n            type: \"info\",\n            message: \"New deployment started for API service\",\n            time: \"1 hour ago\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 5,\n            type: \"success\",\n            message: \"SSL certificates renewed automatically\",\n            time: \"2 hours ago\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 border border-primary/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"Welcome to Command Center\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Monitor and manage your entire infrastructure from this unified dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: statusCards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card rounded-lg p-6 border border-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: card.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(card.icon, {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: card.stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-lg font-semibold ${stat.color}`,\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, stat.label, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, card.title, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-card rounded-lg p-6 border border-border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"System Resources\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: resourceMetrics.map((metric)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: metric.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: [\n                                                        metric.value,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `${metric.color} h-2 rounded-full transition-all duration-500`,\n                                                style: {\n                                                    width: `${metric.value}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, metric.name, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-card rounded-lg p-6 border border-border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Recent Events\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: recentEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(event.icon, {\n                                            className: `h-5 w-5 mt-0.5 ${event.type === \"success\" ? \"text-green-500\" : event.type === \"warning\" ? \"text-yellow-500\" : event.type === \"error\" ? \"text-red-500\" : \"text-blue-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: event.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: event.time\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, event.id, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: \"Restart Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Quick service restart\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: \"Backup Database\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Create backup now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: \"Deploy Update\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Deploy latest changes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle2_Clock_Cpu_Database_GitBranch_HardDrive_MemoryStick_Network_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: \"View Logs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Real-time log viewer\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(ssr)/./src/components/auth/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    refetchOnWindowFocus: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthGuard.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/AuthGuard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/auth.store */ \"(ssr)/./src/stores/auth.store.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard auto */ \n\n\n\n\nconst PUBLIC_PATHS = [\n    \"/login\"\n];\nfunction AuthGuard({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, isLoading, checkAuth } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, [\n        checkAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const isPublicPath = PUBLIC_PATHS.includes(pathname);\n            if (!isAuthenticated && !isPublicPath) {\n                router.push(\"/login\");\n            } else if (isAuthenticated && isPublicPath) {\n                router.push(\"/\");\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        pathname,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    const isPublicPath = PUBLIC_PATHS.includes(pathname);\n    if (!isAuthenticated && !isPublicPath) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/common/ConnectionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/common/ConnectionStatus.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus),\n/* harmony export */   OfflineNotice: () => (/* binding */ OfflineNotice)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus,OfflineNotice auto */ \n\n\n\nfunction ConnectionStatus({ className, showText = true }) {\n    const { isOnline, isChecking, status } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_1__.useConnectionStatus)();\n    const getStatusConfig = ()=>{\n        if (isChecking) {\n            return {\n                icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                color: \"text-yellow-500\",\n                bg: \"bg-yellow-50\",\n                text: \"Checking...\"\n            };\n        }\n        if (isOnline) {\n            return {\n                icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                color: \"text-green-500\",\n                bg: \"bg-green-50\",\n                text: \"Online\"\n            };\n        }\n        return {\n            icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-red-500\",\n            bg: \"bg-red-50\",\n            text: \"Offline\"\n        };\n    };\n    const config = getStatusConfig();\n    const Icon = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1 rounded-full\", config.bg),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-3 w-3\", config.color)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium\", config.color),\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n// Full-screen offline notice\nfunction OfflineNotice() {\n    const { isOnline } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_1__.useConnectionStatus)();\n    if (isOnline) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 bg-red-500 text-white text-center py-2 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"You are offline. Some features may not work properly.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/common/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/common/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler auto */ \n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Call optional error handler\n        this.props.onError?.(error, errorInfo);\n        // Log to monitoring service if available\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-16 w-16 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-foreground\",\n                                    children: \"Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"An unexpected error occurred. Our team has been notified.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"text-left bg-card border border-border rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer font-medium text-red-600 mb-2\",\n                                    children: \"Error Details (Development)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm font-mono\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Error:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" \",\n                                                this.state.error.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this),\n                                        this.state.error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs mt-1 p-2 bg-background rounded border\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 21\n                                        }, this),\n                                        this.state.errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs mt-1 p-2 bg-background rounded border\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleRetry,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleReload,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Reload Page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleGoHome,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Go Home\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"If this problem persists, please contact support with the error details.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        };\n    }\n}\n// HOC for wrapping components with error boundary\nfunction withErrorBoundary(Component, fallback, onError) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            fallback: fallback,\n            onError: onError,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n            lineNumber: 184,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Hook for error reporting\nfunction useErrorHandler() {\n    const handleError = (error, context)=>{\n        console.error(`Error in ${context || \"component\"}:`, error);\n        // Log to monitoring service\n        if (false) {}\n    };\n    return {\n        handleError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/git-branch.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,ChevronRight,Container,Database,FileText,GitBranch,HardDrive,LogOut,Menu,Rocket,Server,Settings,Shield,Terminal,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/auth.store */ \"(ssr)/./src/stores/auth.store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/ConnectionStatus */ \"(ssr)/./src/components/common/ConnectionStatus.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Overview\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Docker\",\n        href: \"/docker\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Environment\",\n        href: \"/environment\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Database\",\n        href: \"/database\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Monitoring\",\n        href: \"/monitoring\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Health\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Logs\",\n        href: \"/logs\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"Deployment\",\n        href: \"/deployment\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: \"Backup\",\n        href: \"/backup\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        name: \"API Testing\",\n        href: \"/api\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    },\n    {\n        name: \"Security\",\n        href: \"/security\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    },\n    {\n        name: \"Alerts\",\n        href: \"/alerts\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    }\n];\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, logout } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out\", !sidebarOpen && \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-full flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center justify-between px-6 border-b border-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Command Center\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-muted-foreground hover:text-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 space-y-1 px-3 py-4 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4 ml-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-border p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: user?.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: user?.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"w-full flex items-center space-x-3 px-3 py-2 text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"transition-all duration-300\", sidebarOpen ? \"lg:ml-64\" : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-40 flex h-16 items-center gap-4 border-b border-border bg-background px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                className: \"text-muted-foreground hover:text-foreground\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_ChevronRight_Container_Database_FileText_GitBranch_HardDrive_LogOut_Menu_Rocket_Server_Settings_Shield_Terminal_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: navigation.find((item)=>item.href === pathname)?.name || \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: new Date().toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/layout/DashboardLayout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAlerts: () => (/* binding */ useAlerts),\n/* harmony export */   useApiMutation: () => (/* binding */ useApiMutation),\n/* harmony export */   useApiQuery: () => (/* binding */ useApiQuery),\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus),\n/* harmony export */   useCreateDeployment: () => (/* binding */ useCreateDeployment),\n/* harmony export */   useDatabaseQuery: () => (/* binding */ useDatabaseQuery),\n/* harmony export */   useDatabases: () => (/* binding */ useDatabases),\n/* harmony export */   useDeployments: () => (/* binding */ useDeployments),\n/* harmony export */   useDockerContainerAction: () => (/* binding */ useDockerContainerAction),\n/* harmony export */   useDockerContainers: () => (/* binding */ useDockerContainers),\n/* harmony export */   useDockerImages: () => (/* binding */ useDockerImages),\n/* harmony export */   useEnvironmentVariables: () => (/* binding */ useEnvironmentVariables),\n/* harmony export */   useHealthCheck: () => (/* binding */ useHealthCheck),\n/* harmony export */   useLogs: () => (/* binding */ useLogs),\n/* harmony export */   useServiceAction: () => (/* binding */ useServiceAction),\n/* harmony export */   useServices: () => (/* binding */ useServices),\n/* harmony export */   useSystemMetrics: () => (/* binding */ useSystemMetrics),\n/* harmony export */   useUpdateEnvironmentVariable: () => (/* binding */ useUpdateEnvironmentVariable)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useApiQuery,useApiMutation,useDockerContainers,useDockerImages,useDockerContainerAction,useEnvironmentVariables,useUpdateEnvironmentVariable,useServices,useServiceAction,useDatabases,useDatabaseQuery,useDeployments,useCreateDeployment,useSystemMetrics,useLogs,useAlerts,useHealthCheck,useConnectionStatus auto */ \n\n\n// Generic API hook with enhanced error handling and caching\nfunction useApiQuery(key, fetcher, options = {}) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: Array.isArray(key) ? key : [\n            key\n        ],\n        queryFn: fetcher,\n        select: (data)=>data.data,\n        retry: (failureCount, error)=>{\n            // Don't retry on client errors (4xx)\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            return failureCount < 3;\n        },\n        staleTime: 5 * 60 * 1000,\n        ...options\n    });\n}\nfunction useApiMutation(mutationFn, options = {}) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn,\n        onError: (error)=>{\n            // Global error handling\n            if (error?.message && !options.onError) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message);\n            }\n        },\n        onSuccess: (data, variables, context)=>{\n            // Invalidate relevant queries on successful mutations\n            if (options.meta?.invalidates) {\n                const patterns = Array.isArray(options.meta.invalidates) ? options.meta.invalidates : [\n                    options.meta.invalidates\n                ];\n                patterns.forEach((pattern)=>{\n                    queryClient.invalidateQueries({\n                        queryKey: [\n                            pattern\n                        ]\n                    });\n                });\n            }\n            options.onSuccess?.(data, variables, context);\n        },\n        ...options\n    });\n}\n// Specialized hooks for different API operations\n// Docker API hooks\nfunction useDockerContainers(all = false) {\n    return useApiQuery([\n        \"docker-containers\",\n        all\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/docker/containers\", {\n            params: {\n                all\n            }\n        }), {\n        refetchInterval: 5000\n    });\n}\nfunction useDockerImages() {\n    return useApiQuery([\n        \"docker-images\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/docker/images\"), {\n        refetchInterval: 10000\n    });\n}\nfunction useDockerContainerAction() {\n    return useApiMutation(({ containerId, action })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/docker/containers/${containerId}/${action}`), {\n        meta: {\n            invalidates: \"docker-containers\"\n        }\n    });\n}\n// Environment API hooks\nfunction useEnvironmentVariables(environment) {\n    return useApiQuery([\n        \"environment-variables\",\n        environment\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/environment/variables\", {\n            params: {\n                environment\n            }\n        }));\n}\nfunction useUpdateEnvironmentVariable() {\n    return useApiMutation(({ id, data })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(`/environment/variables/${id}`, data), {\n        meta: {\n            invalidates: \"environment-variables\"\n        }\n    });\n}\n// Services API hooks\nfunction useServices() {\n    return useApiQuery([\n        \"services\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/services\"), {\n        refetchInterval: 5000\n    });\n}\nfunction useServiceAction() {\n    return useApiMutation(({ serviceId, action })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/services/${serviceId}/${action}`), {\n        meta: {\n            invalidates: \"services\"\n        }\n    });\n}\n// Database API hooks\nfunction useDatabases() {\n    return useApiQuery([\n        \"databases\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/databases\"), {\n        refetchInterval: 10000\n    });\n}\nfunction useDatabaseQuery() {\n    return useApiMutation(({ databaseId, query })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/databases/${databaseId}/query`, {\n            query\n        }));\n}\n// Deployment API hooks\nfunction useDeployments(environment) {\n    return useApiQuery([\n        \"deployments\",\n        environment || \"all\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/deployments\", {\n            params: {\n                environment\n            }\n        }), {\n        refetchInterval: 5000\n    });\n}\nfunction useCreateDeployment() {\n    return useApiMutation((data)=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/deployments\", data), {\n        meta: {\n            invalidates: \"deployments\"\n        }\n    });\n}\n// Monitoring API hooks\nfunction useSystemMetrics() {\n    return useApiQuery([\n        \"system-metrics\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/system\"), {\n        refetchInterval: 5000\n    });\n}\nfunction useLogs(filters) {\n    return useApiQuery([\n        \"logs\",\n        filters\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/logs\", {\n            params: filters\n        }), {\n        enabled: !!filters,\n        staleTime: 0\n    });\n}\nfunction useAlerts(filters = {}) {\n    return useApiQuery([\n        \"alerts\",\n        filters\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/alerts\", {\n            params: filters\n        }), {\n        refetchInterval: 10000\n    });\n}\n// Health check hook\nfunction useHealthCheck() {\n    return useApiQuery([\n        \"health\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/health\"), {\n        refetchInterval: 30000,\n        retry: false,\n        staleTime: 0\n    });\n}\n// Connection status hook\nfunction useConnectionStatus() {\n    const { data: healthData, isError, isLoading } = useHealthCheck();\n    return {\n        isOnline: !isError && !!healthData,\n        isChecking: isLoading,\n        status: isError ? \"offline\" : healthData ? \"online\" : \"checking\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   createWebSocketClient: () => (/* binding */ createWebSocketClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nclass ApiClient {\n    constructor(){\n        this.retryCount = 0;\n        this.maxRetries = 3;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            baseURL: API_BASE_URL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getStoredToken)();\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            config.headers[\"X-Request-ID\"] = this.generateRequestId();\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            this.retryCount = 0;\n            return response;\n        }, async (error)=>{\n            const originalRequest = error.config;\n            if (this.shouldRetry(error) && this.retryCount < this.maxRetries) {\n                this.retryCount++;\n                await this.delay(1000 * this.retryCount);\n                return this.client(originalRequest);\n            }\n            if (error.response) {\n                const { status, data } = error.response;\n                switch(status){\n                    case 401:\n                        this.handleUnauthorized();\n                        break;\n                    case 403:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Access forbidden\");\n                        break;\n                    case 404:\n                        console.warn(\"Resource not found:\", originalRequest.url);\n                        break;\n                    case 429:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Too many requests. Please try again later.\");\n                        break;\n                    case 500:\n                    case 502:\n                    case 503:\n                    case 504:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Server error. Please try again.\");\n                        break;\n                    default:\n                        if (data?.message) {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(data.message);\n                        }\n                }\n                return Promise.reject({\n                    message: data?.message || error.message,\n                    status,\n                    code: data?.code,\n                    details: data?.details\n                });\n            }\n            if (error.code === \"NETWORK_ERROR\" || !error.response) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n            }\n            return Promise.reject({\n                message: error.message || \"An unexpected error occurred\",\n                status: 0\n            });\n        });\n    }\n    shouldRetry(error) {\n        return !error.response || error.response.status >= 500 || error.code === \"NETWORK_ERROR\" || error.code === \"TIMEOUT\";\n    }\n    delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    generateRequestId() {\n        return Math.random().toString(36).substring(2, 15);\n    }\n    handleUnauthorized() {\n        if (false) {}\n    }\n    async get(url, config) {\n        return this.client.get(url, config);\n    }\n    async post(url, data, config) {\n        return this.client.post(url, data, config);\n    }\n    async put(url, data, config) {\n        return this.client.put(url, data, config);\n    }\n    async patch(url, data, config) {\n        return this.client.patch(url, data, config);\n    }\n    async delete(url, config) {\n        return this.client.delete(url, config);\n    }\n    async healthCheck() {\n        try {\n            const response = await this.get(\"/health\");\n            return response.status === 200;\n        } catch (error) {\n            console.error(\"Health check failed:\", error);\n            return false;\n        }\n    }\n}\nconst apiClient = new ApiClient();\n// WebSocket client for real-time updates\nfunction createWebSocketClient(namespace = \"\") {\n    const wsUrl = \"ws://localhost:3000\" || 0;\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getStoredToken)();\n    const ws = new WebSocket(`${wsUrl}${namespace}?token=${token}`);\n    ws.onopen = ()=>{\n        console.log(`WebSocket connected to ${namespace}`);\n    };\n    ws.onerror = (error)=>{\n        console.error(\"WebSocket error:\", error);\n    };\n    ws.onclose = ()=>{\n        console.log(\"WebSocket disconnected\");\n    // Implement reconnection logic if needed\n    };\n    return ws;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken)\n/* harmony export */ });\nconst DEVELOPER_EMAIL = \"<EMAIL>\";\nconst DEVELOPER_PASSWORD = \"developer123!\";\nclass AuthService {\n    static{\n        this.TOKEN_KEY = \"command-center-token\";\n    }\n    static{\n        this.REFRESH_TOKEN_KEY = \"command-center-refresh-token\";\n    }\n    static{\n        this.USER_KEY = \"command-center-user\";\n    }\n    static async login(credentials) {\n        // Special developer authentication\n        if (credentials.email === DEVELOPER_EMAIL && credentials.password === DEVELOPER_PASSWORD) {\n            const user = {\n                id: \"dev-001\",\n                email: DEVELOPER_EMAIL,\n                name: \"Developer\",\n                role: \"developer\",\n                permissions: [\n                    \"docker.manage\",\n                    \"environment.configure\",\n                    \"services.control\",\n                    \"database.manage\",\n                    \"deployment.execute\",\n                    \"backup.manage\",\n                    \"monitoring.view\",\n                    \"logs.view\",\n                    \"api.test\",\n                    \"system.admin\"\n                ]\n            };\n            const response = {\n                user,\n                accessToken: btoa(JSON.stringify({\n                    user,\n                    exp: Date.now() + 3600000\n                })),\n                refreshToken: btoa(JSON.stringify({\n                    userId: user.id,\n                    exp: Date.now() + 86400000 * 7\n                })) // 7 days\n            };\n            // Store tokens and user\n            this.setTokens(response.accessToken, response.refreshToken);\n            this.setUser(user);\n            return response;\n        }\n        // For production, make actual API call\n        try {\n            const response = await fetch(`${\"http://localhost:3000\"}/api/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(credentials)\n            });\n            if (!response.ok) {\n                throw new Error(\"Invalid credentials\");\n            }\n            const data = await response.json();\n            this.setTokens(data.accessToken, data.refreshToken);\n            this.setUser(data.user);\n            return data;\n        } catch (error) {\n            throw new Error(\"Invalid credentials\");\n        }\n    }\n    static logout() {\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n        localStorage.removeItem(this.USER_KEY);\n    }\n    static getToken() {\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static getRefreshToken() {\n        return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n    }\n    static getUser() {\n        const userStr = localStorage.getItem(this.USER_KEY);\n        if (!userStr) return null;\n        try {\n            return JSON.parse(userStr);\n        } catch  {\n            return null;\n        }\n    }\n    static isAuthenticated() {\n        const token = this.getToken();\n        if (!token) return false;\n        try {\n            const payload = JSON.parse(atob(token));\n            return payload.exp > Date.now();\n        } catch  {\n            return false;\n        }\n    }\n    static hasPermission(permission) {\n        const user = this.getUser();\n        if (!user) return false;\n        return user.permissions.includes(permission);\n    }\n    static setTokens(accessToken, refreshToken) {\n        localStorage.setItem(this.TOKEN_KEY, accessToken);\n        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);\n    }\n    static setUser(user) {\n        localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n    static async refreshToken() {\n        const refreshToken = this.getRefreshToken();\n        if (!refreshToken) return null;\n        try {\n            const response = await fetch(`${\"http://localhost:3000\"}/api/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${refreshToken}`\n                }\n            });\n            if (!response.ok) {\n                this.logout();\n                return null;\n            }\n            const data = await response.json();\n            this.setTokens(data.accessToken, data.refreshToken);\n            this.setUser(data.user);\n            return data;\n        } catch  {\n            this.logout();\n            return null;\n        }\n    }\n}\nfunction getStoredToken() {\n    return AuthService.getToken();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    const parts = [];\n    if (hours > 0) parts.push(`${hours}h`);\n    if (minutes > 0) parts.push(`${minutes}m`);\n    if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);\n    return parts.join(\" \");\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        dateStyle: \"medium\",\n        timeStyle: \"short\"\n    }).format(new Date(date));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth.store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth.store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set)=>({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        error: null,\n        login: async (credentials)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.login(credentials);\n                set({\n                    user: response.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : \"Login failed\"\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.logout();\n            set({\n                user: null,\n                isAuthenticated: false,\n                isLoading: false,\n                error: null\n            });\n        },\n        checkAuth: ()=>{\n            const user = _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.getUser();\n            const isAuthenticated = _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.isAuthenticated();\n            set({\n                user,\n                isAuthenticated,\n                isLoading: false,\n                error: null\n            });\n        },\n        refreshAuth: async ()=>{\n            try {\n                const response = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.refreshToken();\n                if (response) {\n                    set({\n                        user: response.user,\n                        isAuthenticated: true,\n                        error: null\n                    });\n                } else {\n                    set({\n                        user: null,\n                        isAuthenticated: false,\n                        error: null\n                    });\n                }\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    error: error instanceof Error ? error.message : \"Auth refresh failed\"\n                });\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth.store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"97c5c484344f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tbWFuZC1jZW50ZXItZGV2ZWxvcGVyLWRhc2hib2FyZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MmYzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk3YzVjNDg0MzQ0ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_common_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/ErrorBoundary */ \"(rsc)/./src/components/common/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/common/ConnectionStatus */ \"(rsc)/./src/components/common/ConnectionStatus.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Command Center - Developer Dashboard\",\n    description: \"Manage and monitor your Command Center infrastructure\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__.OfflineNotice, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this),\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/common/ConnectionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/common/ConnectionStatus.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionStatus: () => (/* binding */ e0),
/* harmony export */   OfflineNotice: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx#ConnectionStatus`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx#OfflineNotice`);


/***/ }),

/***/ "(rsc)/./src/components/common/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/common/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useErrorHandler: () => (/* binding */ e2),
/* harmony export */   withErrorBoundary: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#withErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#useErrorHandler`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/zustand","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();