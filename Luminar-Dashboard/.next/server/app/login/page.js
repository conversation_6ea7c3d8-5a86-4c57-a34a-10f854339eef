/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/ConnectionStatus.tsx */ \"(ssr)/./src/components/common/ConnectionStatus.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/ErrorBoundary.tsx */ \"(ssr)/./src/components/common/ErrorBoundary.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FConnectionStatus.tsx&modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fcomponents%2Fcommon%2FErrorBoundary.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaG1lZGFiZHVsbGElMkZMdW1pbmFyJTJGTHVtaW5hci1EYXNoYm9hcmQlMkZzcmMlMkZhcHAlMkZsb2dpbiUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbW1hbmQtY2VudGVyLWRldmVsb3Blci1kYXNoYm9hcmQvPzZmMTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWhtZWRhYmR1bGxhL0x1bWluYXIvTHVtaW5hci1EYXNoYm9hcmQvc3JjL2FwcC9sb2dpbi9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/auth.store */ \"(ssr)/./src/stores/auth.store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.string().min(8, \"Password must be at least 8 characters\")\n});\nfunction LoginPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isLoading } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { register, handleSubmit, formState: { errors }, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)();\n    const onSubmit = async (data)=>{\n        try {\n            await login(data);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Welcome to Command Center Developer Dashboard!\");\n            router.push(\"/\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Invalid credentials. Please try again.\");\n        }\n    };\n    const fillDeveloperCredentials = ()=>{\n        setValue(\"email\", \"<EMAIL>\");\n        setValue(\"password\", \"developer123!\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Developer credentials filled\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-8 p-8 bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-primary/20 rounded-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-12 w-12 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white\",\n                            children: \"Command Center\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-300\",\n                            children: \"Developer Dashboard Login\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onSubmit),\n                    className: \"mt-8 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-200 mb-2\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ...register(\"email\"),\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm\",\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-400\",\n                                            children: errors.email.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-200 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"password\"),\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm pr-12\",\n                                                    placeholder: \"••••••••\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-400\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full flex justify-center items-center px-4 py-3 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true) : \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: fillDeveloperCredentials,\n                                    className: \"w-full px-4 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors border border-white/20\",\n                                    children: \"Use Developer Credentials\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Secure access to Command Center infrastructure management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(ssr)/./src/components/auth/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    refetchOnWindowFocus: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthGuard.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/AuthGuard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/auth.store */ \"(ssr)/./src/stores/auth.store.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard auto */ \n\n\n\n\nconst PUBLIC_PATHS = [\n    \"/login\"\n];\nfunction AuthGuard({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, isLoading, checkAuth } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, [\n        checkAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const isPublicPath = PUBLIC_PATHS.includes(pathname);\n            if (!isAuthenticated && !isPublicPath) {\n                router.push(\"/login\");\n            } else if (isAuthenticated && isPublicPath) {\n                router.push(\"/\");\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        pathname,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/auth/AuthGuard.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    const isPublicPath = PUBLIC_PATHS.includes(pathname);\n    if (!isAuthenticated && !isPublicPath) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/common/ConnectionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/common/ConnectionStatus.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus),\n/* harmony export */   OfflineNotice: () => (/* binding */ OfflineNotice)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus,OfflineNotice auto */ \n\n\n\nfunction ConnectionStatus({ className, showText = true }) {\n    const { isOnline, isChecking, status } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_1__.useConnectionStatus)();\n    const getStatusConfig = ()=>{\n        if (isChecking) {\n            return {\n                icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                color: \"text-yellow-500\",\n                bg: \"bg-yellow-50\",\n                text: \"Checking...\"\n            };\n        }\n        if (isOnline) {\n            return {\n                icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                color: \"text-green-500\",\n                bg: \"bg-green-50\",\n                text: \"Online\"\n            };\n        }\n        return {\n            icon: _barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-red-500\",\n            bg: \"bg-red-50\",\n            text: \"Offline\"\n        };\n    };\n    const config = getStatusConfig();\n    const Icon = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1 rounded-full\", config.bg),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-3 w-3\", config.color)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium\", config.color),\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n// Full-screen offline notice\nfunction OfflineNotice() {\n    const { isOnline } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_1__.useConnectionStatus)();\n    if (isOnline) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 bg-red-500 text-white text-center py-2 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"You are offline. Some features may not work properly.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/common/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/common/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler auto */ \n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Call optional error handler\n        this.props.onError?.(error, errorInfo);\n        // Log to monitoring service if available\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-16 w-16 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-foreground\",\n                                    children: \"Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"An unexpected error occurred. Our team has been notified.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"text-left bg-card border border-border rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer font-medium text-red-600 mb-2\",\n                                    children: \"Error Details (Development)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm font-mono\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Error:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" \",\n                                                this.state.error.message\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this),\n                                        this.state.error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs mt-1 p-2 bg-background rounded border\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 21\n                                        }, this),\n                                        this.state.errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs mt-1 p-2 bg-background rounded border\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleRetry,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleReload,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Reload Page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleGoHome,\n                                    className: \"flex items-center justify-center space-x-2 px-4 py-2 border border-border rounded-lg hover:bg-accent transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Go Home\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"If this problem persists, please contact support with the error details.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        };\n    }\n}\n// HOC for wrapping components with error boundary\nfunction withErrorBoundary(Component, fallback, onError) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            fallback: fallback,\n            onError: onError,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx\",\n            lineNumber: 184,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Hook for error reporting\nfunction useErrorHandler() {\n    const handleError = (error, context)=>{\n        console.error(`Error in ${context || \"component\"}:`, error);\n        // Log to monitoring service\n        if (false) {}\n    };\n    return {\n        handleError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAlerts: () => (/* binding */ useAlerts),\n/* harmony export */   useApiMutation: () => (/* binding */ useApiMutation),\n/* harmony export */   useApiQuery: () => (/* binding */ useApiQuery),\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus),\n/* harmony export */   useCreateDeployment: () => (/* binding */ useCreateDeployment),\n/* harmony export */   useDatabaseQuery: () => (/* binding */ useDatabaseQuery),\n/* harmony export */   useDatabases: () => (/* binding */ useDatabases),\n/* harmony export */   useDeployments: () => (/* binding */ useDeployments),\n/* harmony export */   useDockerContainerAction: () => (/* binding */ useDockerContainerAction),\n/* harmony export */   useDockerContainers: () => (/* binding */ useDockerContainers),\n/* harmony export */   useDockerImages: () => (/* binding */ useDockerImages),\n/* harmony export */   useEnvironmentVariables: () => (/* binding */ useEnvironmentVariables),\n/* harmony export */   useHealthCheck: () => (/* binding */ useHealthCheck),\n/* harmony export */   useLogs: () => (/* binding */ useLogs),\n/* harmony export */   useServiceAction: () => (/* binding */ useServiceAction),\n/* harmony export */   useServices: () => (/* binding */ useServices),\n/* harmony export */   useSystemMetrics: () => (/* binding */ useSystemMetrics),\n/* harmony export */   useUpdateEnvironmentVariable: () => (/* binding */ useUpdateEnvironmentVariable)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useApiQuery,useApiMutation,useDockerContainers,useDockerImages,useDockerContainerAction,useEnvironmentVariables,useUpdateEnvironmentVariable,useServices,useServiceAction,useDatabases,useDatabaseQuery,useDeployments,useCreateDeployment,useSystemMetrics,useLogs,useAlerts,useHealthCheck,useConnectionStatus auto */ \n\n\n// Generic API hook with enhanced error handling and caching\nfunction useApiQuery(key, fetcher, options = {}) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: Array.isArray(key) ? key : [\n            key\n        ],\n        queryFn: fetcher,\n        select: (data)=>data.data,\n        retry: (failureCount, error)=>{\n            // Don't retry on client errors (4xx)\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            return failureCount < 3;\n        },\n        staleTime: 5 * 60 * 1000,\n        ...options\n    });\n}\nfunction useApiMutation(mutationFn, options = {}) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn,\n        onError: (error)=>{\n            // Global error handling\n            if (error?.message && !options.onError) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message);\n            }\n        },\n        onSuccess: (data, variables, context)=>{\n            // Invalidate relevant queries on successful mutations\n            if (options.meta?.invalidates) {\n                const patterns = Array.isArray(options.meta.invalidates) ? options.meta.invalidates : [\n                    options.meta.invalidates\n                ];\n                patterns.forEach((pattern)=>{\n                    queryClient.invalidateQueries({\n                        queryKey: [\n                            pattern\n                        ]\n                    });\n                });\n            }\n            options.onSuccess?.(data, variables, context);\n        },\n        ...options\n    });\n}\n// Specialized hooks for different API operations\n// Docker API hooks\nfunction useDockerContainers(all = false) {\n    return useApiQuery([\n        \"docker-containers\",\n        all\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/docker/containers\", {\n            params: {\n                all\n            }\n        }), {\n        refetchInterval: 5000\n    });\n}\nfunction useDockerImages() {\n    return useApiQuery([\n        \"docker-images\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/docker/images\"), {\n        refetchInterval: 10000\n    });\n}\nfunction useDockerContainerAction() {\n    return useApiMutation(({ containerId, action })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/docker/containers/${containerId}/${action}`), {\n        meta: {\n            invalidates: \"docker-containers\"\n        }\n    });\n}\n// Environment API hooks\nfunction useEnvironmentVariables(environment) {\n    return useApiQuery([\n        \"environment-variables\",\n        environment\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/environment/variables\", {\n            params: {\n                environment\n            }\n        }));\n}\nfunction useUpdateEnvironmentVariable() {\n    return useApiMutation(({ id, data })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(`/environment/variables/${id}`, data), {\n        meta: {\n            invalidates: \"environment-variables\"\n        }\n    });\n}\n// Services API hooks\nfunction useServices() {\n    return useApiQuery([\n        \"services\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/services\"), {\n        refetchInterval: 5000\n    });\n}\nfunction useServiceAction() {\n    return useApiMutation(({ serviceId, action })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/services/${serviceId}/${action}`), {\n        meta: {\n            invalidates: \"services\"\n        }\n    });\n}\n// Database API hooks\nfunction useDatabases() {\n    return useApiQuery([\n        \"databases\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/databases\"), {\n        refetchInterval: 10000\n    });\n}\nfunction useDatabaseQuery() {\n    return useApiMutation(({ databaseId, query })=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/databases/${databaseId}/query`, {\n            query\n        }));\n}\n// Deployment API hooks\nfunction useDeployments(environment) {\n    return useApiQuery([\n        \"deployments\",\n        environment || \"all\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/deployments\", {\n            params: {\n                environment\n            }\n        }), {\n        refetchInterval: 5000\n    });\n}\nfunction useCreateDeployment() {\n    return useApiMutation((data)=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/deployments\", data), {\n        meta: {\n            invalidates: \"deployments\"\n        }\n    });\n}\n// Monitoring API hooks\nfunction useSystemMetrics() {\n    return useApiQuery([\n        \"system-metrics\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/system\"), {\n        refetchInterval: 5000\n    });\n}\nfunction useLogs(filters) {\n    return useApiQuery([\n        \"logs\",\n        filters\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/logs\", {\n            params: filters\n        }), {\n        enabled: !!filters,\n        staleTime: 0\n    });\n}\nfunction useAlerts(filters = {}) {\n    return useApiQuery([\n        \"alerts\",\n        filters\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/monitoring/alerts\", {\n            params: filters\n        }), {\n        refetchInterval: 10000\n    });\n}\n// Health check hook\nfunction useHealthCheck() {\n    return useApiQuery([\n        \"health\"\n    ], ()=>_lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/health\"), {\n        refetchInterval: 30000,\n        retry: false,\n        staleTime: 0\n    });\n}\n// Connection status hook\nfunction useConnectionStatus() {\n    const { data: healthData, isError, isLoading } = useHealthCheck();\n    return {\n        isOnline: !isError && !!healthData,\n        isChecking: isLoading,\n        status: isError ? \"offline\" : healthData ? \"online\" : \"checking\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   createWebSocketClient: () => (/* binding */ createWebSocketClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nclass ApiClient {\n    constructor(){\n        this.retryCount = 0;\n        this.maxRetries = 3;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            baseURL: API_BASE_URL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getStoredToken)();\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            config.headers[\"X-Request-ID\"] = this.generateRequestId();\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            this.retryCount = 0;\n            return response;\n        }, async (error)=>{\n            const originalRequest = error.config;\n            if (this.shouldRetry(error) && this.retryCount < this.maxRetries) {\n                this.retryCount++;\n                await this.delay(1000 * this.retryCount);\n                return this.client(originalRequest);\n            }\n            if (error.response) {\n                const { status, data } = error.response;\n                switch(status){\n                    case 401:\n                        this.handleUnauthorized();\n                        break;\n                    case 403:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Access forbidden\");\n                        break;\n                    case 404:\n                        console.warn(\"Resource not found:\", originalRequest.url);\n                        break;\n                    case 429:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Too many requests. Please try again later.\");\n                        break;\n                    case 500:\n                    case 502:\n                    case 503:\n                    case 504:\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Server error. Please try again.\");\n                        break;\n                    default:\n                        if (data?.message) {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(data.message);\n                        }\n                }\n                return Promise.reject({\n                    message: data?.message || error.message,\n                    status,\n                    code: data?.code,\n                    details: data?.details\n                });\n            }\n            if (error.code === \"NETWORK_ERROR\" || !error.response) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n            }\n            return Promise.reject({\n                message: error.message || \"An unexpected error occurred\",\n                status: 0\n            });\n        });\n    }\n    shouldRetry(error) {\n        return !error.response || error.response.status >= 500 || error.code === \"NETWORK_ERROR\" || error.code === \"TIMEOUT\";\n    }\n    delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    generateRequestId() {\n        return Math.random().toString(36).substring(2, 15);\n    }\n    handleUnauthorized() {\n        if (false) {}\n    }\n    async get(url, config) {\n        return this.client.get(url, config);\n    }\n    async post(url, data, config) {\n        return this.client.post(url, data, config);\n    }\n    async put(url, data, config) {\n        return this.client.put(url, data, config);\n    }\n    async patch(url, data, config) {\n        return this.client.patch(url, data, config);\n    }\n    async delete(url, config) {\n        return this.client.delete(url, config);\n    }\n    async healthCheck() {\n        try {\n            const response = await this.get(\"/health\");\n            return response.status === 200;\n        } catch (error) {\n            console.error(\"Health check failed:\", error);\n            return false;\n        }\n    }\n}\nconst apiClient = new ApiClient();\n// WebSocket client for real-time updates\nfunction createWebSocketClient(namespace = \"\") {\n    const wsUrl = \"ws://localhost:3000\" || 0;\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getStoredToken)();\n    const ws = new WebSocket(`${wsUrl}${namespace}?token=${token}`);\n    ws.onopen = ()=>{\n        console.log(`WebSocket connected to ${namespace}`);\n    };\n    ws.onerror = (error)=>{\n        console.error(\"WebSocket error:\", error);\n    };\n    ws.onclose = ()=>{\n        console.log(\"WebSocket disconnected\");\n    // Implement reconnection logic if needed\n    };\n    return ws;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9jbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0Y7QUFDeEM7QUFDQztBQUV6QyxNQUFNRyxlQUFlQyx1QkFBK0IsSUFBSTtBQVN4RCxNQUFNRztJQUtKQyxhQUFjO2FBSE5DLGFBQWE7YUFDYkMsYUFBYTtRQUduQixJQUFJLENBQUNDLE1BQU0sR0FBR1gsNkNBQUtBLENBQUNZLE1BQU0sQ0FBQztZQUN6QkMsU0FBU1Y7WUFDVFcsU0FBUztZQUNUQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDQyxpQkFBaUI7SUFDeEI7SUFFUUEsb0JBQW9CO1FBQzFCLHNCQUFzQjtRQUN0QixJQUFJLENBQUNMLE1BQU0sQ0FBQ00sWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FDbEMsQ0FBQ0M7WUFDQyxNQUFNQyxRQUFRbkIscURBQWNBO1lBQzVCLElBQUltQixPQUFPO2dCQUNURCxPQUFPTCxPQUFPLENBQUNPLGFBQWEsR0FBRyxDQUFDLE9BQU8sRUFBRUQsTUFBTSxDQUFDO1lBQ2xEO1lBQ0FELE9BQU9MLE9BQU8sQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDUSxpQkFBaUI7WUFDdkQsT0FBT0g7UUFDVCxHQUNBLENBQUNJLFFBQVVDLFFBQVFDLE1BQU0sQ0FBQ0Y7UUFHNUIsdUJBQXVCO1FBQ3ZCLElBQUksQ0FBQ2IsTUFBTSxDQUFDTSxZQUFZLENBQUNVLFFBQVEsQ0FBQ1IsR0FBRyxDQUNuQyxDQUFDUTtZQUNDLElBQUksQ0FBQ2xCLFVBQVUsR0FBRztZQUNsQixPQUFPa0I7UUFDVCxHQUNBLE9BQU9IO1lBQ0wsTUFBTUksa0JBQWtCSixNQUFNSixNQUFNO1lBRXBDLElBQUksSUFBSSxDQUFDUyxXQUFXLENBQUNMLFVBQVUsSUFBSSxDQUFDZixVQUFVLEdBQUcsSUFBSSxDQUFDQyxVQUFVLEVBQUU7Z0JBQ2hFLElBQUksQ0FBQ0QsVUFBVTtnQkFDZixNQUFNLElBQUksQ0FBQ3FCLEtBQUssQ0FBQyxPQUFPLElBQUksQ0FBQ3JCLFVBQVU7Z0JBQ3ZDLE9BQU8sSUFBSSxDQUFDRSxNQUFNLENBQUNpQjtZQUNyQjtZQUVBLElBQUlKLE1BQU1HLFFBQVEsRUFBRTtnQkFDbEIsTUFBTSxFQUFFSSxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHUixNQUFNRyxRQUFRO2dCQUV2QyxPQUFRSTtvQkFDTixLQUFLO3dCQUNILElBQUksQ0FBQ0Usa0JBQWtCO3dCQUN2QjtvQkFDRixLQUFLO3dCQUNIaEMsa0RBQUtBLENBQUN1QixLQUFLLENBQUM7d0JBQ1o7b0JBQ0YsS0FBSzt3QkFDSFUsUUFBUUMsSUFBSSxDQUFDLHVCQUF1QlAsZ0JBQWdCUSxHQUFHO3dCQUN2RDtvQkFDRixLQUFLO3dCQUNIbkMsa0RBQUtBLENBQUN1QixLQUFLLENBQUM7d0JBQ1o7b0JBQ0YsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSzt3QkFDSHZCLGtEQUFLQSxDQUFDdUIsS0FBSyxDQUFDO3dCQUNaO29CQUNGO3dCQUNFLElBQUlRLE1BQU1LLFNBQVM7NEJBQ2pCcEMsa0RBQUtBLENBQUN1QixLQUFLLENBQUNRLEtBQUtLLE9BQU87d0JBQzFCO2dCQUNKO2dCQUVBLE9BQU9aLFFBQVFDLE1BQU0sQ0FBQztvQkFDcEJXLFNBQVNMLE1BQU1LLFdBQVdiLE1BQU1hLE9BQU87b0JBQ3ZDTjtvQkFDQU8sTUFBTU4sTUFBTU07b0JBQ1pDLFNBQVNQLE1BQU1PO2dCQUNqQjtZQUNGO1lBRUEsSUFBSWYsTUFBTWMsSUFBSSxLQUFLLG1CQUFtQixDQUFDZCxNQUFNRyxRQUFRLEVBQUU7Z0JBQ3JEMUIsa0RBQUtBLENBQUN1QixLQUFLLENBQUM7WUFDZDtZQUVBLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQztnQkFDcEJXLFNBQVNiLE1BQU1hLE9BQU8sSUFBSTtnQkFDMUJOLFFBQVE7WUFDVjtRQUNGO0lBRUo7SUFFUUYsWUFBWUwsS0FBVSxFQUFXO1FBQ3ZDLE9BQ0UsQ0FBQ0EsTUFBTUcsUUFBUSxJQUNmSCxNQUFNRyxRQUFRLENBQUNJLE1BQU0sSUFBSSxPQUN6QlAsTUFBTWMsSUFBSSxLQUFLLG1CQUNmZCxNQUFNYyxJQUFJLEtBQUs7SUFFbkI7SUFFUVIsTUFBTVUsRUFBVSxFQUFpQjtRQUN2QyxPQUFPLElBQUlmLFFBQVFnQixDQUFBQSxVQUFXQyxXQUFXRCxTQUFTRDtJQUNwRDtJQUVRakIsb0JBQTRCO1FBQ2xDLE9BQU9vQixLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztJQUNqRDtJQUVRYixxQkFBcUI7UUFDM0IsSUFBSSxLQUFrQixFQUFhLEVBU2xDO0lBQ0g7SUFFQSxNQUFNb0IsSUFBYWpCLEdBQVcsRUFBRWhCLE1BQTJCLEVBQTZCO1FBQ3RGLE9BQU8sSUFBSSxDQUFDVCxNQUFNLENBQUMwQyxHQUFHLENBQUNqQixLQUFLaEI7SUFDOUI7SUFFQSxNQUFNa0MsS0FBY2xCLEdBQVcsRUFBRUosSUFBVSxFQUFFWixNQUEyQixFQUE2QjtRQUNuRyxPQUFPLElBQUksQ0FBQ1QsTUFBTSxDQUFDMkMsSUFBSSxDQUFDbEIsS0FBS0osTUFBTVo7SUFDckM7SUFFQSxNQUFNbUMsSUFBYW5CLEdBQVcsRUFBRUosSUFBVSxFQUFFWixNQUEyQixFQUE2QjtRQUNsRyxPQUFPLElBQUksQ0FBQ1QsTUFBTSxDQUFDNEMsR0FBRyxDQUFDbkIsS0FBS0osTUFBTVo7SUFDcEM7SUFFQSxNQUFNb0MsTUFBZXBCLEdBQVcsRUFBRUosSUFBVSxFQUFFWixNQUEyQixFQUE2QjtRQUNwRyxPQUFPLElBQUksQ0FBQ1QsTUFBTSxDQUFDNkMsS0FBSyxDQUFDcEIsS0FBS0osTUFBTVo7SUFDdEM7SUFFQSxNQUFNcUMsT0FBZ0JyQixHQUFXLEVBQUVoQixNQUEyQixFQUE2QjtRQUN6RixPQUFPLElBQUksQ0FBQ1QsTUFBTSxDQUFDOEMsTUFBTSxDQUFDckIsS0FBS2hCO0lBQ2pDO0lBRUEsTUFBTXNDLGNBQWdDO1FBQ3BDLElBQUk7WUFDRixNQUFNL0IsV0FBVyxNQUFNLElBQUksQ0FBQzBCLEdBQUcsQ0FBQztZQUNoQyxPQUFPMUIsU0FBU0ksTUFBTSxLQUFLO1FBQzdCLEVBQUUsT0FBT1AsT0FBTztZQUNkVSxRQUFRVixLQUFLLENBQUMsd0JBQXdCQTtZQUN0QyxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRU8sTUFBTW1DLFlBQVksSUFBSXBELFlBQVk7QUFFekMseUNBQXlDO0FBQ2xDLFNBQVNxRCxzQkFBc0JDLFlBQW9CLEVBQUU7SUFDMUQsTUFBTUMsUUFBUTFELHFCQUE4QixJQUFJO0lBQ2hELE1BQU1pQixRQUFRbkIscURBQWNBO0lBRTVCLE1BQU04RCxLQUFLLElBQUlDLFVBQVUsQ0FBQyxFQUFFSCxNQUFNLEVBQUVELFVBQVUsT0FBTyxFQUFFeEMsTUFBTSxDQUFDO0lBRTlEMkMsR0FBR0UsTUFBTSxHQUFHO1FBQ1ZoQyxRQUFRaUMsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVOLFVBQVUsQ0FBQztJQUNuRDtJQUVBRyxHQUFHSSxPQUFPLEdBQUcsQ0FBQzVDO1FBQ1pVLFFBQVFWLEtBQUssQ0FBQyxvQkFBb0JBO0lBQ3BDO0lBRUF3QyxHQUFHSyxPQUFPLEdBQUc7UUFDWG5DLFFBQVFpQyxHQUFHLENBQUM7SUFDWix5Q0FBeUM7SUFDM0M7SUFFQSxPQUFPSDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tbWFuZC1jZW50ZXItZGV2ZWxvcGVyLWRhc2hib2FyZC8uL3NyYy9saWIvYXBpL2NsaWVudC50cz8xNTliIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcywgeyBBeGlvc0luc3RhbmNlLCBBeGlvc1JlcXVlc3RDb25maWcsIEF4aW9zUmVzcG9uc2UgfSBmcm9tICdheGlvcyc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5pbXBvcnQgeyBnZXRTdG9yZWRUb2tlbiB9IGZyb20gJy4uL2F1dGgnO1xuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcblxuZXhwb3J0IGludGVyZmFjZSBBcGlFcnJvciB7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgc3RhdHVzOiBudW1iZXI7XG4gIGNvZGU/OiBzdHJpbmc7XG4gIGRldGFpbHM/OiBhbnk7XG59XG5cbmNsYXNzIEFwaUNsaWVudCB7XG4gIHByaXZhdGUgY2xpZW50OiBBeGlvc0luc3RhbmNlO1xuICBwcml2YXRlIHJldHJ5Q291bnQgPSAwO1xuICBwcml2YXRlIG1heFJldHJpZXMgPSAzO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuY2xpZW50ID0gYXhpb3MuY3JlYXRlKHtcbiAgICAgIGJhc2VVUkw6IEFQSV9CQVNFX1VSTCxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHRoaXMuc2V0dXBJbnRlcmNlcHRvcnMoKTtcbiAgfVxuXG4gIHByaXZhdGUgc2V0dXBJbnRlcmNlcHRvcnMoKSB7XG4gICAgLy8gUmVxdWVzdCBpbnRlcmNlcHRvclxuICAgIHRoaXMuY2xpZW50LmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShcbiAgICAgIChjb25maWcpID0+IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRTdG9yZWRUb2tlbigpO1xuICAgICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgICBjb25maWcuaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke3Rva2VufWA7XG4gICAgICAgIH1cbiAgICAgICAgY29uZmlnLmhlYWRlcnNbJ1gtUmVxdWVzdC1JRCddID0gdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpO1xuICAgICAgICByZXR1cm4gY29uZmlnO1xuICAgICAgfSxcbiAgICAgIChlcnJvcikgPT4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXG4gICAgKTtcblxuICAgIC8vIFJlc3BvbnNlIGludGVyY2VwdG9yXG4gICAgdGhpcy5jbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgICAgIChyZXNwb25zZSkgPT4ge1xuICAgICAgICB0aGlzLnJldHJ5Q291bnQgPSAwO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICB9LFxuICAgICAgYXN5bmMgKGVycm9yKSA9PiB7XG4gICAgICAgIGNvbnN0IG9yaWdpbmFsUmVxdWVzdCA9IGVycm9yLmNvbmZpZztcblxuICAgICAgICBpZiAodGhpcy5zaG91bGRSZXRyeShlcnJvcikgJiYgdGhpcy5yZXRyeUNvdW50IDwgdGhpcy5tYXhSZXRyaWVzKSB7XG4gICAgICAgICAgdGhpcy5yZXRyeUNvdW50Kys7XG4gICAgICAgICAgYXdhaXQgdGhpcy5kZWxheSgxMDAwICogdGhpcy5yZXRyeUNvdW50KTtcbiAgICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQob3JpZ2luYWxSZXF1ZXN0KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChlcnJvci5yZXNwb25zZSkge1xuICAgICAgICAgIGNvbnN0IHsgc3RhdHVzLCBkYXRhIH0gPSBlcnJvci5yZXNwb25zZTtcbiAgICAgICAgICBcbiAgICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgICAgICAgY2FzZSA0MDE6XG4gICAgICAgICAgICAgIHRoaXMuaGFuZGxlVW5hdXRob3JpemVkKCk7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA0MDM6XG4gICAgICAgICAgICAgIHRvYXN0LmVycm9yKCdBY2Nlc3MgZm9yYmlkZGVuJyk7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA0MDQ6XG4gICAgICAgICAgICAgIGNvbnNvbGUud2FybignUmVzb3VyY2Ugbm90IGZvdW5kOicsIG9yaWdpbmFsUmVxdWVzdC51cmwpO1xuICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgNDI5OlxuICAgICAgICAgICAgICB0b2FzdC5lcnJvcignVG9vIG1hbnkgcmVxdWVzdHMuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuJyk7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA1MDA6XG4gICAgICAgICAgICBjYXNlIDUwMjpcbiAgICAgICAgICAgIGNhc2UgNTAzOlxuICAgICAgICAgICAgY2FzZSA1MDQ6XG4gICAgICAgICAgICAgIHRvYXN0LmVycm9yKCdTZXJ2ZXIgZXJyb3IuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgaWYgKGRhdGE/Lm1lc3NhZ2UpIHtcbiAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihkYXRhLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KHtcbiAgICAgICAgICAgIG1lc3NhZ2U6IGRhdGE/Lm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgIHN0YXR1cyxcbiAgICAgICAgICAgIGNvZGU6IGRhdGE/LmNvZGUsXG4gICAgICAgICAgICBkZXRhaWxzOiBkYXRhPy5kZXRhaWxzLFxuICAgICAgICAgIH0gYXMgQXBpRXJyb3IpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdORVRXT1JLX0VSUk9SJyB8fCAhZXJyb3IucmVzcG9uc2UpIHtcbiAgICAgICAgICB0b2FzdC5lcnJvcignTmV0d29yayBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbi4nKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdCh7XG4gICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcsXG4gICAgICAgICAgc3RhdHVzOiAwLFxuICAgICAgICB9IGFzIEFwaUVycm9yKTtcbiAgICAgIH1cbiAgICApO1xuICB9XG5cbiAgcHJpdmF0ZSBzaG91bGRSZXRyeShlcnJvcjogYW55KTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIChcbiAgICAgICFlcnJvci5yZXNwb25zZSB8fFxuICAgICAgZXJyb3IucmVzcG9uc2Uuc3RhdHVzID49IDUwMCB8fFxuICAgICAgZXJyb3IuY29kZSA9PT0gJ05FVFdPUktfRVJST1InIHx8XG4gICAgICBlcnJvci5jb2RlID09PSAnVElNRU9VVCdcbiAgICApO1xuICB9XG5cbiAgcHJpdmF0ZSBkZWxheShtczogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBtcykpO1xuICB9XG5cbiAgcHJpdmF0ZSBnZW5lcmF0ZVJlcXVlc3RJZCgpOiBzdHJpbmcge1xuICAgIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTUpO1xuICB9XG5cbiAgcHJpdmF0ZSBoYW5kbGVVbmF1dGhvcml6ZWQoKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY29tbWFuZC1jZW50ZXItdG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjb21tYW5kLWNlbnRlci1yZWZyZXNoLXRva2VuJyk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY29tbWFuZC1jZW50ZXItdXNlcicpO1xuICAgICAgXG4gICAgICBpZiAod2luZG93LmxvY2F0aW9uLnBhdGhuYW1lICE9PSAnL2xvZ2luJykge1xuICAgICAgICB0b2FzdC5lcnJvcignU2Vzc2lvbiBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLicpO1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdldDxUID0gYW55Pih1cmw6IHN0cmluZywgY29uZmlnPzogQXhpb3NSZXF1ZXN0Q29uZmlnKTogUHJvbWlzZTxBeGlvc1Jlc3BvbnNlPFQ+PiB7XG4gICAgcmV0dXJuIHRoaXMuY2xpZW50LmdldCh1cmwsIGNvbmZpZyk7XG4gIH1cblxuICBhc3luYyBwb3N0PFQgPSBhbnk+KHVybDogc3RyaW5nLCBkYXRhPzogYW55LCBjb25maWc/OiBBeGlvc1JlcXVlc3RDb25maWcpOiBQcm9taXNlPEF4aW9zUmVzcG9uc2U8VD4+IHtcbiAgICByZXR1cm4gdGhpcy5jbGllbnQucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyk7XG4gIH1cblxuICBhc3luYyBwdXQ8VCA9IGFueT4odXJsOiBzdHJpbmcsIGRhdGE/OiBhbnksIGNvbmZpZz86IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXhpb3NSZXNwb25zZTxUPj4ge1xuICAgIHJldHVybiB0aGlzLmNsaWVudC5wdXQodXJsLCBkYXRhLCBjb25maWcpO1xuICB9XG5cbiAgYXN5bmMgcGF0Y2g8VCA9IGFueT4odXJsOiBzdHJpbmcsIGRhdGE/OiBhbnksIGNvbmZpZz86IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXhpb3NSZXNwb25zZTxUPj4ge1xuICAgIHJldHVybiB0aGlzLmNsaWVudC5wYXRjaCh1cmwsIGRhdGEsIGNvbmZpZyk7XG4gIH1cblxuICBhc3luYyBkZWxldGU8VCA9IGFueT4odXJsOiBzdHJpbmcsIGNvbmZpZz86IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXhpb3NSZXNwb25zZTxUPj4ge1xuICAgIHJldHVybiB0aGlzLmNsaWVudC5kZWxldGUodXJsLCBjb25maWcpO1xuICB9XG5cbiAgYXN5bmMgaGVhbHRoQ2hlY2soKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXQoJy9oZWFsdGgnKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5zdGF0dXMgPT09IDIwMDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignSGVhbHRoIGNoZWNrIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KCk7XG5cbi8vIFdlYlNvY2tldCBjbGllbnQgZm9yIHJlYWwtdGltZSB1cGRhdGVzXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlV2ViU29ja2V0Q2xpZW50KG5hbWVzcGFjZTogc3RyaW5nID0gJycpIHtcbiAgY29uc3Qgd3NVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19XU19VUkwgfHwgJ3dzOi8vbG9jYWxob3N0OjMwMDAnO1xuICBjb25zdCB0b2tlbiA9IGdldFN0b3JlZFRva2VuKCk7XG4gIFxuICBjb25zdCB3cyA9IG5ldyBXZWJTb2NrZXQoYCR7d3NVcmx9JHtuYW1lc3BhY2V9P3Rva2VuPSR7dG9rZW59YCk7XG4gIFxuICB3cy5vbm9wZW4gPSAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coYFdlYlNvY2tldCBjb25uZWN0ZWQgdG8gJHtuYW1lc3BhY2V9YCk7XG4gIH07XG4gIFxuICB3cy5vbmVycm9yID0gKGVycm9yKSA9PiB7XG4gICAgY29uc29sZS5lcnJvcignV2ViU29ja2V0IGVycm9yOicsIGVycm9yKTtcbiAgfTtcbiAgXG4gIHdzLm9uY2xvc2UgPSAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBkaXNjb25uZWN0ZWQnKTtcbiAgICAvLyBJbXBsZW1lbnQgcmVjb25uZWN0aW9uIGxvZ2ljIGlmIG5lZWRlZFxuICB9O1xuICBcbiAgcmV0dXJuIHdzO1xufSJdLCJuYW1lcyI6WyJheGlvcyIsInRvYXN0IiwiZ2V0U3RvcmVkVG9rZW4iLCJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIkFwaUNsaWVudCIsImNvbnN0cnVjdG9yIiwicmV0cnlDb3VudCIsIm1heFJldHJpZXMiLCJjbGllbnQiLCJjcmVhdGUiLCJiYXNlVVJMIiwidGltZW91dCIsImhlYWRlcnMiLCJzZXR1cEludGVyY2VwdG9ycyIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsIkF1dGhvcml6YXRpb24iLCJnZW5lcmF0ZVJlcXVlc3RJZCIsImVycm9yIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwib3JpZ2luYWxSZXF1ZXN0Iiwic2hvdWxkUmV0cnkiLCJkZWxheSIsInN0YXR1cyIsImRhdGEiLCJoYW5kbGVVbmF1dGhvcml6ZWQiLCJjb25zb2xlIiwid2FybiIsInVybCIsIm1lc3NhZ2UiLCJjb2RlIiwiZGV0YWlscyIsIm1zIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJwYXRobmFtZSIsImhyZWYiLCJnZXQiLCJwb3N0IiwicHV0IiwicGF0Y2giLCJkZWxldGUiLCJoZWFsdGhDaGVjayIsImFwaUNsaWVudCIsImNyZWF0ZVdlYlNvY2tldENsaWVudCIsIm5hbWVzcGFjZSIsIndzVXJsIiwiTkVYVF9QVUJMSUNfV1NfVVJMIiwid3MiLCJXZWJTb2NrZXQiLCJvbm9wZW4iLCJsb2ciLCJvbmVycm9yIiwib25jbG9zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken)\n/* harmony export */ });\nconst DEVELOPER_EMAIL = \"<EMAIL>\";\nconst DEVELOPER_PASSWORD = \"developer123!\";\nclass AuthService {\n    static{\n        this.TOKEN_KEY = \"command-center-token\";\n    }\n    static{\n        this.REFRESH_TOKEN_KEY = \"command-center-refresh-token\";\n    }\n    static{\n        this.USER_KEY = \"command-center-user\";\n    }\n    static async login(credentials) {\n        // Special developer authentication\n        if (credentials.email === DEVELOPER_EMAIL && credentials.password === DEVELOPER_PASSWORD) {\n            const user = {\n                id: \"dev-001\",\n                email: DEVELOPER_EMAIL,\n                name: \"Developer\",\n                role: \"developer\",\n                permissions: [\n                    \"docker.manage\",\n                    \"environment.configure\",\n                    \"services.control\",\n                    \"database.manage\",\n                    \"deployment.execute\",\n                    \"backup.manage\",\n                    \"monitoring.view\",\n                    \"logs.view\",\n                    \"api.test\",\n                    \"system.admin\"\n                ]\n            };\n            const response = {\n                user,\n                accessToken: btoa(JSON.stringify({\n                    user,\n                    exp: Date.now() + 3600000\n                })),\n                refreshToken: btoa(JSON.stringify({\n                    userId: user.id,\n                    exp: Date.now() + 86400000 * 7\n                })) // 7 days\n            };\n            // Store tokens and user\n            this.setTokens(response.accessToken, response.refreshToken);\n            this.setUser(user);\n            return response;\n        }\n        // For production, make actual API call\n        try {\n            const response = await fetch(`${\"http://localhost:3000\"}/api/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(credentials)\n            });\n            if (!response.ok) {\n                throw new Error(\"Invalid credentials\");\n            }\n            const data = await response.json();\n            this.setTokens(data.accessToken, data.refreshToken);\n            this.setUser(data.user);\n            return data;\n        } catch (error) {\n            throw new Error(\"Invalid credentials\");\n        }\n    }\n    static logout() {\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n        localStorage.removeItem(this.USER_KEY);\n    }\n    static getToken() {\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static getRefreshToken() {\n        return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n    }\n    static getUser() {\n        const userStr = localStorage.getItem(this.USER_KEY);\n        if (!userStr) return null;\n        try {\n            return JSON.parse(userStr);\n        } catch  {\n            return null;\n        }\n    }\n    static isAuthenticated() {\n        const token = this.getToken();\n        if (!token) return false;\n        try {\n            const payload = JSON.parse(atob(token));\n            return payload.exp > Date.now();\n        } catch  {\n            return false;\n        }\n    }\n    static hasPermission(permission) {\n        const user = this.getUser();\n        if (!user) return false;\n        return user.permissions.includes(permission);\n    }\n    static setTokens(accessToken, refreshToken) {\n        localStorage.setItem(this.TOKEN_KEY, accessToken);\n        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);\n    }\n    static setUser(user) {\n        localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n    static async refreshToken() {\n        const refreshToken = this.getRefreshToken();\n        if (!refreshToken) return null;\n        try {\n            const response = await fetch(`${\"http://localhost:3000\"}/api/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${refreshToken}`\n                }\n            });\n            if (!response.ok) {\n                this.logout();\n                return null;\n            }\n            const data = await response.json();\n            this.setTokens(data.accessToken, data.refreshToken);\n            this.setUser(data.user);\n            return data;\n        } catch  {\n            this.logout();\n            return null;\n        }\n    }\n}\nfunction getStoredToken() {\n    return AuthService.getToken();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatBytes(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    const parts = [];\n    if (hours > 0) parts.push(`${hours}h`);\n    if (minutes > 0) parts.push(`${minutes}m`);\n    if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);\n    return parts.join(\" \");\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        dateStyle: \"medium\",\n        timeStyle: \"short\"\n    }).format(new Date(date));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth.store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth.store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set)=>({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        error: null,\n        login: async (credentials)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.login(credentials);\n                set({\n                    user: response.user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : \"Login failed\"\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.logout();\n            set({\n                user: null,\n                isAuthenticated: false,\n                isLoading: false,\n                error: null\n            });\n        },\n        checkAuth: ()=>{\n            const user = _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.getUser();\n            const isAuthenticated = _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.isAuthenticated();\n            set({\n                user,\n                isAuthenticated,\n                isLoading: false,\n                error: null\n            });\n        },\n        refreshAuth: async ()=>{\n            try {\n                const response = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.AuthService.refreshToken();\n                if (response) {\n                    set({\n                        user: response.user,\n                        isAuthenticated: true,\n                        error: null\n                    });\n                } else {\n                    set({\n                        user: null,\n                        isAuthenticated: false,\n                        error: null\n                    });\n                }\n            } catch (error) {\n                set({\n                    user: null,\n                    isAuthenticated: false,\n                    error: error instanceof Error ? error.message : \"Auth refresh failed\"\n                });\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmVzL2F1dGguc3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBRVE7QUFTbEMsTUFBTUUsZUFBZUYsK0NBQU1BLENBQVksQ0FBQ0csTUFBUztRQUN0REMsTUFBTTtRQUNOQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFDWEMsT0FBTztRQUVQQyxPQUFPLE9BQU9DO1lBQ1pOLElBQUk7Z0JBQUVHLFdBQVc7Z0JBQU1DLE9BQU87WUFBSztZQUNuQyxJQUFJO2dCQUNGLE1BQU1HLFdBQVcsTUFBTVQsa0RBQVdBLENBQUNPLEtBQUssQ0FBQ0M7Z0JBQ3pDTixJQUFJO29CQUNGQyxNQUFNTSxTQUFTTixJQUFJO29CQUNuQkMsaUJBQWlCO29CQUNqQkMsV0FBVztvQkFDWEMsT0FBTztnQkFDVDtZQUNGLEVBQUUsT0FBT0EsT0FBTztnQkFDZEosSUFBSTtvQkFDRkMsTUFBTTtvQkFDTkMsaUJBQWlCO29CQUNqQkMsV0FBVztvQkFDWEMsT0FBT0EsaUJBQWlCSSxRQUFRSixNQUFNSyxPQUFPLEdBQUc7Z0JBQ2xEO2dCQUNBLE1BQU1MO1lBQ1I7UUFDRjtRQUVBTSxRQUFRO1lBQ05aLGtEQUFXQSxDQUFDWSxNQUFNO1lBQ2xCVixJQUFJO2dCQUNGQyxNQUFNO2dCQUNOQyxpQkFBaUI7Z0JBQ2pCQyxXQUFXO2dCQUNYQyxPQUFPO1lBQ1Q7UUFDRjtRQUVBTyxXQUFXO1lBQ1QsTUFBTVYsT0FBT0gsa0RBQVdBLENBQUNjLE9BQU87WUFDaEMsTUFBTVYsa0JBQWtCSixrREFBV0EsQ0FBQ0ksZUFBZTtZQUVuREYsSUFBSTtnQkFDRkM7Z0JBQ0FDO2dCQUNBQyxXQUFXO2dCQUNYQyxPQUFPO1lBQ1Q7UUFDRjtRQUVBUyxhQUFhO1lBQ1gsSUFBSTtnQkFDRixNQUFNTixXQUFXLE1BQU1ULGtEQUFXQSxDQUFDZ0IsWUFBWTtnQkFDL0MsSUFBSVAsVUFBVTtvQkFDWlAsSUFBSTt3QkFDRkMsTUFBTU0sU0FBU04sSUFBSTt3QkFDbkJDLGlCQUFpQjt3QkFDakJFLE9BQU87b0JBQ1Q7Z0JBQ0YsT0FBTztvQkFDTEosSUFBSTt3QkFDRkMsTUFBTTt3QkFDTkMsaUJBQWlCO3dCQUNqQkUsT0FBTztvQkFDVDtnQkFDRjtZQUNGLEVBQUUsT0FBT0EsT0FBTztnQkFDZEosSUFBSTtvQkFDRkMsTUFBTTtvQkFDTkMsaUJBQWlCO29CQUNqQkUsT0FBT0EsaUJBQWlCSSxRQUFRSixNQUFNSyxPQUFPLEdBQUc7Z0JBQ2xEO1lBQ0Y7UUFDRjtJQUNGLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21tYW5kLWNlbnRlci1kZXZlbG9wZXItZGFzaGJvYXJkLy4vc3JjL3N0b3Jlcy9hdXRoLnN0b3JlLnRzP2U2Y2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XG5pbXBvcnQgeyBBdXRoU3RhdGUsIExvZ2luQ3JlZGVudGlhbHMgfSBmcm9tICdAL3R5cGVzL2F1dGgnO1xuaW1wb3J0IHsgQXV0aFNlcnZpY2UgfSBmcm9tICdAL2xpYi9hdXRoJztcblxuaW50ZXJmYWNlIEF1dGhTdG9yZSBleHRlbmRzIEF1dGhTdGF0ZSB7XG4gIGxvZ2luOiAoY3JlZGVudGlhbHM6IExvZ2luQ3JlZGVudGlhbHMpID0+IFByb21pc2U8dm9pZD47XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbiAgY2hlY2tBdXRoOiAoKSA9PiB2b2lkO1xuICByZWZyZXNoQXV0aDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZUF1dGhTdG9yZSA9IGNyZWF0ZTxBdXRoU3RvcmU+KChzZXQpID0+ICh7XG4gIHVzZXI6IG51bGwsXG4gIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gIGlzTG9hZGluZzogdHJ1ZSxcbiAgZXJyb3I6IG51bGwsXG5cbiAgbG9naW46IGFzeW5jIChjcmVkZW50aWFscykgPT4ge1xuICAgIHNldCh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgQXV0aFNlcnZpY2UubG9naW4oY3JlZGVudGlhbHMpO1xuICAgICAgc2V0KHtcbiAgICAgICAgdXNlcjogcmVzcG9uc2UudXNlcixcbiAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICBlcnJvcjogbnVsbFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldCh7XG4gICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdMb2dpbiBmYWlsZWQnXG4gICAgICB9KTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICBsb2dvdXQ6ICgpID0+IHtcbiAgICBBdXRoU2VydmljZS5sb2dvdXQoKTtcbiAgICBzZXQoe1xuICAgICAgdXNlcjogbnVsbCxcbiAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgZXJyb3I6IG51bGxcbiAgICB9KTtcbiAgfSxcblxuICBjaGVja0F1dGg6ICgpID0+IHtcbiAgICBjb25zdCB1c2VyID0gQXV0aFNlcnZpY2UuZ2V0VXNlcigpO1xuICAgIGNvbnN0IGlzQXV0aGVudGljYXRlZCA9IEF1dGhTZXJ2aWNlLmlzQXV0aGVudGljYXRlZCgpO1xuICAgIFxuICAgIHNldCh7XG4gICAgICB1c2VyLFxuICAgICAgaXNBdXRoZW50aWNhdGVkLFxuICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgIGVycm9yOiBudWxsXG4gICAgfSk7XG4gIH0sXG5cbiAgcmVmcmVzaEF1dGg6IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBdXRoU2VydmljZS5yZWZyZXNoVG9rZW4oKTtcbiAgICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgICBzZXQoe1xuICAgICAgICAgIHVzZXI6IHJlc3BvbnNlLnVzZXIsXG4gICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICAgIGVycm9yOiBudWxsXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0KHtcbiAgICAgICAgICB1c2VyOiBudWxsLFxuICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IG51bGxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldCh7XG4gICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdBdXRoIHJlZnJlc2ggZmFpbGVkJ1xuICAgICAgfSk7XG4gICAgfVxuICB9XG59KSk7Il0sIm5hbWVzIjpbImNyZWF0ZSIsIkF1dGhTZXJ2aWNlIiwidXNlQXV0aFN0b3JlIiwic2V0IiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsImVycm9yIiwibG9naW4iLCJjcmVkZW50aWFscyIsInJlc3BvbnNlIiwiRXJyb3IiLCJtZXNzYWdlIiwibG9nb3V0IiwiY2hlY2tBdXRoIiwiZ2V0VXNlciIsInJlZnJlc2hBdXRoIiwicmVmcmVzaFRva2VuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth.store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"97c5c484344f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tbWFuZC1jZW50ZXItZGV2ZWxvcGVyLWRhc2hib2FyZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MmYzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk3YzVjNDg0MzQ0ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_common_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/ErrorBoundary */ \"(rsc)/./src/components/common/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/common/ConnectionStatus */ \"(rsc)/./src/components/common/ConnectionStatus.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Command Center - Developer Dashboard\",\n    description: \"Manage and monitor your Command Center infrastructure\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__.ErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__.OfflineNotice, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this),\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Luminar/Luminar-Dashboard/src/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/login/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/common/ConnectionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/common/ConnectionStatus.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionStatus: () => (/* binding */ e0),
/* harmony export */   OfflineNotice: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx#ConnectionStatus`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ConnectionStatus.tsx#OfflineNotice`);


/***/ }),

/***/ "(rsc)/./src/components/common/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/common/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useErrorHandler: () => (/* binding */ e2),
/* harmony export */   withErrorBoundary: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#withErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Luminar/Luminar-Dashboard/src/components/common/ErrorBoundary.tsx#useErrorHandler`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/zustand","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fahmedabdulla%2FLuminar%2FLuminar-Dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();