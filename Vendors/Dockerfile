# Multi-stage Dockerfile for Vendors Frontend

# Stage 1: Dependencies
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Stage 2: Builder
FROM node:20-alpine AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
ENV NEXT_TELEMETRY_DISABLED 1
RUN npm run build

# Stage 3: Runner (Production)
FROM node:20-alpine AS runner
RUN apk add --no-cache libc6-compat

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tanstack -u 1001

WORKDIR /app

# Copy necessary files
COPY --from=builder --chown=tanstack:nodejs /app/.vinxi ./dist
COPY --from=builder --chown=tanstack:nodejs /app/package*.json ./
COPY --from=builder --chown=tanstack:nodejs /app/public ./public

# Install only production dependencies
RUN npm ci --only=production

# Switch to non-root user
USER tanstack

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3002/health || exit 1

# Set environment
ENV NODE_ENV=production
ENV PORT=3002

# Start the application
CMD ["npm", "start"]