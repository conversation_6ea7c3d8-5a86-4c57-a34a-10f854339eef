/// <reference types="vinxi/types/client" />
import { hydrateRoot } from 'react-dom/client'
import { StartClient } from '@tanstack/react-start'
import { createRouter } from './router'
import { ThemeProvider, apiClient } from 'luminar-ui'

// Start MSW in development
async function enableMocking() {
  if (import.meta.env.PROD) {
    return
  }

  const { worker } = await import('./mocks/browser')
  
  // Start the worker
  return worker.start({
    onUnhandledRequest: 'bypass',
    serviceWorker: {
      url: '/mockServiceWorker.js',
    },
  }).then(() => {
    console.log('MSW started successfully')
    console.log('MSW is now intercepting requests')
  }).catch((error) => {
    console.error('MSW failed to start:', error)
  })
}

enableMocking().then(() => {
  // Configure API client
  apiClient.setBaseURL(import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api')
  
  const router = createRouter()
  hydrateRoot(
    document, 
    <ThemeProvider>
      <StartClient router={router} />
    </ThemeProvider>
  )
})
