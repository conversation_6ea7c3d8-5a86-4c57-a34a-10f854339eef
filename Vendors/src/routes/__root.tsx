import {
  Head<PERSON>onte<PERSON>,
  <PERSON>,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  createRootRoute,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import { Sidebar } from '~/components/ui/Sidebar'
import { NotificationCenter } from '~/components/ui/NotificationCenter'
import { ProgressIndicator } from '~/components/ui/ProgressIndicator'
import { 
  LayoutDashboard, 
  Building2, 
  FileText, 
  Star, 
  BarChart3,
  Users,
  Settings 
} from 'lucide-react'
import appCss from '~/styles/app.css?url'
import { seo } from '~/utils/seo'
import { useState } from 'react'
import { useAuthStore } from 'luminar-ui'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'L&D Vendor Management System',
        description: 'Manage vendors, proposals, and reviews for Learning & Development',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  const { queryClient } = Route.useRouteContext()
  
  return (
    <QueryClientProvider client={queryClient}>
      <RootDocument>
        <Outlet />
      </RootDocument>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const user = useAuthStore((state) => state.user)

  // Simulate loading state changes
  React.useEffect(() => {
    const handleStart = () => setIsLoading(true)
    const handleEnd = () => setIsLoading(false)

    // Listen to route changes
    window.addEventListener('routeChangeStart', handleStart)
    window.addEventListener('routeChangeComplete', handleEnd)

    return () => {
      window.removeEventListener('routeChangeStart', handleStart)
      window.removeEventListener('routeChangeComplete', handleEnd)
    }
  }, [])

  // Sidebar navigation sections
  const sidebarSections = [
    {
      id: 'main',
      items: [
        {
          id: 'dashboard',
          label: 'Dashboard',
          href: '/',
          icon: <LayoutDashboard className="h-4 w-4" />
        },
        {
          id: 'vendors',
          label: 'Vendors',
          href: '/vendors',
          icon: <Building2 className="h-4 w-4" />
        },
        {
          id: 'proposals',
          label: 'Proposals',
          href: '/proposals',
          icon: <FileText className="h-4 w-4" />
        },
        {
          id: 'reviews',
          label: 'Reviews',
          href: '/reviews',
          icon: <Star className="h-4 w-4" />
        },
        {
          id: 'analytics',
          label: 'Analytics',
          href: '/analytics',
          icon: <BarChart3 className="h-4 w-4" />
        }
      ]
    },
    {
      id: 'admin',
      title: 'Administration',
      items: [
        {
          id: 'users',
          label: 'Users',
          href: '/users',
          icon: <Users className="h-4 w-4" />
        },
        {
          id: 'settings',
          label: 'Settings',
          href: '/settings',
          icon: <Settings className="h-4 w-4" />
        }
      ]
    }
  ]

  const userProfile = user ? {
    name: user.name,
    email: user.email,
    role: user.roles?.[0]?.name || 'User'
  } : {
    name: 'Guest User',
    email: '<EMAIL>',
    role: 'Guest'
  }

  // Mock notifications
  const mockNotifications = [
    {
      id: '1',
      type: 'info' as const,
      title: 'New Vendor Application',
      message: 'TechCorp has submitted a new vendor application for review.',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      read: false,
    },
    {
      id: '2',
      type: 'success' as const,
      title: 'Proposal Approved',
      message: 'Proposal #PRO-001 from DataTech Solutions has been approved.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      read: false,
    },
    {
      id: '3',
      type: 'warning' as const,
      title: 'Review Reminder',
      message: 'You have 3 pending vendor reviews due this week.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      read: true,
    }
  ]

  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <div className="flex h-screen bg-gray-50">
          <Sidebar 
            sections={sidebarSections}
            userProfile={userProfile}
            defaultCollapsed={sidebarCollapsed}
            onCollapsedChange={setSidebarCollapsed}
            collapsible={true}
          />
          
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <header className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-800">
                  L&D Vendor Management System
                </h2>
                <NotificationCenter notifications={mockNotifications} />
              </div>
            </header>

            {/* Progress Indicator */}
            {isLoading && <ProgressIndicator />}

            {/* Main Content */}
            <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
              {children}
            </main>
          </div>
        </div>
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
