import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { But<PERSON> } from '~/components/ui/button'
import { Plus, Phone, Mail, Globe, Star, Eye, Edit, Trash2, FileUp } from 'lucide-react'
import { DataTable } from '~/components/ui/DataTable'
import { AdvancedSearch } from '~/components/ui/AdvancedSearch'
import { BulkActions } from '~/components/common/BulkActions'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { FileUpload } from '~/components/ui/FileUpload'
import { VendorEditForm } from '~/components/vendors/VendorEditForm'
import { useToast } from '~/hooks/use-toast'
import { useVendorStore } from 'luminar-ui'
import type { VendorCategory, VendorStatus, Vendor } from '~/types/vendor-management'
import type { ColumnDef } from '@tanstack/react-table'

export const Route = createFileRoute('/vendors/')({
  component: VendorsPage,
})

function VendorsPage() {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [showEditForm, setShowEditForm] = useState(false)
  const [editingVendor, setEditingVendor] = useState<Vendor | null>(null)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    category: 'all' as VendorCategory | 'all',
    status: 'all' as VendorStatus | 'all',
    rating: [0, 5] as [number, number],
  })
  
  const { toast } = useToast()
  const {
    vendors,
    vendorsLoading,
    vendorsError,
    fetchVendors,
    deleteVendor,
    updateVendor,
  } = useVendorStore()

  useEffect(() => {
    // Fetch vendors with filters
    fetchVendors({
      search: filters.search,
      category: filters.category === 'all' ? undefined : filters.category,
      status: filters.status === 'all' ? undefined : filters.status,
    })
  }, [filters, fetchVendors])

  const getStatusBadgeVariant = (status: VendorStatus) => {
    switch (status) {
      case 'active': return 'default'
      case 'inactive': return 'secondary'
      case 'pending': return 'outline'
    }
  }

  const getCategoryColor = (category: VendorCategory) => {
    const colors = {
      'Training': 'bg-blue-100 text-blue-800',
      'Assessment': 'bg-green-100 text-green-800',
      'Certification': 'bg-purple-100 text-purple-800',
      'Content': 'bg-yellow-100 text-yellow-800',
      'Consulting': 'bg-pink-100 text-pink-800',
    }
    return colors[category]
  }

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case 'delete':
          for (const id of selectedRows) {
            await deleteVendor(id)
          }
          toast({
            title: 'Success',
            description: `Deleted ${selectedRows.size} vendors`,
          })
          setSelectedRows(new Set())
          break
        case 'export':
          // Export functionality to be implemented
          toast({
            title: 'Export Started',
            description: `Exporting ${selectedRows.size} vendors...`,
          })
          break
        case 'activate':
          for (const id of selectedRows) {
            const vendor = vendors?.find(v => v.id === id)
            if (vendor) {
              await updateVendor(id, { status: 'active' })
            }
          }
          toast({
            title: 'Success',
            description: `Activated ${selectedRows.size} vendors`,
          })
          setSelectedRows(new Set())
          break
        case 'deactivate':
          for (const id of selectedRows) {
            const vendor = vendors?.find(v => v.id === id)
            if (vendor) {
              await updateVendor(id, { status: 'inactive' })
            }
          }
          toast({
            title: 'Success',
            description: `Deactivated ${selectedRows.size} vendors`,
          })
          setSelectedRows(new Set())
          break
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to complete bulk action',
        variant: 'destructive',
      })
    }
  }

  const handleFileUpload = (files: File[]) => {
    // Implement file upload logic
    toast({
      title: 'Files Uploaded',
      description: `${files.length} file(s) uploaded successfully`,
    })
    setShowUploadDialog(false)
  }

  const columns: ColumnDef<Vendor>[] = [
    {
      accessorKey: 'companyName',
      header: 'Company',
      cell: ({ row }) => {
        const vendor = row.original
        return (
          <div>
            <p className="font-medium">{vendor.companyName}</p>
            {vendor.website && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Globe className="h-3 w-3" />
                <a 
                  href={vendor.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  {new URL(vendor.website).hostname}
                </a>
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'contactPerson',
      header: 'Contact',
      cell: ({ row }) => {
        const vendor = row.original
        return (
          <div className="space-y-1">
            <p className="font-medium">{vendor.contactPerson}</p>
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Mail className="h-3 w-3" />
              <a href={`mailto:${vendor.email}`} className="hover:underline" onClick={(e) => e.stopPropagation()}>
                {vendor.email}
              </a>
            </div>
            {vendor.phoneNumbers[0] && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Phone className="h-3 w-3" />
                {vendor.phoneNumbers[0].number}
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${getCategoryColor(row.original.category)}`}>
          {row.original.category}
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant={getStatusBadgeVariant(row.original.status)}>
          {row.original.status}
        </Badge>
      ),
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{row.original.rating}</span>
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Link to="/vendors/$vendorId" params={{ vendorId: row.original.id }}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Button 
            variant="outline" 
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              setEditingVendor(row.original)
              setShowEditForm(true)
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={async (e) => {
              e.stopPropagation()
              if (confirm('Are you sure you want to delete this vendor?')) {
                try {
                  await deleteVendor(row.original.id)
                  toast({
                    title: 'Success',
                    description: 'Vendor deleted successfully',
                  })
                } catch (error) {
                  toast({
                    title: 'Error',
                    description: 'Failed to delete vendor',
                    variant: 'destructive',
                  })
                }
              }
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  if (vendorsError) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Error loading vendors: {vendorsError}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Filter vendors based on rating range - ensure vendors is an array
  const vendorsArray = Array.isArray(vendors) ? vendors : []
  const filteredVendors = vendorsArray.filter(v => 
    v.rating >= filters.rating[0] && v.rating <= filters.rating[1]
  )

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/' },
          { label: 'Vendors' },
        ]}
      />
      
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Vendors</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowUploadDialog(true)}>
            <FileUp className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Link to="/vendors/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Vendor
            </Button>
          </Link>
        </div>
      </div>

      <AdvancedSearch
        onSearch={(searchFilters) => {
          setFilters({
            search: searchFilters.query || '',
            category: searchFilters.filters?.category || 'all',
            status: searchFilters.filters?.status || 'all',
            rating: searchFilters.filters?.rating || [0, 5],
          })
        }}
        searchPlaceholder="Search vendors by name, email, or contact person..."
        filters={[
          {
            key: 'category',
            label: 'Category',
            type: 'select',
            options: [
              { value: 'all', label: 'All Categories' },
              { value: 'Training', label: 'Training' },
              { value: 'Assessment', label: 'Assessment' },
              { value: 'Certification', label: 'Certification' },
              { value: 'Content', label: 'Content' },
              { value: 'Consulting', label: 'Consulting' },
            ],
          },
          {
            key: 'status',
            label: 'Status',
            type: 'select',
            options: [
              { value: 'all', label: 'All Statuses' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'pending', label: 'Pending' },
            ],
          },
          {
            key: 'rating',
            label: 'Rating',
            type: 'range',
            min: 0,
            max: 5,
            step: 0.5,
          },
        ]}
        showSaveSearch
        savedSearches={[
          { id: '1', name: 'High-rated Active Vendors', filters: { status: 'active', rating: [4, 5] } },
          { id: '2', name: 'Training Providers', filters: { category: 'Training' } },
        ]}
      />

      {selectedRows.size > 0 && (
        <BulkActions
          selectedCount={selectedRows.size}
          actions={[
            { label: 'Delete', value: 'delete', variant: 'destructive' },
            { label: 'Export', value: 'export' },
            { label: 'Activate', value: 'activate' },
            { label: 'Deactivate', value: 'deactivate' },
          ]}
          onAction={handleBulkAction}
        />
      )}

      <Card>
        <CardContent className="p-0">
          {vendorsLoading ? (
            <LoadingSkeleton type="table" rows={5} />
          ) : (
            <DataTable
              columns={columns}
              data={filteredVendors}
              searchable
              sortable
              selectable
              selectedRows={selectedRows}
              onSelectionChange={setSelectedRows}
              emptyMessage="No vendors found"
              onRowClick={(row) => {
                window.location.href = `/vendors/${row.id}`
              }}
            />
          )}
        </CardContent>
      </Card>

      {showEditForm && editingVendor && (
        <VendorEditForm
          vendor={editingVendor}
          open={showEditForm}
          onOpenChange={setShowEditForm}
          onSuccess={() => {
            setShowEditForm(false)
            setEditingVendor(null)
            toast({
              title: 'Success',
              description: 'Vendor updated successfully',
            })
          }}
        />
      )}

      <FileUpload
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
        onUpload={handleFileUpload}
        accept=".csv,.xlsx"
        multiple
        maxSize={10 * 1024 * 1024} // 10MB
        title="Import Vendors"
        description="Upload CSV or Excel files to import vendors"
      />
    </div>
  )
}