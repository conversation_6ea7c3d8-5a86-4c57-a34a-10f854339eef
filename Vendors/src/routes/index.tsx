import { createFileRoute } from "@tanstack/react-router";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Building2, FileText, Star, TrendingUp, Users, DollarSign } from "lucide-react";
import { useVendorStore, useAuthStore } from "luminar-ui";
import { useEffect } from "react";

export const Route = createFileRoute("/")({
  component: Dashboard,
});

function Dashboard() {
  const user = useAuthStore((state) => state.user);
  const {
    vendors,
    proposals, 
    reviews,
    dashboardStats,
    topVendors,
    fetchVendors,
    fetchProposals,
    fetchReviews,
    fetchDashboardStats,
    fetchTopPerformingVendors,
  } = useVendorStore();

  useEffect(() => {
    // Fetch dashboard data
    const loadDashboardData = async () => {
      await Promise.all([
        fetchVendors({ limit: 10 }),
        fetchProposals({ limit: 10 }),
        fetchReviews(),
        fetchDashboardStats(),
        fetchTopPerformingVendors(5),
      ]);
    };

    loadDashboardData();
  }, [fetchVendors, fetchProposals, fetchReviews, fetchDashboardStats, fetchTopPerformingVendors]);

  // Calculate stats from store data
  const vendorStats = {
    total: dashboardStats?.totalVendors || vendors.length,
    active: dashboardStats?.activeVendors || vendors.filter(v => v.status === 'active').length,
    averageRating: dashboardStats?.averageRating || 0,
    byCategory: dashboardStats?.vendorsByCategory || {}
  };

  const proposalStats = {
    total: dashboardStats?.totalProposals || proposals.length,
    approvalRate: dashboardStats?.proposalApprovalRate || 0,
    totalSpent: dashboardStats?.totalSpent || 0,
    averageProposalValue: dashboardStats?.averageProposalValue || 0,
    byStatus: dashboardStats?.proposalsByStatus || {}
  };

  const reviewStats = {
    total: dashboardStats?.totalReviews || reviews.length,
    recentReviews: reviews.slice(0, 5)
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">L&D Vendor Management Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Vendors</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{vendorStats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {vendorStats?.active || 0} active vendors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Proposals</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{proposalStats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {proposalStats?.approvalRate || 0}% approval rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{vendorStats?.averageRating || 0}</div>
            <p className="text-xs text-muted-foreground">
              From {reviewStats?.total || 0} reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(proposalStats?.totalSpent || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: ${(proposalStats?.averageProposalValue || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Vendor Categories</CardTitle>
          </CardHeader>
          <CardContent>
            {vendorStats?.byCategory && (
              <div className="space-y-4">
                {Object.entries(vendorStats.byCategory).map(([category, count]) => (
                  <div key={category} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category}</span>
                    <span className="text-sm text-muted-foreground">{count as number} vendors</span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Proposal Status</CardTitle>
          </CardHeader>
          <CardContent>
            {proposalStats?.byStatus && (
              <div className="space-y-4">
                {Object.entries(proposalStats.byStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{status}</span>
                    <span className="text-sm text-muted-foreground">{count as number} proposals</span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          {reviewStats?.recentReviews && reviewStats.recentReviews.length > 0 ? (
            <div className="space-y-4">
              {reviewStats.recentReviews.map((review: any) => (
                <div key={review.id} className="border-b pb-4 last:border-0">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{review.vendor?.companyName}</span>
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`h-4 w-4 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">{review.comment}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    By {review.reviewerName} - {review.reviewerRole}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No reviews yet</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
