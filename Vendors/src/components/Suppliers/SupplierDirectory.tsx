import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useVendorStore } from '@/lib/stores/vendor.store';
import { useAuthStore } from '@/lib/stores/auth.store';
import { 
  Building2, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Star,
  Search, 
  Filter,
  Plus,
  Edit,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Award,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface Vendor {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  description: string;
  website?: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  primaryContact: {
    name: string;
    title: string;
    email: string;
    phone: string;
  };
  capabilities: string[];
  certifications: string[];
  qualificationStatus: 'pending' | 'qualified' | 'disqualified' | 'under_review';
  riskLevel: 'low' | 'medium' | 'high';
  performanceScore?: number;
  totalContracts: number;
  activeContracts: number;
  totalContractValue: number;
  lastEvaluationDate?: string;
  lastEvaluationScore?: number;
  onboardingDate: string;
  status: 'active' | 'inactive' | 'suspended';
  tags: string[];
  diversity: {
    isMinorityOwned: boolean;
    isWomanOwned: boolean;
    isVeteranOwned: boolean;
    isSmallBusiness: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

interface SupplierFilters {
  search: string;
  category: string;
  qualificationStatus: string;
  riskLevel: string;
  status: string;
  diversity: string;
  sortBy: string;
}

export const SupplierDirectory: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    vendors, 
    fetchVendors, 
    createVendor,
    updateVendor 
  } = useVendorStore();
  
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const [filters, setFilters] = useState<SupplierFilters>({
    search: '',
    category: '',
    qualificationStatus: '',
    riskLevel: '',
    status: '',
    diversity: '',
    sortBy: 'name'
  });
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);

  useEffect(() => {
    loadVendors();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [vendors, filters]);

  const loadVendors = async () => {
    try {
      setIsLoading(true);
      await fetchVendors();
    } catch (error) {
      console.error('Error loading vendors:', error);
      toast.error('Failed to load vendors');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...vendors];

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(vendor => 
        vendor.name.toLowerCase().includes(searchTerm) ||
        vendor.category.toLowerCase().includes(searchTerm) ||
        vendor.capabilities.some(cap => cap.toLowerCase().includes(searchTerm)) ||
        vendor.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(vendor => vendor.category === filters.category);
    }

    // Qualification status filter
    if (filters.qualificationStatus) {
      filtered = filtered.filter(vendor => vendor.qualificationStatus === filters.qualificationStatus);
    }

    // Risk level filter
    if (filters.riskLevel) {
      filtered = filtered.filter(vendor => vendor.riskLevel === filters.riskLevel);
    }

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(vendor => vendor.status === filters.status);
    }

    // Diversity filter
    if (filters.diversity) {
      filtered = filtered.filter(vendor => {
        switch (filters.diversity) {
          case 'minority': return vendor.diversity.isMinorityOwned;
          case 'woman': return vendor.diversity.isWomanOwned;
          case 'veteran': return vendor.diversity.isVeteranOwned;
          case 'small': return vendor.diversity.isSmallBusiness;
          default: return true;
        }
      });
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'performance':
          return (b.performanceScore || 0) - (a.performanceScore || 0);
        case 'contracts':
          return b.totalContracts - a.totalContracts;
        case 'value':
          return b.totalContractValue - a.totalContractValue;
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

    setFilteredVendors(filtered);
  };

  const getCategories = () => {
    return Array.from(new Set(vendors.map(vendor => vendor.category))).sort();
  };

  const getQualificationBadgeColor = (status: string) => {
    switch (status) {
      case 'qualified': return 'default';
      case 'pending': return 'secondary';
      case 'under_review': return 'outline';
      case 'disqualified': return 'destructive';
      default: return 'secondary';
    }
  };

  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'default';
      case 'medium': return 'secondary';
      case 'high': return 'destructive';
      default: return 'secondary';
    }
  };

  const VendorCard: React.FC<{ vendor: Vendor }> = ({ vendor }) => {
    const diversityBadges = [];
    if (vendor.diversity.isMinorityOwned) diversityBadges.push('Minority Owned');
    if (vendor.diversity.isWomanOwned) diversityBadges.push('Woman Owned');
    if (vendor.diversity.isVeteranOwned) diversityBadges.push('Veteran Owned');
    if (vendor.diversity.isSmallBusiness) diversityBadges.push('Small Business');

    return (
      <Card className="h-full hover:shadow-lg transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-gray-600" />
              <Badge variant={getQualificationBadgeColor(vendor.qualificationStatus)}>
                {vendor.qualificationStatus.replace('_', ' ')}
              </Badge>
            </div>
            <Badge variant={getRiskBadgeColor(vendor.riskLevel)}>
              {vendor.riskLevel} risk
            </Badge>
          </div>
          <CardTitle className="text-lg">{vendor.name}</CardTitle>
          <p className="text-sm text-gray-600">{vendor.category}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-600 line-clamp-2">{vendor.description}</p>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span>{vendor.address.city}, {vendor.address.state}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4 text-gray-400" />
              <span>{vendor.activeContracts} active</span>
            </div>
            <div className="flex items-center gap-1">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{vendor.phone}</span>
            </div>
            <div className="flex items-center gap-1">
              <Award className="h-4 w-4 text-gray-400" />
              <span>{vendor.performanceScore || 'N/A'}%</span>
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium">Capabilities:</p>
            <div className="flex flex-wrap gap-1">
              {vendor.capabilities.slice(0, 3).map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability}
                </Badge>
              ))}
              {vendor.capabilities.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{vendor.capabilities.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          {diversityBadges.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Diversity:</p>
              <div className="flex flex-wrap gap-1">
                {diversityBadges.slice(0, 2).map((badge, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {badge}
                  </Badge>
                ))}
                {diversityBadges.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{diversityBadges.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => setSelectedVendor(vendor)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </DialogTrigger>
            </Dialog>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                // Handle edit action
                toast.info('Edit functionality coming soon');
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const VendorDetailsDialog: React.FC = () => {
    if (!selectedVendor) return null;

    return (
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{selectedVendor.name}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Status and Risk Indicators */}
          <div className="flex flex-wrap gap-2">
            <Badge variant={getQualificationBadgeColor(selectedVendor.qualificationStatus)}>
              {selectedVendor.qualificationStatus.replace('_', ' ')}
            </Badge>
            <Badge variant={getRiskBadgeColor(selectedVendor.riskLevel)}>
              {selectedVendor.riskLevel} risk
            </Badge>
            <Badge variant={selectedVendor.status === 'active' ? 'default' : 'secondary'}>
              {selectedVendor.status}
            </Badge>
            {selectedVendor.performanceScore && (
              <Badge variant="outline">
                Performance: {selectedVendor.performanceScore}%
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Information */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Description</h4>
                    <p className="text-gray-600">{selectedVendor.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Contact Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <a href={`mailto:${selectedVendor.email}`} className="text-blue-600 hover:underline">
                            {selectedVendor.email}
                          </a>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          <span>{selectedVendor.phone}</span>
                        </div>
                        {selectedVendor.website && (
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4" />
                            <a href={selectedVendor.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              Website
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Address</h4>
                      <div className="text-sm text-gray-600">
                        <div>{selectedVendor.address.street}</div>
                        <div>{selectedVendor.address.city}, {selectedVendor.address.state} {selectedVendor.address.postalCode}</div>
                        <div>{selectedVendor.address.country}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Primary Contact</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">{selectedVendor.primaryContact.name}</p>
                      <p className="text-sm text-gray-600">{selectedVendor.primaryContact.title}</p>
                    </div>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        <a href={`mailto:${selectedVendor.primaryContact.email}`} className="text-blue-600 hover:underline">
                          {selectedVendor.primaryContact.email}
                        </a>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        <span>{selectedVendor.primaryContact.phone}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Capabilities & Certifications</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Capabilities</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedVendor.capabilities.map((capability, index) => (
                        <Badge key={index} variant="outline">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Certifications</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedVendor.certifications.map((cert, index) => (
                        <Badge key={index} variant="secondary">
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Key Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Contracts</span>
                      <span className="font-medium">{selectedVendor.totalContracts}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Active Contracts</span>
                      <span className="font-medium">{selectedVendor.activeContracts}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Value</span>
                      <span className="font-medium">${selectedVendor.totalContractValue.toLocaleString()}</span>
                    </div>
                    {selectedVendor.performanceScore && (
                      <div className="flex justify-between">
                        <span className="text-sm">Performance Score</span>
                        <span className="font-medium">{selectedVendor.performanceScore}%</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Diversity Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {selectedVendor.diversity.isMinorityOwned && (
                      <Badge variant="secondary" className="w-full justify-center">
                        Minority Owned Business
                      </Badge>
                    )}
                    {selectedVendor.diversity.isWomanOwned && (
                      <Badge variant="secondary" className="w-full justify-center">
                        Woman Owned Business
                      </Badge>
                    )}
                    {selectedVendor.diversity.isVeteranOwned && (
                      <Badge variant="secondary" className="w-full justify-center">
                        Veteran Owned Business
                      </Badge>
                    )}
                    {selectedVendor.diversity.isSmallBusiness && (
                      <Badge variant="secondary" className="w-full justify-center">
                        Small Business
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Timeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4" />
                    <div>
                      <div className="font-medium">Onboarded</div>
                      <div className="text-gray-600">
                        {format(new Date(selectedVendor.onboardingDate), 'MMM d, yyyy')}
                      </div>
                    </div>
                  </div>
                  {selectedVendor.lastEvaluationDate && (
                    <div className="flex items-center gap-2 text-sm">
                      <Star className="h-4 w-4" />
                      <div>
                        <div className="font-medium">Last Evaluation</div>
                        <div className="text-gray-600">
                          {format(new Date(selectedVendor.lastEvaluationDate), 'MMM d, yyyy')}
                          {selectedVendor.lastEvaluationScore && (
                            <span className="ml-2 font-medium">
                              ({selectedVendor.lastEvaluationScore}%)
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="space-y-2">
                <Button className="w-full">
                  Start Evaluation
                </Button>
                <Button variant="outline" className="w-full">
                  Create Contract
                </Button>
                <Button variant="outline" className="w-full">
                  View History
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Supplier Directory</h1>
          <p className="text-gray-600">Manage your vendor relationships</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadVendors} variant="outline" disabled={isLoading}>
            Refresh
          </Button>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Vendor
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search vendors..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.category}
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {getCategories().map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.qualificationStatus}
              onValueChange={(value) => setFilters(prev => ({ ...prev, qualificationStatus: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Qualification" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="qualified">Qualified</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="disqualified">Disqualified</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.riskLevel}
              onValueChange={(value) => setFilters(prev => ({ ...prev, riskLevel: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Risk Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Risk Levels</SelectItem>
                <SelectItem value="low">Low Risk</SelectItem>
                <SelectItem value="medium">Medium Risk</SelectItem>
                <SelectItem value="high">High Risk</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.diversity}
              onValueChange={(value) => setFilters(prev => ({ ...prev, diversity: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Diversity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Vendors</SelectItem>
                <SelectItem value="minority">Minority Owned</SelectItem>
                <SelectItem value="woman">Woman Owned</SelectItem>
                <SelectItem value="veteran">Veteran Owned</SelectItem>
                <SelectItem value="small">Small Business</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortBy}
              onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="performance">Performance</SelectItem>
                <SelectItem value="contracts">Contracts</SelectItem>
                <SelectItem value="value">Total Value</SelectItem>
                <SelectItem value="newest">Newest</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredVendors.length} of {vendors.length} vendors
        </p>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm text-gray-600">
            {Object.values(filters).filter(v => v !== '').length} filters applied
          </span>
        </div>
      </div>

      {/* Vendor Grid */}
      {filteredVendors.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <Building2 className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No vendors found</h3>
            <p className="text-gray-600 text-center">
              Try adjusting your filters or search terms to find vendors.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredVendors.map((vendor) => (
            <VendorCard key={vendor.id} vendor={vendor} />
          ))}
        </div>
      )}

      {/* Vendor Details Dialog */}
      <Dialog open={!!selectedVendor} onOpenChange={(open) => !open && setSelectedVendor(null)}>
        <VendorDetailsDialog />
      </Dialog>

      {/* Add Vendor Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Vendor</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-gray-600">Add vendor form will be implemented here.</p>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                toast.success('Vendor creation form coming soon!');
                setShowAddDialog(false);
              }}>
                Create Vendor
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};