# Production Environment Configuration

# Database
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=CHANGE_ME_STRONG_PASSWORD
DATABASE_NAME=command_center

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD

# JWT
JWT_SECRET=CHANGE_ME_RANDOM_SECRET_KEY
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d

# Session
SESSION_SECRET=CHANGE_ME_SESSION_SECRET

# CORS
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# API URLs
VITE_API_URL=https://api.yourdomain.com

# File Upload
MAX_FILE_SIZE=104857600
ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Email (SendGrid)
SENDGRID_API_KEY=
FROM_EMAIL=<EMAIL>

# Monitoring
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_LOGGING=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# SSL/TLS
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem