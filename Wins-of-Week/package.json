{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.488.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "luminar-ui": "file:../shared-ui", "zustand": "^5.0.0", "axios": "^1.6.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.0", "xlsx": "^0.18.5", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}