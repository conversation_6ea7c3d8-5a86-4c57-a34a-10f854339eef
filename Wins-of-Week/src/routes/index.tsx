import { createFileRoute } from "@tanstack/react-router";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Award, TrendingUp, Users, BookOpen } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { useWinsStore, useAuthStore } from "luminar-ui";
import { useEffect } from "react";

export const Route = createFileRoute("/")({
  component: Home,
});

function Home() {
  const user = useAuthStore((state) => state.user);
  const {
    dashboardStats,
    topPerformers,
    achievementLeaderboard,
    fetchDashboardStats,
    fetchTopPerformers,
    fetchAchievementLeaderboard,
  } = useWinsStore();

  useEffect(() => {
    // Fetch dashboard data
    const loadDashboardData = async () => {
      await Promise.all([
        fetchDashboardStats(user?.id, user?.departmentId),
        fetchTopPerformers(3),
        fetchAchievementLeaderboard(),
      ]);
    };

    loadDashboardData();
  }, [user, fetchDashboardStats, fetchTopPerformers, fetchAchievementLeaderboard]);
  return (
    <div className="container mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">L&D Weekly Wins</h1>
        <p className="text-xl text-muted-foreground">
          Track, celebrate, and share your team's achievements and learning initiatives
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Achievements</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalAchievements || 0}</div>
            <p className="text-xs text-muted-foreground">This week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(dashboardStats?.totalCostSavings || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Year to date</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalSubmissions || 0}</div>
            <p className="text-xs text-muted-foreground">Participating</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Training Ideas</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalTrainingIdeas || 0}</div>
            <p className="text-xs text-muted-foreground">Submitted this month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Get started with your weekly submission</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link to="/submissions/new">
              <Button className="w-full" size="lg">
                Create New Submission
              </Button>
            </Link>
            <Link to="/submissions">
              <Button variant="outline" className="w-full" size="lg">
                View Past Submissions
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Recognitions</CardTitle>
            <CardDescription>Celebrating our team's achievements</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {achievementLeaderboard.length > 0 ? (
                achievementLeaderboard.slice(0, 3).map((achievement: any, index: number) => (
                  <div key={achievement.id || index} className="flex items-start space-x-4">
                    <Award className={`h-5 w-5 mt-0.5 ${
                      index === 0 ? 'text-yellow-500' : 
                      index === 1 ? 'text-blue-500' : 
                      'text-green-500'
                    }`} />
                    <div>
                      <p className="font-medium">{achievement.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {achievement.userName} - {achievement.date ? new Date(achievement.date).toLocaleDateString() : 'Recently'}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">No recent recognitions</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}