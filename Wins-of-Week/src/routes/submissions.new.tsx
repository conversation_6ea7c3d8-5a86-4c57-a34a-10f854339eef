import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Button } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Textarea } from "~/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { Loader2 } from "lucide-react"
import { AchievementForm } from "~/components/submission/AchievementForm"
import { ImageUpload } from "~/components/submission/ImageUpload"
import { RecognitionForm } from "~/components/submission/RecognitionForm"
import { CostInitiativeForm } from "~/components/submission/CostInitiativeForm"
import { TrainingIdeaForm } from "~/components/submission/TrainingIdeaForm"
import { ProgressTracker } from "~/components/submission/ProgressTracker"
import { useWinsStore, useAuthStore } from "luminar-ui"
import type { Achievement, Recognition, CostInitiative, TrainingIdea, ProgressUpdate } from "~/types/submission"

export const Route = createFileRoute("/submissions/new")({
  component: NewSubmission,
})

const submissionSchema = z.object({
  weeklyHighlight: z.string().min(10, "Please provide a weekly highlight"),
  challenges: z.string().optional(),
  nextWeekFocus: z.string().optional(),
})

function NewSubmission() {
  const navigate = useNavigate()
  const user = useAuthStore((state) => state.user)
  const { createSubmission, saveDraft } = useWinsStore()
  
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [recognitions, setRecognitions] = useState<Recognition[]>([])
  const [costInitiatives, setCostInitiatives] = useState<CostInitiative[]>([])
  const [trainingIdeas, setTrainingIdeas] = useState<TrainingIdea[]>([])
  const [progressUpdates, setProgressUpdates] = useState<ProgressUpdate[]>([])
  const [profilePicture, setProfilePicture] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(submissionSchema),
  })

  const onSubmit = async (data: z.infer<typeof submissionSchema>) => {
    if (!user) return
    
    setIsSubmitting(true)
    try {
      const submission = await createSubmission({
        userId: user.id,
        weekStartDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last Monday
        weekEndDate: new Date().toISOString(),
        weeklyHighlight: data.weeklyHighlight,
        challenges: data.challenges,
        nextWeekFocus: data.nextWeekFocus,
        achievements,
        recognitions,
        costInitiatives,
        trainingIdeas,
        progressUpdates,
      })
      
      if (submission) {
        navigate({ to: '/submissions' })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSaveDraft = async () => {
    const values = getValues()
    await saveDraft({
      weeklyHighlight: values.weeklyHighlight || '',
      challenges: values.challenges || '',
      nextWeekFocus: values.nextWeekFocus || '',
      achievements,
      recognitions,
      costInitiatives,
      trainingIdeas,
      progressUpdates,
    })
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Weekly Submission</h1>
        <p className="text-muted-foreground">
          Share your achievements, recognitions, and initiatives for this week
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Staff Information</CardTitle>
            <CardDescription>Your profile details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input id="name" value={user?.name || ''} readOnly />
              </div>
              <div>
                <Label htmlFor="department">Department</Label>
                <Input id="department" value={user?.department?.name || 'Not assigned'} readOnly />
              </div>
            </div>
            <ImageUpload
              label="Profile Picture"
              onImageUpload={setProfilePicture}
              onImageRemove={() => setProfilePicture(null)}
            />
          </CardContent>
        </Card>

        <Tabs defaultValue="achievements" className="space-y-4">
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
            <TabsTrigger value="recognitions">Recognitions</TabsTrigger>
            <TabsTrigger value="costs">Cost Initiatives</TabsTrigger>
            <TabsTrigger value="training">Training Ideas</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="achievements">
            <Card>
              <CardHeader>
                <CardTitle>Weekly Achievements</CardTitle>
                <CardDescription>
                  List your accomplishments and wins for this week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AchievementForm achievements={achievements} onChange={setAchievements} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recognitions">
            <Card>
              <CardHeader>
                <CardTitle>Recognitions & Awards</CardTitle>
                <CardDescription>
                  Any recognitions, certifications, or awards received
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RecognitionForm recognitions={recognitions} onChange={setRecognitions} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="costs">
            <Card>
              <CardHeader>
                <CardTitle>Cost Saving Initiatives</CardTitle>
                <CardDescription>
                  Ideas or implementations that save costs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CostInitiativeForm initiatives={costInitiatives} onChange={setCostInitiatives} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="training">
            <Card>
              <CardHeader>
                <CardTitle>Training Ideas</CardTitle>
                <CardDescription>
                  Suggest new training programs or improvements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TrainingIdeaForm ideas={trainingIdeas} onChange={setTrainingIdeas} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="progress">
            <Card>
              <CardHeader>
                <CardTitle>Progress Updates</CardTitle>
                <CardDescription>
                  Update on your goals and ongoing projects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProgressTracker updates={progressUpdates} onChange={setProgressUpdates} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>Weekly Summary</CardTitle>
            <CardDescription>Summarize your week</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="highlight">Weekly Highlight</Label>
              <Textarea
                id="highlight"
                {...register("weeklyHighlight")}
                placeholder="What was the biggest win or highlight of your week?"
              />
              {errors.weeklyHighlight && (
                <p className="text-sm text-red-500 mt-1">{errors.weeklyHighlight.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="challenges">Challenges Faced (Optional)</Label>
              <Textarea
                id="challenges"
                {...register("challenges")}
                placeholder="Any challenges you encountered this week?"
              />
            </div>
            <div>
              <Label htmlFor="nextWeek">Next Week Focus (Optional)</Label>
              <Textarea
                id="nextWeek"
                {...register("nextWeekFocus")}
                placeholder="What will you focus on next week?"
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-4">
          <Button type="submit" size="lg" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Weekly Report'
            )}
          </Button>
          <Button type="button" variant="outline" size="lg" onClick={handleSaveDraft}>
            Save as Draft
          </Button>
        </div>
      </form>
    </div>
  )
}