import { createFileRoute } from "@tanstack/react-router"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import { Link } from "@tanstack/react-router"
import { Award, DollarSign, Target, BookOpen, Calendar, Eye, Loader2 } from "lucide-react"
import { format } from "date-fns"
import { useWinsStore, useAuthStore } from "luminar-ui"
import { useEffect, useState } from "react"

export const Route = createFileRoute("/submissions/")({
  component: Submissions,
})

function Submissions() {
  const user = useAuthStore((state) => state.user)
  const { submissions, fetchSubmissions } = useWinsStore()
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'mine' | 'department'>('all')

  useEffect(() => {
    const loadSubmissions = async () => {
      setIsLoading(true)
      try {
        const params: any = { status: 'all', limit: 100 }
        
        if (filter === 'mine' && user) {
          params.userId = user.id
        } else if (filter === 'department' && user?.departmentId) {
          params.departmentId = user.departmentId
        }
        
        await fetchSubmissions(params)
      } finally {
        setIsLoading(false)
      }
    }

    loadSubmissions()
  }, [filter, user, fetchSubmissions])
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft": return "bg-gray-100 text-gray-800"
      case "submitted": return "bg-blue-100 text-blue-800"
      case "reviewed": return "bg-green-100 text-green-800"
      case "approved": return "bg-purple-100 text-purple-800"
      case "rejected": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Weekly Submissions</h1>
          <p className="text-muted-foreground">
            View and manage all staff weekly submissions
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="all">All Submissions</option>
            <option value="mine">My Submissions</option>
            <option value="department">My Department</option>
          </select>
          <Link to="/submissions/new">
            <Button>Create New Submission</Button>
          </Link>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : submissions.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-muted-foreground mb-4">No submissions found</p>
            <Link to="/submissions/new">
              <Button>Create Your First Submission</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {submissions.map((submission) => (
            <Card key={submission.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{submission.user?.name || 'Unknown User'}</CardTitle>
                    <CardDescription>{submission.user?.department?.name || 'No Department'}</CardDescription>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(submission.status)}`}>
                    {submission.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-5 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Achievements</p>
                      <p className="font-medium">{submission.achievements?.length || 0}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-yellow-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Recognitions</p>
                      <p className="font-medium">{submission.recognitions?.length || 0}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Cost Savings</p>
                      <p className="font-medium">
                        ${(submission.costInitiatives?.reduce((sum, init) => sum + (init.estimatedSavings || 0), 0) || 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="w-4 h-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Training Ideas</p>
                      <p className="font-medium">{submission.trainingIdeas?.length || 0}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Submitted</p>
                      <p className="font-medium">
                        {format(new Date(submission.createdAt), "MMM d")}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    Week of {format(new Date(submission.weekStartDate), "MMM d, yyyy")}
                  </p>
                  <Link to={`/submissions/${submission.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}