import { createFileRoute } from "@tanstack/react-router"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Award, TrendingUp, Users, Target, Calendar, Star, Loader2 } from "lucide-react"
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { useWinsStore, useAuthStore } from "luminar-ui"
import { useEffect, useState } from "react"

export const Route = createFileRoute("/dashboard")({
  component: Dashboard,
})

function Dashboard() {
  const user = useAuthStore((state) => state.user)
  const {
    dashboardStats,
    weeklyAnalytics,
    analytics,
    topPerformers,
    fetchDashboardStats,
    fetchWeeklyAnalytics,
    fetchAnalytics,
    fetchTopPerformers,
  } = useWinsStore()
  
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true)
      try {
        const endDate = new Date()
        const startDate = new Date()
        startDate.setMonth(startDate.getMonth() - 6) // Last 6 months
        
        await Promise.all([
          fetchDashboardStats(user?.id, user?.departmentId),
          fetchWeeklyAnalytics(6), // Last 6 weeks
          fetchAnalytics(startDate.toISOString(), endDate.toISOString()),
          fetchTopPerformers(5),
        ])
      } finally {
        setIsLoading(false)
      }
    }

    loadDashboardData()
  }, [user, fetchDashboardStats, fetchWeeklyAnalytics, fetchAnalytics, fetchTopPerformers])

  // Transform data for charts
  const weeklySubmissionsData = weeklyAnalytics?.map((week, index) => ({
    week: `Week ${index + 1}`,
    submissions: week.submissionCount,
  })) || []

  const costSavingsData = analytics?.monthlyBreakdown?.map(month => ({
    month: new Date(month.month).toLocaleString('default', { month: 'short' }),
    savings: month.costSavings,
  })) || []

  const trainingCategoriesData = [
    { name: "Technical", value: dashboardStats?.trainingIdeasByCategory?.technical || 0, color: "#3b82f6" },
    { name: "Soft Skills", value: dashboardStats?.trainingIdeasByCategory?.softSkills || 0, color: "#10b981" },
    { name: "Leadership", value: dashboardStats?.trainingIdeasByCategory?.leadership || 0, color: "#f59e0b" },
    { name: "Compliance", value: dashboardStats?.trainingIdeasByCategory?.compliance || 0, color: "#ef4444" },
    { name: "Other", value: dashboardStats?.trainingIdeasByCategory?.other || 0, color: "#6b7280" },
  ].filter(cat => cat.value > 0)

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Performance Dashboard</h1>
        <p className="text-muted-foreground">
          Track team performance and key metrics across L&D initiatives
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Achievements</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalAchievements || 0}</div>
            <p className="text-xs text-muted-foreground">
              This period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">YTD Cost Savings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(dashboardStats?.totalCostSavings || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Year to date
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalSubmissions || 0}</div>
            <p className="text-xs text-muted-foreground">
              Total submissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Training Ideas</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalTrainingIdeas || 0}</div>
            <p className="text-xs text-muted-foreground">
              Submitted
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Submissions Trend</CardTitle>
            <CardDescription>Number of submissions over the past 6 weeks</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={weeklySubmissionsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="submissions" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  dot={{ fill: "#3b82f6" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cost Savings by Month</CardTitle>
            <CardDescription>Total cost savings achieved each month</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={costSavingsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => `$${value.toLocaleString()}`} />
                <Bar dataKey="savings" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Training Ideas by Category</CardTitle>
            <CardDescription>Distribution of submitted training ideas</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={trainingCategoriesData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {trainingCategoriesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Top Performers</CardTitle>
            <CardDescription>Staff with the most achievements this quarter</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.length > 0 ? (
                topPerformers.map((performer, index) => (
                  <div key={performer.userId} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{performer.userName}</p>
                        <p className="text-sm text-muted-foreground">{performer.departmentName || 'No Department'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium">{performer.achievementCount}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">No data available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}