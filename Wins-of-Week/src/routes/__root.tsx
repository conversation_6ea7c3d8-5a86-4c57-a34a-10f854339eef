import {
  HeadContent,
  <PERSON>,
  Outlet,
  <PERSON>rip<PERSON>,
  createRootRoute,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import appCss from '~/styles/app.css?url'
import { seo } from '~/utils/seo'
import { useAuthStore } from 'luminar-ui'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'L&D Weekly Wins | Staff Achievement Tracking System',
        description: 'Track and celebrate your team\'s weekly achievements, recognitions, and learning initiatives.',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  const user = useAuthStore((state) => state.user)
  const logout = useAuthStore((state) => state.logout)

  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <header className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <h1 className="text-xl font-bold text-gray-900">Wins of the Week</h1>
                <nav className="hidden md:flex space-x-4">
                  <Link
                    to="/"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    activeOptions={{ exact: true }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    Home
                  </Link>
                  <Link
                    to="/submissions"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    Submissions
                  </Link>
                  <Link
                    to="/submissions/new"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    New Submission
                  </Link>
                  <Link
                    to="/dashboard"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/analytics"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    Analytics
                  </Link>
                  <Link
                    to="/reports"
                    activeProps={{
                      className: 'font-bold text-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                  >
                    Reports
                  </Link>
                </nav>
              </div>
              <div className="flex items-center space-x-4">
                {user ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">{user.name}</span>
                    <button
                      onClick={() => logout()}
                      className="text-sm text-gray-600 hover:text-gray-900"
                    >
                      Sign out
                    </button>
                  </div>
                ) : (
                  <Link
                    to="/"
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    Sign in
                  </Link>
                )}
              </div>
            </div>
          </div>
        </header>
        <main className="flex-1">
          {children}
        </main>
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
