import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useWinsStore } from '@/lib/stores/wins.store';
import { useAuthStore } from '@/lib/stores/auth.store';
import { 
  Trophy, 
  TrendingUp, 
  Users, 
  Calendar, 
  Eye,
  Heart,
  MessageCircle,
  Star,
  Award,
  Target,
  Clock,
  CheckCircle,
  BarChart3,
  PieChart
} from 'lucide-react';
import { format, startOfWeek, endOfWeek, subWeeks } from 'date-fns';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  Cell,
  LineChart,
  Line,
  <PERSON><PERSON><PERSON>,
  Area
} from 'recharts';

interface DashboardStats {
  totalSubmissions: number;
  approvedWins: number;
  pendingApproval: number;
  thisWeekSubmissions: number;
  averageRating: number;
  totalViews: number;
  totalReactions: number;
  mySubmissions: number;
  myApprovedWins: number;
  streakWeeks: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const WinsDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    submissions, 
    approvals, 
    analytics,
    fetchSubmissions, 
    fetchApprovals, 
    fetchAnalytics 
  } = useWinsStore();
  
  const [stats, setStats] = useState<DashboardStats>({
    totalSubmissions: 0,
    approvedWins: 0,
    pendingApproval: 0,
    thisWeekSubmissions: 0,
    averageRating: 0,
    totalViews: 0,
    totalReactions: 0,
    mySubmissions: 0,
    myApprovedWins: 0,
    streakWeeks: 0
  });
  
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        fetchSubmissions(),
        fetchApprovals(),
        fetchAnalytics()
      ]);
      calculateStats();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = () => {
    const currentWeekStart = startOfWeek(new Date());
    const currentWeekEnd = endOfWeek(new Date());
    
    const thisWeekSubmissions = submissions.filter(s => {
      const submissionDate = new Date(s.submittedAt);
      return submissionDate >= currentWeekStart && submissionDate <= currentWeekEnd;
    });

    const approvedSubmissions = submissions.filter(s => s.status === 'approved');
    const pendingSubmissions = submissions.filter(s => s.status === 'pending');
    
    const mySubmissions = submissions.filter(s => s.userId === user?.id);
    const myApprovedWins = mySubmissions.filter(s => s.status === 'approved');
    
    const totalViews = submissions.reduce((sum, s) => sum + (s.views || 0), 0);
    const totalReactions = submissions.reduce((sum, s) => sum + (s.reactions || 0), 0);
    
    const ratingsWithScores = submissions.filter(s => s.rating && s.rating > 0);
    const averageRating = ratingsWithScores.length > 0 
      ? ratingsWithScores.reduce((sum, s) => sum + (s.rating || 0), 0) / ratingsWithScores.length 
      : 0;

    // Calculate streak (simplified - consecutive weeks with submissions)
    let streakWeeks = 0;
    let currentWeek = new Date();
    
    for (let i = 0; i < 12; i++) {
      const weekStart = startOfWeek(subWeeks(currentWeek, i));
      const weekEnd = endOfWeek(subWeeks(currentWeek, i));
      
      const hasSubmissionThisWeek = mySubmissions.some(s => {
        const submissionDate = new Date(s.submittedAt);
        return submissionDate >= weekStart && submissionDate <= weekEnd;
      });
      
      if (hasSubmissionThisWeek) {
        streakWeeks++;
      } else if (i > 0) {
        break;
      }
    }

    setStats({
      totalSubmissions: submissions.length,
      approvedWins: approvedSubmissions.length,
      pendingApproval: pendingSubmissions.length,
      thisWeekSubmissions: thisWeekSubmissions.length,
      averageRating: Math.round(averageRating * 10) / 10,
      totalViews,
      totalReactions,
      mySubmissions: mySubmissions.length,
      myApprovedWins: myApprovedWins.length,
      streakWeeks
    });
  };

  const getSubmissionTrends = () => {
    const weeks = [];
    for (let i = 5; i >= 0; i--) {
      const weekStart = startOfWeek(subWeeks(new Date(), i));
      const weekEnd = endOfWeek(subWeeks(new Date(), i));
      
      const weekSubmissions = submissions.filter(s => {
        const submissionDate = new Date(s.submittedAt);
        return submissionDate >= weekStart && submissionDate <= weekEnd;
      });
      
      const weekApprovals = weekSubmissions.filter(s => s.status === 'approved');
      
      weeks.push({
        week: format(weekStart, 'MMM dd'),
        submissions: weekSubmissions.length,
        approved: weekApprovals.length
      });
    }
    return weeks;
  };

  const getCategoryBreakdown = () => {
    const categoryCount: Record<string, number> = {};
    
    submissions.forEach(submission => {
      categoryCount[submission.category] = (categoryCount[submission.category] || 0) + 1;
    });

    const total = Object.values(categoryCount).reduce((sum, count) => sum + count, 0);
    
    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count,
      percentage: Math.round((count / total) * 100)
    }));
  };

  const getTopWins = () => {
    return submissions
      .filter(s => s.status === 'approved')
      .sort((a, b) => {
        const scoreA = (a.views || 0) + (a.reactions || 0) * 2 + (a.rating || 0) * 5;
        const scoreB = (b.views || 0) + (b.reactions || 0) * 2 + (b.rating || 0) * 5;
        return scoreB - scoreA;
      })
      .slice(0, 5);
  };

  const getRecentActivity = () => {
    const recentSubmissions = submissions
      .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
      .slice(0, 5);

    const recentApprovals = approvals
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    return {
      submissions: recentSubmissions.map(submission => ({
        id: submission.id,
        title: submission.title,
        author: submission.authorName || 'Anonymous',
        date: submission.submittedAt,
        status: submission.status,
        category: submission.category
      })),
      approvals: recentApprovals.map(approval => {
        const submission = submissions.find(s => s.id === approval.submissionId);
        return {
          id: approval.id,
          title: submission?.title || 'Unknown Submission',
          reviewer: approval.reviewerName || 'System',
          date: approval.createdAt,
          status: approval.status,
          feedback: approval.feedback
        };
      })
    };
  };

  const getTeamLeaderboard = () => {
    const userStats: Record<string, { submissions: number; approved: number; name: string }> = {};
    
    submissions.forEach(submission => {
      if (!userStats[submission.userId]) {
        userStats[submission.userId] = {
          submissions: 0,
          approved: 0,
          name: submission.authorName || 'Unknown User'
        };
      }
      userStats[submission.userId].submissions++;
      if (submission.status === 'approved') {
        userStats[submission.userId].approved++;
      }
    });

    return Object.entries(userStats)
      .map(([userId, stats]) => ({
        userId,
        name: stats.name,
        submissions: stats.submissions,
        approved: stats.approved,
        approvalRate: stats.submissions > 0 ? Math.round((stats.approved / stats.submissions) * 100) : 0
      }))
      .sort((a, b) => b.approved - a.approved)
      .slice(0, 10);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Wins Dashboard</h1>
          <p className="text-gray-600">Celebrate achievements and share success stories</p>
        </div>
        <Button onClick={loadDashboardData} disabled={isLoading}>
          Refresh Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              {stats.thisWeekSubmissions} this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Wins</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvedWins}</div>
            <p className="text-xs text-muted-foreground">
              {stats.pendingApproval} pending approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalViews}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalReactions} reactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">My Wins</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.myApprovedWins}</div>
            <p className="text-xs text-muted-foreground">
              {stats.streakWeeks} week streak
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Personal Stats */}
      {stats.mySubmissions > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-900">Your Performance</h3>
                <p className="text-blue-700">
                  {stats.myApprovedWins} of {stats.mySubmissions} submissions approved
                </p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-blue-600">
                  {Math.round((stats.myApprovedWins / stats.mySubmissions) * 100)}%
                </div>
                <div className="text-sm text-blue-600">Approval Rate</div>
              </div>
            </div>
            <Progress 
              value={(stats.myApprovedWins / stats.mySubmissions) * 100} 
              className="mt-4" 
            />
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Submission Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Weekly Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={getSubmissionTrends()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="submissions" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      name="Submissions"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="approved" 
                      stackId="2"
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      name="Approved"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Category Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      dataKey="count"
                      data={getCategoryBreakdown()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                    >
                      {getCategoryBreakdown().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Wins */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Top Wins This Month
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTopWins().map((win, index) => (
                  <div key={win.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{win.title}</h4>
                        <p className="text-sm text-gray-600">{win.authorName} • {win.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{win.views || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-4 w-4" />
                          <span>{win.reactions || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4" />
                          <span>{win.rating || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Submission & Approval Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={getSubmissionTrends()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="week" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="submissions" stroke="#8884d8" strokeWidth={3} name="Submissions" />
                  <Line type="monotone" dataKey="approved" stroke="#82ca9d" strokeWidth={3} name="Approved" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Average Views per Win</span>
                    <span className="font-bold">
                      {stats.approvedWins > 0 ? Math.round(stats.totalViews / stats.approvedWins) : 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Average Reactions per Win</span>
                    <span className="font-bold">
                      {stats.approvedWins > 0 ? Math.round(stats.totalReactions / stats.approvedWins) : 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Average Rating</span>
                    <span className="font-bold">{stats.averageRating}/5</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Approval Rate</span>
                    <span className="font-bold">
                      {stats.totalSubmissions > 0 ? Math.round((stats.approvedWins / stats.totalSubmissions) * 100) : 0}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Weekly Goals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Submissions Target</span>
                      <span>{stats.thisWeekSubmissions}/20</span>
                    </div>
                    <Progress value={(stats.thisWeekSubmissions / 20) * 100} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Team Participation</span>
                      <span>75%</span>
                    </div>
                    <Progress value={75} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Quality Score</span>
                      <span>{stats.averageRating}/5</span>
                    </div>
                    <Progress value={(stats.averageRating / 5) * 100} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="leaderboard" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Leaderboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTeamLeaderboard().map((member, index) => (
                  <div key={member.userId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                        index === 0 ? 'bg-yellow-500' : 
                        index === 1 ? 'bg-gray-400' : 
                        index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{member.name}</h4>
                        <p className="text-sm text-gray-600">
                          {member.approved} approved wins
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        {member.approvalRate}%
                      </div>
                      <div className="text-sm text-gray-600">
                        {member.submissions} submissions
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>🏆 Most Wins</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {getTeamLeaderboard()[0]?.name || 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {getTeamLeaderboard()[0]?.approved || 0} approved wins
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>🎯 Best Approval Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {getTeamLeaderboard().sort((a, b) => b.approvalRate - a.approvalRate)[0]?.approvalRate || 0}%
                  </div>
                  <div className="text-sm text-gray-600">
                    {getTeamLeaderboard().sort((a, b) => b.approvalRate - a.approvalRate)[0]?.name || 'N/A'}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>🔥 Longest Streak</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats.streakWeeks} weeks
                  </div>
                  <div className="text-sm text-gray-600">
                    Current best streak
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Submissions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Submissions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRecentActivity().submissions.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-600">
                          by {activity.author} • {format(new Date(activity.date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant={activity.status === 'approved' ? 'default' : activity.status === 'rejected' ? 'destructive' : 'secondary'}>
                          {activity.status}
                        </Badge>
                        <p className="text-xs text-gray-600 mt-1">{activity.category}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Approvals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Recent Reviews
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRecentActivity().approvals.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-600">
                          reviewed by {activity.reviewer} • {format(new Date(activity.date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant={activity.status === 'approved' ? 'default' : 'destructive'}>
                          {activity.status}
                        </Badge>
                        {activity.feedback && (
                          <p className="text-xs text-gray-600 mt-1 max-w-32 truncate">
                            {activity.feedback}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};