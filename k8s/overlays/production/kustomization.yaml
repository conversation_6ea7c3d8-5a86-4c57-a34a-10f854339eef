apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ld-apps-prod

bases:
  - ../../base

namePrefix: prod-
nameSuffix: ""

commonLabels:
  environment: production
  app.kubernetes.io/instance: production

replicas:
  - name: backend
    count: 3
  - name: training-frontend
    count: 3
  - name: vendors-frontend
    count: 3
  - name: wins-frontend
    count: 3

patches:
  - target:
      kind: Deployment
      name: backend
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "1Gi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "500m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "2Gi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "1000m"

  - target:
      kind: HorizontalPodAutoscaler
      name: backend-hpa
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 3
      - op: replace
        path: /spec/maxReplicas
        value: 20

  - target:
      kind: PersistentVolumeClaim
      name: postgres-pvc
    patch: |-
      - op: replace
        path: /spec/resources/requests/storage
        value: "100Gi"

configMapGenerator:
  - name: ld-config
    behavior: merge
    literals:
      - NODE_ENV=production
      - LOG_LEVEL=warn
      - ENABLE_SWAGGER=false
      - VITE_API_URL=https://api.ld-apps.company.com
      - CORS_ORIGIN=https://ld-apps.company.com

secretGenerator:
  - name: ld-secrets
    behavior: replace
    files:
      - secrets/sealed-secrets.yaml

images:
  - name: ld-backend
    newName: registry.company.com/ld-apps/backend
    newTag: v1.0.0
  - name: ld-training
    newName: registry.company.com/ld-apps/training
    newTag: v1.0.0
  - name: ld-vendors
    newName: registry.company.com/ld-apps/vendors
    newTag: v1.0.0
  - name: ld-wins
    newName: registry.company.com/ld-apps/wins
    newTag: v1.0.0
  - name: postgres
    newName: postgres
    newTag: 15-alpine
  - name: redis
    newName: redis
    newTag: 7-alpine

patchesStrategicMerge:
  - ingress-patch.yaml
  - postgres-storage-patch.yaml
  - resource-limits-patch.yaml