apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-apps-ingress
  namespace: ld-apps-prod
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "200"
    nginx.ingress.kubernetes.io/limit-connections: "50"
    nginx.ingress.kubernetes.io/limit-rps: "50"
    nginx.ingress.kubernetes.io/enable-modsecurity: "true"
    nginx.ingress.kubernetes.io/enable-owasp-modsecurity-crs: "true"
spec:
  tls:
  - hosts:
    - ld-apps.company.com
    secretName: ld-apps-prod-tls
  rules:
  - host: ld-apps.company.com
    http:
      paths:
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: prod-backend-service
            port:
              number: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-api-ingress
  namespace: ld-apps-prod
spec:
  tls:
  - hosts:
    - api.ld-apps.company.com
    secretName: ld-api-prod-tls
  rules:
  - host: api.ld-apps.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prod-backend-service
            port:
              number: 3000