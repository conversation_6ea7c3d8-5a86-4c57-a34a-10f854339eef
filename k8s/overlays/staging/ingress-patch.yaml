apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-apps-ingress
  namespace: ld-apps-staging
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: staging-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Staging Environment - Authentication Required"
spec:
  tls:
  - hosts:
    - staging.ld-apps.company.com
    secretName: ld-apps-staging-tls
  rules:
  - host: staging.ld-apps.company.com
    http:
      paths:
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: staging-backend-service
            port:
              number: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-api-ingress
  namespace: ld-apps-staging
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: staging-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Staging API - Authentication Required"
spec:
  tls:
  - hosts:
    - api.staging.ld-apps.company.com
    secretName: ld-api-staging-tls
  rules:
  - host: api.staging.ld-apps.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: staging-backend-service
            port:
              number: 3000