apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ld-apps-staging

bases:
  - ../../base

namePrefix: staging-
nameSuffix: ""

commonLabels:
  environment: staging
  app.kubernetes.io/instance: staging

replicas:
  - name: backend
    count: 1
  - name: training-frontend
    count: 1
  - name: vendors-frontend
    count: 1
  - name: wins-frontend
    count: 1

patches:
  - target:
      kind: Deployment
      name: backend
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
      - op: add
        path: /spec/template/spec/containers/0/env/-
        value:
          name: ENABLE_DEBUG
          value: "true"

  - target:
      kind: HorizontalPodAutoscaler
      name: backend-hpa
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 1
      - op: replace
        path: /spec/maxReplicas
        value: 3

  - target:
      kind: PersistentVolumeClaim
      name: postgres-pvc
    patch: |-
      - op: replace
        path: /spec/resources/requests/storage
        value: "20Gi"

configMapGenerator:
  - name: ld-config
    behavior: merge
    literals:
      - NODE_ENV=staging
      - LOG_LEVEL=debug
      - ENABLE_SWAGGER=true
      - VITE_API_URL=https://api.staging.ld-apps.company.com
      - CORS_ORIGIN=https://staging.ld-apps.company.com

images:
  - name: ld-backend
    newName: registry.company.com/ld-apps/backend
    newTag: staging
  - name: ld-training
    newName: registry.company.com/ld-apps/training
    newTag: staging
  - name: ld-vendors
    newName: registry.company.com/ld-apps/vendors
    newTag: staging
  - name: ld-wins
    newName: registry.company.com/ld-apps/wins
    newTag: staging
  - name: postgres
    newName: postgres
    newTag: 15-alpine
  - name: redis
    newName: redis
    newTag: 7-alpine

patchesStrategicMerge:
  - ingress-patch.yaml