apiVersion: v1
kind: Secret
metadata:
  name: ld-secrets
  namespace: ld-apps
type: Opaque
stringData:
  DATABASE_USER: "ld_admin"
  DATABASE_PASSWORD: "changeme"  # This will be replaced in production
  JWT_SECRET: "changeme"         # This will be replaced in production
  JWT_REFRESH_SECRET: "changeme" # This will be replaced in production
  REDIS_PASSWORD: "changeme"     # This will be replaced in production
  ENCRYPTION_KEY: "changeme"     # This will be replaced in production
  MAIL_USER: "<EMAIL>"
  MAIL_PASSWORD: "changeme"      # This will be replaced in production
  AWS_ACCESS_KEY_ID: "changeme"  # This will be replaced in production
  AWS_SECRET_ACCESS_KEY: "changeme" # This will be replaced in production
  SENTRY_DSN: ""                 # This will be replaced in production