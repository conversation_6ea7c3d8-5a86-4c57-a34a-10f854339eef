apiVersion: v1
kind: Secret
metadata:
  name: ld-secrets
  namespace: ld-apps
type: Opaque
stringData:
  # Database Credentials
  DATABASE_USERNAME: postgres
  DATABASE_PASSWORD: CHANGE_ME_STRONG_PASSWORD
  
  # Redis Password
  REDIS_PASSWORD: CHANGE_ME_REDIS_PASSWORD
  
  # JWT Secret
  JWT_SECRET: CHANGE_ME_RANDOM_SECRET_KEY
  
  # Session Secret
  SESSION_SECRET: CHANGE_ME_SESSION_SECRET
  
  # Email Configuration (optional)
  SENDGRID_API_KEY: ""
  FROM_EMAIL: "<EMAIL>"