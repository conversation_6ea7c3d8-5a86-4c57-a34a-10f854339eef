apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ld-apps

resources:
  - namespace.yaml
  - configmap.yaml
  - secrets.yaml
  - postgres.yaml
  - redis.yaml
  - backend.yaml
  - training-frontend.yaml
  - vendors-frontend.yaml
  - wins-frontend.yaml
  - ingress.yaml
  - network-policies.yaml
  - pod-disruption-budgets.yaml
  - service-monitors.yaml

commonLabels:
  app.kubernetes.io/name: ld-apps
  app.kubernetes.io/managed-by: kustomize
  app.kubernetes.io/part-of: learning-development

images:
  - name: ld-backend
    newTag: latest
  - name: ld-training
    newTag: latest
  - name: ld-vendors
    newTag: latest
  - name: ld-wins
    newTag: latest

configMapGenerator:
  - name: ld-config
    behavior: merge
    envs:
      - configs/base.env

secretGenerator:
  - name: ld-secrets
    behavior: merge
    envs:
      - secrets/base.env

patches:
  - target:
      kind: Deployment
      name: backend
    patch: |-
      - op: add
        path: /spec/template/metadata/annotations
        value:
          prometheus.io/scrape: "true"
          prometheus.io/port: "3000"
          prometheus.io/path: "/metrics"