apiVersion: apps/v1
kind: Deployment
metadata:
  name: wins-frontend
  namespace: ld-apps
  labels:
    app: wins-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wins-frontend
  template:
    metadata:
      labels:
        app: wins-frontend
    spec:
      containers:
      - name: wins-frontend
        image: ld-wins:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3003
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3003"
        - name: VITE_API_URL
          valueFrom:
            configMapKeyRef:
              name: ld-config
              key: VITE_API_URL
        - name: VITE_APP_NAME
          value: "Wins of the Week"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3003
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3003
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: wins-service
  namespace: ld-apps
  labels:
    app: wins-frontend
spec:
  selector:
    app: wins-frontend
  ports:
  - port: 3003
    targetPort: 3003
    name: http
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: wins-hpa
  namespace: ld-apps
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: wins-frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85