apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-frontend
  namespace: ld-apps
  labels:
    app: training-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: training-frontend
  template:
    metadata:
      labels:
        app: training-frontend
    spec:
      containers:
      - name: training-frontend
        image: ld-training:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: VITE_API_URL
          valueFrom:
            configMapKeyRef:
              name: ld-config
              key: VITE_API_URL
        - name: VITE_APP_NAME
          value: "Training Need Analysis"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: training-service
  namespace: ld-apps
  labels:
    app: training-frontend
spec:
  selector:
    app: training-frontend
  ports:
  - port: 3001
    targetPort: 3001
    name: http
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: training-hpa
  namespace: ld-apps
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: training-frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85