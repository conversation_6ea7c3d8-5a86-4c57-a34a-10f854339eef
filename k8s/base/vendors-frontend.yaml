apiVersion: apps/v1
kind: Deployment
metadata:
  name: vendors-frontend
  namespace: ld-apps
  labels:
    app: vendors-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: vendors-frontend
  template:
    metadata:
      labels:
        app: vendors-frontend
    spec:
      containers:
      - name: vendors-frontend
        image: ld-vendors:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3002
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3002"
        - name: VITE_API_URL
          valueFrom:
            configMapKeyRef:
              name: ld-config
              key: VITE_API_URL
        - name: VITE_APP_NAME
          value: "Vendor Management"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3002
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3002
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: vendors-service
  namespace: ld-apps
  labels:
    app: vendors-frontend
spec:
  selector:
    app: vendors-frontend
  ports:
  - port: 3002
    targetPort: 3002
    name: http
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: vendors-hpa
  namespace: ld-apps
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: vendors-frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85