apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-apps-ingress
  namespace: ld-apps
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - ld-apps.example.com
    secretName: ld-apps-tls
  rules:
  - host: ld-apps.example.com
    http:
      paths:
      # Backend API
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3000
      # Training Frontend
      - path: /training(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: training-service
            port:
              number: 3001
      # Vendors Frontend
      - path: /vendors(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: vendors-service
            port:
              number: 3002
      # Wins Frontend
      - path: /wins(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: wins-service
            port:
              number: 3003
      # Root redirect to training
      - path: /
        pathType: Exact
        backend:
          service:
            name: training-service
            port:
              number: 3001
---
# API subdomain ingress (optional)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ld-api-ingress
  namespace: ld-apps
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://ld-apps.example.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization,Content-Type"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api.ld-apps.example.com
    secretName: ld-api-tls
  rules:
  - host: api.ld-apps.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3000