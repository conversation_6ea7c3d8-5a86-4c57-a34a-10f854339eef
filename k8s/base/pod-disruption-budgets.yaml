apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: backend
  maxUnavailable: 50%
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: training-frontend-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: training-frontend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vendors-frontend-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: vendors-frontend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: wins-frontend-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: wins-frontend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: postgres-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: postgres
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis-pdb
  namespace: ld-apps
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: redis