apiVersion: v1
kind: ConfigMap
metadata:
  name: ld-config
  namespace: ld-apps
data:
  NODE_ENV: "production"
  PORT: "3000"
  VITE_API_URL: "https://api.ld-apps.example.com"
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "ld_apps"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  LOG_LEVEL: "info"
  ENABLE_SWAGGER: "false"
  CORS_ORIGIN: "https://ld-apps.example.com"
  RATE_LIMIT_MAX: "100"
  RATE_LIMIT_WINDOW_MS: "900000"
  FILE_UPLOAD_MAX_SIZE: "104857600"
  SESSION_TIMEOUT: "86400"
  ENABLE_METRICS: "true"
  METRICS_PORT: "9090"