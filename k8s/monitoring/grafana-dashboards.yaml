apiVersion: v1
kind: ConfigMap
metadata:
  name: ld-apps-dashboards
  namespace: monitoring
data:
  ld-apps-overview.json: |
    {
      "dashboard": {
        "title": "L&D Apps Overview",
        "panels": [
          {
            "title": "Request Rate",
            "targets": [
              {
                "expr": "sum(rate(http_request_duration_ms_count[5m])) by (app)",
                "legendFormat": "{{ app }}"
              }
            ],
            "type": "graph"
          },
          {
            "title": "Error Rate",
            "targets": [
              {
                "expr": "sum(rate(http_request_duration_ms_count{status=~\"5..\"}[5m])) by (app) / sum(rate(http_request_duration_ms_count[5m])) by (app)",
                "legendFormat": "{{ app }}"
              }
            ],
            "type": "graph"
          },
          {
            "title": "Response Time (p95)",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_ms_bucket[5m])) by (app, le))",
                "legendFormat": "{{ app }}"
              }
            ],
            "type": "graph"
          },
          {
            "title": "Active Users",
            "targets": [
              {
                "expr": "sum(increase(auth_login_success_total[1h]))",
                "legendFormat": "Logged In Users"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Database Connections",
            "targets": [
              {
                "expr": "prisma_pool_connections_open",
                "legendFormat": "Open Connections"
              },
              {
                "expr": "prisma_pool_connections_limit",
                "legendFormat": "Connection Limit"
              }
            ],
            "type": "graph"
          },
          {
            "title": "Pod Memory Usage",
            "targets": [
              {
                "expr": "container_memory_working_set_bytes{pod=~\".*backend.*|.*frontend.*\"} / container_spec_memory_limit_bytes",
                "legendFormat": "{{ pod }}"
              }
            ],
            "type": "graph"
          }
        ]
      }
    }
  
  training-app-dashboard.json: |
    {
      "dashboard": {
        "title": "Training App Dashboard",
        "panels": [
          {
            "title": "Training Enrollments",
            "targets": [
              {
                "expr": "sum(increase(training_enrollment_created_total[1h]))",
                "legendFormat": "New Enrollments"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Course Completions",
            "targets": [
              {
                "expr": "sum(increase(training_course_completed_total[1h]))",
                "legendFormat": "Completions"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Assessment Scores",
            "targets": [
              {
                "expr": "histogram_quantile(0.5, sum(rate(training_assessment_score_bucket[1h])) by (le))",
                "legendFormat": "Median Score"
              }
            ],
            "type": "gauge"
          },
          {
            "title": "Training API Response Times",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_ms_bucket{handler=~\"/api/training.*\"}[5m])) by (handler, le))",
                "legendFormat": "{{ handler }}"
              }
            ],
            "type": "graph"
          }
        ]
      }
    }
  
  vendors-app-dashboard.json: |
    {
      "dashboard": {
        "title": "Vendors App Dashboard",
        "panels": [
          {
            "title": "Active Vendors",
            "targets": [
              {
                "expr": "vendors_active_total",
                "legendFormat": "Active Vendors"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Contract Values",
            "targets": [
              {
                "expr": "sum(vendors_contract_value_total)",
                "legendFormat": "Total Contract Value"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Vendor Evaluations",
            "targets": [
              {
                "expr": "sum(increase(vendors_evaluation_completed_total[1d]))",
                "legendFormat": "Evaluations Today"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Vendor Performance Scores",
            "targets": [
              {
                "expr": "avg(vendors_performance_score) by (category)",
                "legendFormat": "{{ category }}"
              }
            ],
            "type": "bar"
          }
        ]
      }
    }
  
  wins-app-dashboard.json: |
    {
      "dashboard": {
        "title": "Wins of the Week Dashboard",
        "panels": [
          {
            "title": "Weekly Submissions",
            "targets": [
              {
                "expr": "sum(increase(wins_submission_created_total[1w]))",
                "legendFormat": "This Week"
              }
            ],
            "type": "stat"
          },
          {
            "title": "Approval Rate",
            "targets": [
              {
                "expr": "sum(increase(wins_submission_approved_total[1w])) / sum(increase(wins_submission_created_total[1w]))",
                "legendFormat": "Approval Rate"
              }
            ],
            "type": "gauge"
          },
          {
            "title": "Top Categories",
            "targets": [
              {
                "expr": "topk(5, sum(increase(wins_submission_by_category_total[1w])) by (category))",
                "legendFormat": "{{ category }}"
              }
            ],
            "type": "pie"
          },
          {
            "title": "Engagement Metrics",
            "targets": [
              {
                "expr": "sum(increase(wins_views_total[1d]))",
                "legendFormat": "Views Today"
              },
              {
                "expr": "sum(increase(wins_reactions_total[1d]))",
                "legendFormat": "Reactions Today"
              }
            ],
            "type": "graph"
          }
        ]
      }
    }