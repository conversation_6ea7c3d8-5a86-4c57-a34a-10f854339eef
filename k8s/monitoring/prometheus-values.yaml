# Prometheus Helm Chart Values for L&D Apps
prometheus:
  prometheusSpec:
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: fast-ssd
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi
    
    retention: 30d
    retentionSize: "45GB"
    
    serviceMonitorSelectorNilUsesHelmValues: false
    serviceMonitorSelector:
      matchLabels:
        release: prometheus
    
    resources:
      requests:
        memory: 2Gi
        cpu: 1
      limits:
        memory: 4Gi
        cpu: 2
    
    additionalScrapeConfigs:
      - job_name: 'ld-apps-backend'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - ld-apps
                - ld-apps-prod
                - ld-apps-staging
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
    
    ## Prometheus alerting rules
    additionalPrometheusRulesMap:
      ld-apps-rules:
        groups:
          - name: ld-apps.rules
            interval: 30s
            rules:
              - alert: BackendHighErrorRate
                expr: |
                  (
                    sum(rate(http_request_duration_ms_count{status=~"5.."}[5m])) by (job)
                    /
                    sum(rate(http_request_duration_ms_count[5m])) by (job)
                  ) > 0.05
                for: 5m
                labels:
                  severity: critical
                annotations:
                  summary: "High error rate on {{ $labels.job }}"
                  description: "{{ $labels.job }} has error rate of {{ $value | humanizePercentage }}"
              
              - alert: BackendHighLatency
                expr: |
                  histogram_quantile(0.95,
                    sum(rate(http_request_duration_ms_bucket[5m])) by (job, le)
                  ) > 1000
                for: 5m
                labels:
                  severity: warning
                annotations:
                  summary: "High latency on {{ $labels.job }}"
                  description: "95th percentile latency is {{ $value }}ms"
              
              - alert: DatabaseConnectionPoolExhausted
                expr: |
                  (
                    prisma_pool_connections_open
                    /
                    prisma_pool_connections_limit
                  ) > 0.9
                for: 5m
                labels:
                  severity: critical
                annotations:
                  summary: "Database connection pool almost exhausted"
                  description: "Connection pool is {{ $value | humanizePercentage }} full"
              
              - alert: HighMemoryUsage
                expr: |
                  (
                    container_memory_working_set_bytes{pod=~".*backend.*|.*frontend.*"}
                    /
                    container_spec_memory_limit_bytes
                  ) > 0.9
                for: 5m
                labels:
                  severity: warning
                annotations:
                  summary: "High memory usage in {{ $labels.pod }}"
                  description: "Memory usage is {{ $value | humanizePercentage }}"

alertmanager:
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: standard
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 10Gi
    
    resources:
      requests:
        memory: 200Mi
        cpu: 100m
      limits:
        memory: 500Mi
        cpu: 200m
    
    config:
      global:
        resolve_timeout: 5m
        slack_api_url: 'YOUR_SLACK_WEBHOOK_URL'
      
      route:
        group_by: ['alertname', 'cluster', 'service']
        group_wait: 10s
        group_interval: 10s
        repeat_interval: 12h
        receiver: 'default-receiver'
        routes:
          - match:
              severity: critical
            receiver: critical-receiver
            continue: true
          - match:
              severity: warning
            receiver: warning-receiver
      
      receivers:
        - name: 'default-receiver'
          slack_configs:
            - channel: '#ld-apps-alerts'
              title: 'L&D Apps Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}\n{{ end }}'
        
        - name: 'critical-receiver'
          slack_configs:
            - channel: '#ld-apps-critical'
              title: '🚨 CRITICAL: L&D Apps Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}\n{{ end }}'
          pagerduty_configs:
            - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
        
        - name: 'warning-receiver'
          slack_configs:
            - channel: '#ld-apps-alerts'
              title: '⚠️ Warning: L&D Apps Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}\n{{ end }}'

grafana:
  enabled: true
  adminPassword: changeme
  
  persistence:
    enabled: true
    storageClassName: standard
    size: 10Gi
  
  resources:
    requests:
      memory: 256Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 200m
  
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'ld-apps'
          orgId: 1
          folder: 'L&D Applications'
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/ld-apps
  
  dashboardsConfigMaps:
    ld-apps: "ld-apps-dashboards"