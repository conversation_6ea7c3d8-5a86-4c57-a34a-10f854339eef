#!/bin/bash

# Install monitoring stack for L&D Apps
# This script installs Prometheus, Grafana, and ELK stack

set -e

echo "Installing monitoring stack for L&D Apps..."

# Create monitoring namespace
kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -

# Install Prometheus Operator using Helm
echo "Installing Prometheus Operator..."
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install kube-prometheus-stack
helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --values prometheus-values.yaml \
  --wait

# Install Grafana Dashboards
echo "Installing Grafana dashboards..."
kubectl apply -f grafana-dashboards.yaml

# Install ELK Stack
echo "Installing ELK Stack..."
kubectl apply -f elk-stack.yaml

# Wait for Elasticsearch to be ready
echo "Waiting for Elasticsearch to be ready..."
kubectl wait --for=condition=ready pod -l app=elasticsearch -n monitoring --timeout=300s

# Install Filebeat
echo "Installing Filebeat..."
kubectl apply -f filebeat-daemonset.yaml

# Create index templates in Elasticsearch
echo "Creating Elasticsearch index templates..."
kubectl exec -n monitoring deployment/elasticsearch -- curl -X PUT "localhost:9200/_index_template/ld-apps-template" \
  -H "Content-Type: application/json" \
  -u "elastic:${ELASTIC_PASSWORD:-changeme}" \
  -d '{
    "index_patterns": ["ld-apps-*"],
    "template": {
      "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "index.lifecycle.name": "ld-apps-policy",
        "index.lifecycle.rollover_alias": "ld-apps"
      },
      "mappings": {
        "properties": {
          "@timestamp": { "type": "date" },
          "log_level": { "type": "keyword" },
          "app_name": { "type": "keyword" },
          "namespace": { "type": "keyword" },
          "pod_name": { "type": "keyword" },
          "log_message": { "type": "text" },
          "error_message": { "type": "text" },
          "error_stack": { "type": "text" }
        }
      }
    }
  }'

# Create ILM policy
kubectl exec -n monitoring deployment/elasticsearch -- curl -X PUT "localhost:9200/_ilm/policy/ld-apps-policy" \
  -H "Content-Type: application/json" \
  -u "elastic:${ELASTIC_PASSWORD:-changeme}" \
  -d '{
    "policy": {
      "phases": {
        "hot": {
          "actions": {
            "rollover": {
              "max_size": "10GB",
              "max_age": "7d"
            }
          }
        },
        "warm": {
          "min_age": "7d",
          "actions": {
            "shrink": {
              "number_of_shards": 1
            }
          }
        },
        "cold": {
          "min_age": "30d",
          "actions": {
            "allocate": {
              "number_of_replicas": 0
            }
          }
        },
        "delete": {
          "min_age": "90d"
        }
      }
    }
  }'

# Get service URLs
echo "Getting service URLs..."
echo "Prometheus: http://$(kubectl get svc -n monitoring prometheus-kube-prometheus-prometheus -o jsonpath='{.spec.clusterIP}'):9090"
echo "Grafana: http://$(kubectl get svc -n monitoring prometheus-grafana -o jsonpath='{.spec.clusterIP}'):80"
echo "Grafana admin password: $(kubectl get secret -n monitoring prometheus-grafana -o jsonpath='{.data.admin-password}' | base64 -d)"
echo "Kibana: http://$(kubectl get svc -n monitoring kibana -o jsonpath='{.spec.clusterIP}'):5601"
echo "Elasticsearch: http://$(kubectl get svc -n monitoring elasticsearch -o jsonpath='{.spec.clusterIP}'):9200"

echo "Monitoring stack installation completed!"
echo ""
echo "To access services from outside the cluster:"
echo "kubectl port-forward -n monitoring svc/prometheus-kube-prometheus-prometheus 9090:9090"
echo "kubectl port-forward -n monitoring svc/prometheus-grafana 3000:80"
echo "kubectl port-forward -n monitoring svc/kibana 5601:5601"
echo ""
echo "Note: Update the passwords and secrets before using in production!"