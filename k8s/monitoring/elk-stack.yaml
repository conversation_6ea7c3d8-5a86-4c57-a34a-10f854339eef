apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: monitoring
data:
  elasticsearch.yml: |
    cluster.name: "ld-apps-logs"
    network.host: 0.0.0.0
    discovery.type: single-node
    xpack.security.enabled: true
    xpack.security.authc.api_key.enabled: true
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: monitoring
spec:
  serviceName: elasticsearch
  replicas: 1
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
        ports:
        - containerPort: 9200
          name: http
        - containerPort: 9300
          name: transport
        env:
        - name: ES_JAVA_OPTS
          value: "-Xmx2g -Xms2g"
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elastic-credentials
              key: password
        volumeMounts:
        - name: config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
        - name: data
          mountPath: /usr/share/elasticsearch/data
        resources:
          requests:
            memory: "3Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: config
        configMap:
          name: elasticsearch-config
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 50Gi
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: monitoring
spec:
  selector:
    app: elasticsearch
  ports:
  - port: 9200
    name: http
  - port: 9300
    name: transport
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: monitoring
data:
  logstash.yml: |
    http.host: "0.0.0.0"
    xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
    xpack.monitoring.elasticsearch.username: elastic
    xpack.monitoring.elasticsearch.password: "${ELASTIC_PASSWORD}"
  
  pipeline.conf: |
    input {
      beats {
        port => 5044
      }
    }
    
    filter {
      if [kubernetes][labels][app] in ["backend", "training-frontend", "vendors-frontend", "wins-frontend"] {
        json {
          source => "message"
          target => "parsed"
        }
        
        mutate {
          add_field => {
            "app_name" => "%{[kubernetes][labels][app]}"
            "namespace" => "%{[kubernetes][namespace]}"
            "pod_name" => "%{[kubernetes][pod][name]}"
          }
        }
        
        if [parsed][level] {
          mutate {
            add_field => { "log_level" => "%{[parsed][level]}" }
          }
        }
        
        if [parsed][msg] {
          mutate {
            add_field => { "log_message" => "%{[parsed][msg]}" }
          }
        }
        
        if [parsed][error] {
          mutate {
            add_field => { 
              "error_message" => "%{[parsed][error][message]}"
              "error_stack" => "%{[parsed][error][stack]}"
            }
          }
        }
        
        date {
          match => [ "[parsed][time]", "ISO8601" ]
          target => "@timestamp"
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["http://elasticsearch:9200"]
        user => "elastic"
        password => "${ELASTIC_PASSWORD}"
        index => "ld-apps-%{[app_name]}-%{+YYYY.MM.dd}"
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      containers:
      - name: logstash
        image: docker.elastic.co/logstash/logstash:8.11.0
        ports:
        - containerPort: 5044
          name: beats
        env:
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elastic-credentials
              key: password
        volumeMounts:
        - name: config
          mountPath: /usr/share/logstash/config
        - name: pipeline
          mountPath: /usr/share/logstash/pipeline
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: logstash-config
          items:
          - key: logstash.yml
            path: logstash.yml
      - name: pipeline
        configMap:
          name: logstash-config
          items:
          - key: pipeline.conf
            path: pipeline.conf
---
apiVersion: v1
kind: Service
metadata:
  name: logstash
  namespace: monitoring
spec:
  selector:
    app: logstash
  ports:
  - port: 5044
    name: beats
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:8.11.0
        ports:
        - containerPort: 5601
          name: http
        env:
        - name: ELASTICSEARCH_HOSTS
          value: "http://elasticsearch:9200"
        - name: ELASTICSEARCH_USERNAME
          value: "elastic"
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elastic-credentials
              key: password
        - name: SERVER_NAME
          value: "ld-apps-kibana"
        - name: SERVER_HOST
          value: "0.0.0.0"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: monitoring
spec:
  selector:
    app: kibana
  ports:
  - port: 5601
    name: http
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kibana-ingress
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: kibana-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Kibana - Authentication Required"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - kibana.ld-apps.company.com
    secretName: kibana-tls
  rules:
  - host: kibana.ld-apps.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kibana
            port:
              number: 5601
---
apiVersion: v1
kind: Secret
metadata:
  name: elastic-credentials
  namespace: monitoring
type: Opaque
stringData:
  password: "changeme"  # This should be replaced with a strong password
---
apiVersion: v1
kind: Secret
metadata:
  name: kibana-basic-auth
  namespace: monitoring
type: Opaque
data:
  auth: YWRtaW46JGFwcjEkV3VaLkNOSi4kYXV3RnVHYVl0cHVPdHFPL2xENmwvLg==  # admin:admin - replace in production