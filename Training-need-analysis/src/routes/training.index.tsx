import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useTrainingStore, useAuthStore } from 'luminar-ui'
import { useEffect, useState } from 'react'
import { Button } from '~/components/ui/button'

export const Route = createFileRoute('/training/')({
  component: TrainingCatalog,
})

function TrainingCatalog() {
  const [searchQuery, setSearchQuery] = useState('')
  const user = useAuthStore((state) => state.user)
  const {
    courses,
    coursesLoading,
    coursesPagination,
    recommendations,
    fetchCourses,
    fetchRecommendations,
  } = useTrainingStore()

  useEffect(() => {
    // Fetch initial courses and recommendations
    fetchCourses({ page: 1, limit: 20 })
    if (user?.id) {
      fetchRecommendations()
    }
  }, [fetchCourses, fetchRecommendations, user?.id])

  const handleSearch = async () => {
    await fetchCourses({ search: searchQuery, page: 1, limit: 20 })
  }

  // Loading state
  if (coursesLoading && courses.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading courses...</div>
      </div>
    )
  }

  // Categorize courses
  const featuredCourses = courses.filter(c => (c.rating || 0) >= 4.7).slice(0, 3)
  const popularCourses = [...courses].sort((a, b) => (b.enrollmentCount || 0) - (a.enrollmentCount || 0)).slice(0, 6)
  const technicalCourses = courses.filter(c => c.category === 'technical' || c.skills?.some(skill => skill.name?.toLowerCase().includes('tech')))
  const softSkillsCourses = courses.filter(c => c.category === 'soft_skills' || c.skills?.some(skill => skill.name?.toLowerCase().includes('soft')))

  return (
    <div className="space-y-8">
      {/* Search and Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Discover Your Next Learning Adventure
        </h2>
        <p className="text-gray-600 mb-4">
          Explore our comprehensive training catalog designed to close skill gaps and accelerate career growth.
        </p>
        
        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Search courses, skills, or providers..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Button 
            onClick={handleSearch}
            disabled={coursesLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Search
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => fetchCourses({ page: 1, limit: 50 })}>
            View All Courses
          </Button>
          <Link to="/training/learning-paths">
            <Button variant="outline" size="sm">Learning Paths</Button>
          </Link>
          <Link to="/training/recommendations">
            <Button variant="outline" size="sm">
              My Recommendations {recommendations.length > 0 && `(${recommendations.length})`}
            </Button>
          </Link>
        </div>
      </div>

      {/* Training Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Total Courses"
          value={coursesPagination.total || courses.length}
          icon="📚"
          description="Available now"
        />
        <StatCard
          title="Total Enrollments"
          value={courses.reduce((sum, course) => sum + (course.enrollmentCount || 0), 0).toLocaleString()}
          icon="👥"
          description="Across all courses"
        />
        <StatCard
          title="Avg Course Rating"
          value={courses.length > 0 
            ? (courses.reduce((sum, course) => sum + (course.rating || 0), 0) / courses.length).toFixed(1)
            : '0.0'
          }
          icon="⭐"
          description="Student satisfaction"
        />
        <StatCard
          title="Hours of Content"
          value={courses.reduce((sum, course) => sum + (course.duration || 0), 0)}
          icon="⏱️"
          description="Total learning time"
        />
      </div>

      {/* Featured Courses */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Featured Courses</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredCourses.map((course) => (
            <FeaturedCourseCard key={course.id} course={course} />
          ))}
        </div>
      </section>

      {/* Popular Courses */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Most Popular</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>
      </section>

      {/* Browse by Category */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Browse by Category</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CategorySection
            title="Technical Skills"
            courses={technicalCourses.slice(0, 4)}
            icon="💻"
            color="blue"
          />
          <CategorySection
            title="Soft Skills"
            courses={softSkillsCourses.slice(0, 4)}
            icon="🤝"
            color="green"
          />
        </div>
      </section>

      {/* Quick Actions */}
      <section className="bg-white rounded-lg border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ActionCard
            title="Request New Course"
            description="Don't see what you need? Request a new course or training program."
            action="Submit Request"
            icon="📝"
          />
          <ActionCard
            title="Create Learning Path"
            description="Build a custom learning journey tailored to your career goals."
            action="Get Started"
            icon="🛤️"
          />
          <ActionCard
            title="Training Budget"
            description="Check your available training budget and request approval."
            action="View Budget"
            icon="💰"
          />
        </div>
      </section>
    </div>
  )
}

function StatCard({ 
  title, 
  value, 
  icon, 
  description 
}: { 
  title: string
  value: string | number
  icon: string
  description: string 
}) {
  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="flex items-center">
        <span className="text-2xl mr-3">{icon}</span>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-xs text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  )
}

function FeaturedCourseCard({ course }: { course: any }) {
  return (
    <div className="bg-white rounded-lg border overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-32 flex items-center justify-center">
        <span className="text-4xl text-white">🎓</span>
      </div>
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <DifficultyBadge difficulty={course.level || 'beginner'} />
          <div className="flex items-center text-sm text-gray-500">
            <span>⭐ {course.rating || 0}</span>
          </div>
        </div>
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{course.title}</h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{course.description}</p>
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <span>{course.duration || 0}h</span>
          <span>{course.enrollmentCount || 0} enrolled</span>
          <span>{course.price ? `$${course.price}` : 'Free'}</span>
        </div>
        <Link to="/training/$courseId" params={{ courseId: course.id }}>
          <Button className="w-full">
            View Course
          </Button>
        </Link>
      </div>
    </div>
  )
}

function CourseCard({ course }: { course: any }) {
  return (
    <div className="bg-white rounded-lg border p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-2">
        <TypeBadge type={course.format || 'online'} />
        <div className="flex items-center text-sm text-gray-500">
          <span>⭐ {course.rating || 0}</span>
        </div>
      </div>
      
      <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">{course.title}</h3>
      <p className="text-sm text-gray-600 mb-2">{course.provider || 'Internal'}</p>
      
      <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
        <span>{course.duration || 0}h</span>
        <span>{course.price ? `$${course.price}` : 'Free'}</span>
      </div>
      
      <Link to="/training/$courseId" params={{ courseId: course.id }}>
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      </Link>
    </div>
  )
}

function CategorySection({ 
  title, 
  courses, 
  icon, 
  color 
}: { 
  title: string
  courses: any[]
  icon: string
  color: string 
}) {
  const colorClasses = {
    blue: 'border-blue-200 bg-blue-50',
    green: 'border-green-200 bg-green-50',
  }

  return (
    <div className={`rounded-lg border p-6 ${colorClasses[color as keyof typeof colorClasses]}`}>
      <div className="flex items-center mb-4">
        <span className="text-2xl mr-3">{icon}</span>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      <div className="space-y-3">
        {courses.map((course) => (
          <div key={course.id} className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate">{course.title}</h4>
              <p className="text-xs text-gray-600">{course.duration}h • {course.provider}</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">⭐ {course.rating}</span>
              <Link to="/training/$courseId" params={{ courseId: course.id }}>
                <Button variant="outline" size="sm">
                  View
                </Button>
              </Link>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4">
        <Button variant="outline" className="w-full">
          View All {title}
        </Button>
      </div>
    </div>
  )
}

function ActionCard({ 
  title, 
  description, 
  action, 
  icon 
}: { 
  title: string
  description: string
  action: string
  icon: string 
}) {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center mb-2">
        <span className="text-xl mr-2">{icon}</span>
        <h3 className="font-medium text-gray-900">{title}</h3>
      </div>
      <p className="text-sm text-gray-600 mb-3">{description}</p>
      <Button variant="outline" size="sm">
        {action}
      </Button>
    </div>
  )
}

function DifficultyBadge({ difficulty }: { difficulty: string }) {
  const config = {
    beginner: { color: 'bg-green-100 text-green-800', label: 'Beginner' },
    intermediate: { color: 'bg-yellow-100 text-yellow-800', label: 'Intermediate' },
    advanced: { color: 'bg-red-100 text-red-800', label: 'Advanced' },
  }

  const badgeConfig = config[difficulty as keyof typeof config] || config.beginner

  return (
    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${badgeConfig.color}`}>
      {badgeConfig.label}
    </span>
  )
}

function TypeBadge({ type }: { type: string }) {
  const config = {
    online: { color: 'bg-blue-100 text-blue-800', label: 'Online' },
    workshop: { color: 'bg-purple-100 text-purple-800', label: 'Workshop' },
    certification: { color: 'bg-indigo-100 text-indigo-800', label: 'Certification' },
    internal: { color: 'bg-gray-100 text-gray-800', label: 'Internal' },
    external: { color: 'bg-orange-100 text-orange-800', label: 'External' },
  }

  const badgeConfig = config[type as keyof typeof config] || config.online

  return (
    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${badgeConfig.color}`}>
      {badgeConfig.label}
    </span>
  )
}