import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTrainingStore } from '@/lib/stores/training.store';
import { useAuthStore } from '@/lib/stores/auth.store';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Award, 
  Download,
  Calendar,
  Target,
  BookOpen,
  Clock,
  PieChart,
  LineChart
} from 'lucide-react';
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
  Cell,
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

interface ReportFilters {
  startDate: Date;
  endDate: Date;
  courseId?: string;
  category?: string;
  department?: string;
  reportType: string;
}

interface ReportData {
  enrollmentTrends: Array<{ date: string; enrollments: number; completions: number }>;
  categoryBreakdown: Array<{ category: string; count: number; percentage: number }>;
  performanceMetrics: Array<{ metric: string; value: number; target: number }>;
  topCourses: Array<{ title: string; enrollments: number; avgScore: number; completionRate: number }>;
  departmentStats: Array<{ department: string; enrollments: number; completions: number; avgScore: number }>;
  skillsProgress: Array<{ skill: string; proficiency: number; learners: number }>;
  learnerProgress: Array<{ learner: string; coursesCompleted: number; avgScore: number; totalHours: number }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export const TrainingReports: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    courses, 
    enrollments, 
    assessments, 
    analytics,
    fetchCourses, 
    fetchEnrollments, 
    fetchAssessments,
    fetchAnalytics 
  } = useTrainingStore();
  
  const [filters, setFilters] = useState<ReportFilters>({
    startDate: subMonths(new Date(), 3),
    endDate: new Date(),
    reportType: 'overview'
  });
  
  const [reportData, setReportData] = useState<ReportData>({
    enrollmentTrends: [],
    categoryBreakdown: [],
    performanceMetrics: [],
    topCourses: [],
    departmentStats: [],
    skillsProgress: [],
    learnerProgress: []
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    loadReportData();
  }, [filters]);

  const loadReportData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        fetchCourses(),
        fetchEnrollments(),
        fetchAssessments(),
        fetchAnalytics()
      ]);
      generateReportData();
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateReportData = () => {
    // Filter data based on date range
    const filteredEnrollments = enrollments.filter(enrollment => {
      const enrollmentDate = new Date(enrollment.enrolledAt);
      return enrollmentDate >= filters.startDate && enrollmentDate <= filters.endDate;
    });

    const filteredAssessments = assessments.filter(assessment => {
      const assessmentDate = new Date(assessment.completedAt);
      return assessmentDate >= filters.startDate && assessmentDate <= filters.endDate;
    });

    // Generate enrollment trends
    const enrollmentTrends = generateEnrollmentTrends(filteredEnrollments);
    
    // Generate category breakdown
    const categoryBreakdown = generateCategoryBreakdown(filteredEnrollments);
    
    // Generate performance metrics
    const performanceMetrics = generatePerformanceMetrics(filteredEnrollments, filteredAssessments);
    
    // Generate top courses
    const topCourses = generateTopCourses(filteredEnrollments, filteredAssessments);
    
    // Generate department stats
    const departmentStats = generateDepartmentStats(filteredEnrollments, filteredAssessments);
    
    // Generate skills progress
    const skillsProgress = generateSkillsProgress(filteredEnrollments);
    
    // Generate learner progress
    const learnerProgress = generateLearnerProgress(filteredEnrollments, filteredAssessments);

    setReportData({
      enrollmentTrends,
      categoryBreakdown,
      performanceMetrics,
      topCourses,
      departmentStats,
      skillsProgress,
      learnerProgress
    });
  };

  const generateEnrollmentTrends = (enrollments: any[]) => {
    const trends: Record<string, { enrollments: number; completions: number }> = {};
    
    // Initialize dates
    let currentDate = new Date(filters.startDate);
    while (currentDate <= filters.endDate) {
      const dateKey = format(currentDate, 'yyyy-MM-dd');
      trends[dateKey] = { enrollments: 0, completions: 0 };
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }

    // Count enrollments by date
    enrollments.forEach(enrollment => {
      const dateKey = format(new Date(enrollment.enrolledAt), 'yyyy-MM-dd');
      if (trends[dateKey]) {
        trends[dateKey].enrollments++;
        if (enrollment.status === 'completed') {
          trends[dateKey].completions++;
        }
      }
    });

    return Object.entries(trends).map(([date, data]) => ({
      date: format(new Date(date), 'MMM dd'),
      enrollments: data.enrollments,
      completions: data.completions
    }));
  };

  const generateCategoryBreakdown = (enrollments: any[]) => {
    const categoryCount: Record<string, number> = {};
    
    enrollments.forEach(enrollment => {
      const course = courses.find(c => c.id === enrollment.courseId);
      if (course) {
        categoryCount[course.category] = (categoryCount[course.category] || 0) + 1;
      }
    });

    const total = Object.values(categoryCount).reduce((sum, count) => sum + count, 0);
    
    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count,
      percentage: Math.round((count / total) * 100)
    }));
  };

  const generatePerformanceMetrics = (enrollments: any[], assessments: any[]) => {
    const totalEnrollments = enrollments.length;
    const completedEnrollments = enrollments.filter(e => e.status === 'completed').length;
    const avgScore = assessments.length > 0 
      ? assessments.reduce((sum, a) => sum + a.score, 0) / assessments.length 
      : 0;
    
    return [
      { metric: 'Total Enrollments', value: totalEnrollments, target: 1000 },
      { metric: 'Completion Rate', value: Math.round((completedEnrollments / totalEnrollments) * 100), target: 80 },
      { metric: 'Average Score', value: Math.round(avgScore), target: 85 },
      { metric: 'Active Courses', value: courses.filter(c => c.status === 'active').length, target: 50 }
    ];
  };

  const generateTopCourses = (enrollments: any[], assessments: any[]) => {
    const courseStats: Record<string, { enrollments: number; scores: number[]; completions: number }> = {};
    
    enrollments.forEach(enrollment => {
      const courseId = enrollment.courseId;
      if (!courseStats[courseId]) {
        courseStats[courseId] = { enrollments: 0, scores: [], completions: 0 };
      }
      courseStats[courseId].enrollments++;
      if (enrollment.status === 'completed') {
        courseStats[courseId].completions++;
      }
    });

    assessments.forEach(assessment => {
      const enrollment = enrollments.find(e => e.userId === assessment.userId);
      if (enrollment && courseStats[enrollment.courseId]) {
        courseStats[enrollment.courseId].scores.push(assessment.score);
      }
    });

    return Object.entries(courseStats)
      .map(([courseId, stats]) => {
        const course = courses.find(c => c.id === courseId);
        const avgScore = stats.scores.length > 0 
          ? stats.scores.reduce((sum, score) => sum + score, 0) / stats.scores.length 
          : 0;
        const completionRate = stats.enrollments > 0 
          ? (stats.completions / stats.enrollments) * 100 
          : 0;

        return {
          title: course?.title || 'Unknown Course',
          enrollments: stats.enrollments,
          avgScore: Math.round(avgScore),
          completionRate: Math.round(completionRate)
        };
      })
      .sort((a, b) => b.enrollments - a.enrollments)
      .slice(0, 10);
  };

  const generateDepartmentStats = (enrollments: any[], assessments: any[]) => {
    // Mock department data - in real app, this would come from user data
    const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Operations'];
    
    return departments.map(department => {
      const deptEnrollments = Math.floor(Math.random() * 100) + 20;
      const deptCompletions = Math.floor(deptEnrollments * (0.6 + Math.random() * 0.3));
      const deptAvgScore = Math.floor(Math.random() * 20) + 75;
      
      return {
        department,
        enrollments: deptEnrollments,
        completions: deptCompletions,
        avgScore: deptAvgScore
      };
    });
  };

  const generateSkillsProgress = (enrollments: any[]) => {
    const skillsMap: Record<string, { learners: Set<string>; courses: number }> = {};
    
    enrollments.forEach(enrollment => {
      const course = courses.find(c => c.id === enrollment.courseId);
      if (course?.skills) {
        course.skills.forEach(skill => {
          if (!skillsMap[skill]) {
            skillsMap[skill] = { learners: new Set(), courses: 0 };
          }
          skillsMap[skill].learners.add(enrollment.userId);
          skillsMap[skill].courses++;
        });
      }
    });

    return Object.entries(skillsMap)
      .map(([skill, data]) => ({
        skill,
        proficiency: Math.floor(Math.random() * 40) + 60, // Mock proficiency
        learners: data.learners.size
      }))
      .sort((a, b) => b.learners - a.learners)
      .slice(0, 10);
  };

  const generateLearnerProgress = (enrollments: any[], assessments: any[]) => {
    const learnerStats: Record<string, { coursesCompleted: number; scores: number[]; totalHours: number }> = {};
    
    enrollments.forEach(enrollment => {
      if (!learnerStats[enrollment.userId]) {
        learnerStats[enrollment.userId] = { coursesCompleted: 0, scores: [], totalHours: 0 };
      }
      
      if (enrollment.status === 'completed') {
        learnerStats[enrollment.userId].coursesCompleted++;
        const course = courses.find(c => c.id === enrollment.courseId);
        if (course) {
          learnerStats[enrollment.userId].totalHours += course.duration;
        }
      }
    });

    assessments.forEach(assessment => {
      if (learnerStats[assessment.userId]) {
        learnerStats[assessment.userId].scores.push(assessment.score);
      }
    });

    return Object.entries(learnerStats)
      .map(([userId, stats]) => {
        const avgScore = stats.scores.length > 0 
          ? stats.scores.reduce((sum, score) => sum + score, 0) / stats.scores.length 
          : 0;

        return {
          learner: `User ${userId.slice(-4)}`, // Mock learner name
          coursesCompleted: stats.coursesCompleted,
          avgScore: Math.round(avgScore),
          totalHours: stats.totalHours
        };
      })
      .sort((a, b) => b.coursesCompleted - a.coursesCompleted)
      .slice(0, 20);
  };

  const exportReport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      setIsExporting(true);
      // Mock export functionality
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real application, you would call an API to generate and download the report
      console.log(`Exporting report as ${format}`);
      
    } catch (error) {
      console.error('Error exporting report:', error);
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Training Reports</h1>
          <p className="text-gray-600">Analytics and insights for training programs</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => exportReport('excel')}
            disabled={isExporting}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button 
            variant="outline" 
            onClick={() => exportReport('pdf')}
            disabled={isExporting}
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Start Date</label>
              <DatePicker
                selected={filters.startDate}
                onSelect={(date) => date && setFilters(prev => ({ ...prev, startDate: date }))}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">End Date</label>
              <DatePicker
                selected={filters.endDate}
                onSelect={(date) => date && setFilters(prev => ({ ...prev, endDate: date }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Course</label>
              <Select
                value={filters.courseId || ''}
                onValueChange={(value) => setFilters(prev => ({ ...prev, courseId: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Courses</SelectItem>
                  {courses.map(course => (
                    <SelectItem key={course.id} value={course.id}>{course.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <Select
                value={filters.category || ''}
                onValueChange={(value) => setFilters(prev => ({ ...prev, category: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {Array.from(new Set(courses.map(c => c.category))).map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {reportData.performanceMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.metric}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Target: {metric.target}</p>
                  <Badge variant={metric.value >= metric.target ? 'default' : 'secondary'}>
                    {metric.value >= metric.target ? 'On Track' : 'Below Target'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Report Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="enrollment">Enrollment</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="learners">Learners</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enrollment Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Enrollment Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={reportData.enrollmentTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="enrollments" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      name="Enrollments"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="completions" 
                      stackId="2"
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      name="Completions"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Category Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      dataKey="count"
                      data={reportData.categoryBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                    >
                      {reportData.categoryBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Courses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Top Performing Courses
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.topCourses.slice(0, 5).map((course, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{course.title}</h4>
                      <p className="text-sm text-gray-600">
                        {course.enrollments} enrollments • {course.completionRate}% completion rate
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{course.avgScore}%</div>
                      <div className="text-sm text-gray-600">Avg Score</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="enrollment" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Department Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Department Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={reportData.departmentStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="enrollments" fill="#8884d8" name="Enrollments" />
                    <Bar dataKey="completions" fill="#82ca9d" name="Completions" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Enrollment Details */}
            <Card>
              <CardHeader>
                <CardTitle>Enrollment Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportData.departmentStats.map((dept, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{dept.department}</span>
                        <span className="text-sm text-gray-600">
                          {dept.completions}/{dept.enrollments} completed
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(dept.completions / dept.enrollments) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-sm text-gray-600">
                        Average Score: {dept.avgScore}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Performance Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={reportData.topCourses}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="title" angle={-45} textAnchor="end" height={100} />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="avgScore" fill="#8884d8" name="Average Score %" />
                  <Bar dataKey="completionRate" fill="#82ca9d" name="Completion Rate %" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Skills Development Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {reportData.skillsProgress.map((skill, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">{skill.skill}</h4>
                      <Badge variant="outline">{skill.learners} learners</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Proficiency</span>
                        <span>{skill.proficiency}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${skill.proficiency}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="learners" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Learner Progress Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Learner</th>
                      <th className="text-left p-2">Courses Completed</th>
                      <th className="text-left p-2">Average Score</th>
                      <th className="text-left p-2">Total Hours</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.learnerProgress.map((learner, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 font-medium">{learner.learner}</td>
                        <td className="p-2">{learner.coursesCompleted}</td>
                        <td className="p-2">
                          <Badge variant={learner.avgScore >= 80 ? 'default' : 'secondary'}>
                            {learner.avgScore}%
                          </Badge>
                        </td>
                        <td className="p-2">{learner.totalHours}h</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};