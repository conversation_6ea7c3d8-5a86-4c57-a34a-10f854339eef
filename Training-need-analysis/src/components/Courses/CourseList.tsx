import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useTrainingStore } from '@/lib/stores/training.store';
import { useAuthStore } from '@/lib/stores/auth.store';
import { 
  BookOpen, 
  Clock, 
  Users, 
  Star, 
  Search, 
  Filter,
  PlayCircle,
  Calendar,
  Award,
  Target
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  level: string;
  duration: number;
  instructor: string;
  rating: number;
  enrollmentCount: number;
  skills: string[];
  prerequisites: string[];
  thumbnail?: string;
  status: 'active' | 'draft' | 'archived';
  createdAt: string;
  updatedAt: string;
}

interface CourseFilters {
  search: string;
  category: string;
  level: string;
  status: string;
  sortBy: string;
}

export const CourseList: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    courses, 
    enrollments, 
    fetchCourses, 
    fetchEnrollments,
    enrollInCourse 
  } = useTrainingStore();
  
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [filters, setFilters] = useState<CourseFilters>({
    search: '',
    category: '',
    level: '',
    status: '',
    sortBy: 'title'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  useEffect(() => {
    loadCourses();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [courses, filters]);

  const loadCourses = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        fetchCourses(),
        fetchEnrollments()
      ]);
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('Failed to load courses');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...courses];

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(course => 
        course.title.toLowerCase().includes(searchTerm) ||
        course.description.toLowerCase().includes(searchTerm) ||
        course.instructor.toLowerCase().includes(searchTerm) ||
        course.skills.some(skill => skill.toLowerCase().includes(searchTerm))
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(course => course.category === filters.category);
    }

    // Level filter
    if (filters.level) {
      filtered = filtered.filter(course => course.level === filters.level);
    }

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(course => course.status === filters.status);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'rating':
          return b.rating - a.rating;
        case 'duration':
          return a.duration - b.duration;
        case 'enrollments':
          return b.enrollmentCount - a.enrollmentCount;
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

    setFilteredCourses(filtered);
  };

  const handleEnroll = async (courseId: string) => {
    try {
      await enrollInCourse(courseId);
      toast.success('Successfully enrolled in course!');
      await fetchEnrollments(); // Refresh enrollments
    } catch (error) {
      console.error('Error enrolling in course:', error);
      toast.error('Failed to enroll in course');
    }
  };

  const isEnrolled = (courseId: string) => {
    return enrollments.some(e => e.courseId === courseId && e.userId === user?.id);
  };

  const getEnrollmentStatus = (courseId: string) => {
    const enrollment = enrollments.find(e => e.courseId === courseId && e.userId === user?.id);
    return enrollment?.status || null;
  };

  const getCategories = () => {
    return Array.from(new Set(courses.map(course => course.category))).sort();
  };

  const getLevels = () => {
    return Array.from(new Set(courses.map(course => course.level))).sort();
  };

  const CourseCard: React.FC<{ course: Course }> = ({ course }) => {
    const enrolled = isEnrolled(course.id);
    const enrollmentStatus = getEnrollmentStatus(course.id);

    return (
      <Card className="h-full hover:shadow-lg transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start mb-2">
            <Badge variant="secondary">{course.category}</Badge>
            <Badge variant={course.level === 'Beginner' ? 'default' : course.level === 'Intermediate' ? 'secondary' : 'destructive'}>
              {course.level}
            </Badge>
          </div>
          <CardTitle className="text-lg line-clamp-2">{course.title}</CardTitle>
          <p className="text-sm text-gray-600 line-clamp-3">{course.description}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>{course.duration}h</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              <span>{course.enrollmentCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>{course.rating.toFixed(1)}</span>
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium">Skills you'll learn:</p>
            <div className="flex flex-wrap gap-1">
              {course.skills.slice(0, 3).map((skill, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {course.skills.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{course.skills.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Instructor: <span className="font-medium">{course.instructor}</span>
            </p>
          </div>

          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => setSelectedCourse(course)}
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </DialogTrigger>
            </Dialog>
            
            {enrolled ? (
              <Button 
                size="sm" 
                className="flex-1"
                variant={enrollmentStatus === 'completed' ? 'default' : 'secondary'}
                disabled={enrollmentStatus === 'completed'}
              >
                {enrollmentStatus === 'completed' ? (
                  <>
                    <Award className="h-4 w-4 mr-2" />
                    Completed
                  </>
                ) : (
                  <>
                    <PlayCircle className="h-4 w-4 mr-2" />
                    Continue
                  </>
                )}
              </Button>
            ) : (
              <Button 
                size="sm" 
                className="flex-1"
                onClick={() => handleEnroll(course.id)}
              >
                <Target className="h-4 w-4 mr-2" />
                Enroll
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const CourseDetailsDialog: React.FC = () => {
    if (!selectedCourse) return null;

    const enrolled = isEnrolled(selectedCourse.id);
    const enrollmentStatus = getEnrollmentStatus(selectedCourse.id);

    return (
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{selectedCourse.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary">{selectedCourse.category}</Badge>
            <Badge variant={selectedCourse.level === 'Beginner' ? 'default' : selectedCourse.level === 'Intermediate' ? 'secondary' : 'destructive'}>
              {selectedCourse.level}
            </Badge>
            <Badge variant="outline">{selectedCourse.duration} hours</Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-gray-600">{selectedCourse.description}</p>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Skills You'll Learn</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedCourse.skills.map((skill, index) => (
                    <Badge key={index} variant="outline">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              {selectedCourse.prerequisites.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-2">Prerequisites</h3>
                  <ul className="list-disc list-inside space-y-1">
                    {selectedCourse.prerequisites.map((prereq, index) => (
                      <li key={index} className="text-gray-600">{prereq}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <Card>
                <CardContent className="p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">Duration: {selectedCourse.duration} hours</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    <span className="text-sm">{selectedCourse.enrollmentCount} enrolled</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm">{selectedCourse.rating.toFixed(1)} rating</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">
                      Created {format(new Date(selectedCourse.createdAt), 'MMM d, yyyy')}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2">Instructor</h4>
                  <p className="text-sm font-medium">{selectedCourse.instructor}</p>
                </CardContent>
              </Card>

              {enrolled ? (
                <Button 
                  className="w-full"
                  variant={enrollmentStatus === 'completed' ? 'default' : 'secondary'}
                  disabled={enrollmentStatus === 'completed'}
                >
                  {enrollmentStatus === 'completed' ? (
                    <>
                      <Award className="h-4 w-4 mr-2" />
                      Completed
                    </>
                  ) : (
                    <>
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Continue Learning
                    </>
                  )}
                </Button>
              ) : (
                <Button 
                  className="w-full"
                  onClick={() => {
                    handleEnroll(selectedCourse.id);
                    setSelectedCourse(null);
                  }}
                >
                  <Target className="h-4 w-4 mr-2" />
                  Enroll Now
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Course Catalog</h1>
          <p className="text-gray-600">Discover and enroll in training courses</p>
        </div>
        <Button onClick={loadCourses} disabled={isLoading}>
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search courses..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.category}
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {getCategories().map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.level}
              onValueChange={(value) => setFilters(prev => ({ ...prev, level: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Levels</SelectItem>
                {getLevels().map(level => (
                  <SelectItem key={level} value={level}>{level}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.status}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortBy}
              onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="rating">Rating</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="enrollments">Enrollments</SelectItem>
                <SelectItem value="newest">Newest</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredCourses.length} of {courses.length} courses
        </p>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm text-gray-600">
            {Object.values(filters).filter(v => v !== '').length} filters applied
          </span>
        </div>
      </div>

      {/* Course Grid */}
      {filteredCourses.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <BookOpen className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
            <p className="text-gray-600 text-center">
              Try adjusting your filters or search terms to find courses.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>
      )}

      {/* Course Details Dialog */}
      <Dialog open={!!selectedCourse} onOpenChange={(open) => !open && setSelectedCourse(null)}>
        <CourseDetailsDialog />
      </Dialog>
    </div>
  );
};