{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.488.0", "luminar-ui": "file:../shared-ui", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.0", "zustand": "^5.0.0", "axios": "^1.6.0"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "msw": "^2.10.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}, "msw": {"workerDirectory": ["public"]}}