import { apiClient, ApiResponse, TokenPair, User } from '../client';

// Auth DTOs
export interface LoginDto {
  email: string;
  password: string;
  deviceId?: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface LoginResponse {
  user: User;
  tokens: TokenPair;
  sessionId: string;
  requiresVerification?: boolean;
}

export interface RegisterResponse {
  user: User;
  requiresEmailVerification: boolean;
  message: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface PasswordResetRequestDto {
  email: string;
}

export interface PasswordResetConfirmDto {
  token: string;
  newPassword: string;
}

export interface VerifyEmailDto {
  token: string;
}

// Auth API endpoints
export const authApi = {
  // Authentication
  async login(data: LoginDto): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', data);
    const result = response.data;
    
    // Store tokens and user
    apiClient.setTokens(result.tokens);
    apiClient.setUser(result.user);
    
    return result;
  },

  async register(data: RegisterDto): Promise<RegisterResponse> {
    const response = await apiClient.post<RegisterResponse>('/auth/register', data);
    return response.data;
  },

  async logout(refreshToken?: string, sessionId?: string): Promise<void> {
    try {
      await apiClient.post('/auth/logout', { refreshToken, sessionId });
    } finally {
      apiClient.logout();
    }
  },

  async refreshToken(refreshToken: string): Promise<TokenPair> {
    const response = await apiClient.post<TokenPair>('/auth/refresh', { refreshToken });
    const tokens = response.data;
    apiClient.setTokens(tokens);
    return tokens;
  },

  // Profile
  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>('/auth/profile');
    return response.data;
  },

  async changePassword(data: ChangePasswordDto): Promise<{ message: string }> {
    const response = await apiClient.put<{ message: string }>('/auth/change-password', data);
    return response.data;
  },

  // Email verification
  async verifyEmail(data: VerifyEmailDto): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>('/auth/verify-email', data);
    return response.data;
  },

  async resendVerificationEmail(): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>('/auth/resend-verification');
    return response.data;
  },

  // Password reset
  async requestPasswordReset(data: PasswordResetRequestDto): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>('/auth/password-reset/request', data);
    return response.data;
  },

  async confirmPasswordReset(data: PasswordResetConfirmDto): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>('/auth/password-reset/confirm', data);
    return response.data;
  },

  // Sessions
  async getSessions(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/auth/sessions');
    return response.data;
  },

  async terminateSession(sessionId: string): Promise<{ message: string }> {
    const response = await apiClient.delete<{ message: string }>(`/auth/sessions/${sessionId}`);
    return response.data;
  },

  async terminateAllSessions(): Promise<{ message: string }> {
    const response = await apiClient.delete<{ message: string }>('/auth/sessions');
    return response.data;
  },

  // Roles (Admin)
  async getRoles(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/auth/roles');
    return response.data;
  },

  async createRole(data: any): Promise<any> {
    const response = await apiClient.post<any>('/auth/roles', data);
    return response.data;
  },

  async updateRole(roleId: string, data: any): Promise<any> {
    const response = await apiClient.put<any>(`/auth/roles/${roleId}`, data);
    return response.data;
  },

  async deleteRole(roleId: string): Promise<{ message: string }> {
    const response = await apiClient.delete<{ message: string }>(`/auth/roles/${roleId}`);
    return response.data;
  },

  async assignRole(userId: string, roleId: string): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(`/auth/users/${userId}/roles`, { roleId });
    return response.data;
  },

  async removeRole(userId: string, roleId: string): Promise<{ message: string }> {
    const response = await apiClient.delete<{ message: string }>(`/auth/users/${userId}/roles/${roleId}`);
    return response.data;
  },

  // Security (Admin)
  async getSecurityStats(): Promise<any> {
    const response = await apiClient.get<any>('/auth/admin/security-stats');
    return response.data;
  },

  async getAuthAttempts(page: number = 1, limit: number = 50): Promise<any> {
    const response = await apiClient.get<any>('/auth/admin/auth-attempts', {
      params: { page, limit }
    });
    return response.data;
  },

  async getActiveSessions(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/auth/admin/active-sessions');
    return response.data;
  },
};