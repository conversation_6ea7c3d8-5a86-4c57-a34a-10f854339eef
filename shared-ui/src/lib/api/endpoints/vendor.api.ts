import { apiClient, ApiResponse } from '../client';

// Vendor types
export interface PhoneNumber {
  type: 'primary' | 'secondary';
  number: string;
}

export interface Vendor {
  id: string;
  companyName: string;
  contactPerson: string;
  phoneNumbers: PhoneNumber[];
  email: string;
  website?: string;
  category: 'Training' | 'Assessment' | 'Certification' | 'Content' | 'Consulting';
  status: 'active' | 'inactive' | 'pending';
  rating: number;
  certifications: string[];
  createdAt: Date;
  updatedAt: Date;
  proposals?: Proposal[];
  reviews?: Review[];
  _count?: {
    proposals: number;
    reviews: number;
  };
}

export interface CostItem {
  item: string;
  amount: number;
  currency: string;
}

export interface Proposal {
  id: string;
  vendorId: string;
  vendor?: Vendor;
  title: string;
  description: string;
  costs: CostItem[];
  totalCost: number;
  status: 'draft' | 'submitted' | 'negotiation' | 'approved' | 'rejected';
  attachments: string[];
  validUntil: Date;
  submittedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  reviews?: Review[];
}

export interface Review {
  id: string;
  vendorId: string;
  vendor?: Vendor;
  proposalId?: string;
  proposal?: Proposal;
  rating: 1 | 2 | 3 | 4 | 5;
  reviewerName: string;
  reviewerRole: string;
  comment: string;
  strengths: string[];
  improvements: string[];
  createdAt: Date;
}

// DTOs
export interface CreateVendorDto {
  companyName: string;
  contactPerson: string;
  phoneNumbers?: PhoneNumber[];
  email: string;
  website?: string;
  category: 'Training' | 'Assessment' | 'Certification' | 'Content' | 'Consulting';
  status?: 'active' | 'inactive' | 'pending';
  certifications?: string[];
}

export interface UpdateVendorDto extends Partial<CreateVendorDto> {
  rating?: number;
}

export interface VendorFilters {
  search?: string;
  category?: string;
  status?: string;
  minRating?: number;
  maxRating?: number;
  certifications?: string[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateProposalDto {
  vendorId: string;
  title: string;
  description: string;
  costs: CostItem[];
  totalCost: number;
  attachments?: string[];
  validUntil: string;
}

export interface UpdateProposalDto extends Partial<CreateProposalDto> {
  status?: 'draft' | 'submitted' | 'negotiation' | 'approved' | 'rejected';
  submittedAt?: string;
}

export interface ProposalFilters {
  search?: string;
  status?: string;
  vendorId?: string;
  minCost?: number;
  maxCost?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateReviewDto {
  vendorId: string;
  proposalId?: string;
  rating: 1 | 2 | 3 | 4 | 5;
  reviewerName: string;
  reviewerRole: string;
  comment: string;
  strengths?: string[];
  improvements?: string[];
}

export interface DashboardStats {
  totalVendors: number;
  activeVendors: number;
  totalProposals: number;
  approvedProposals: number;
  averageRating: number;
  totalSpent: number;
}

export interface VendorPerformance {
  vendor: Vendor;
  proposalStats: any[];
  reviewStats: any;
  recentActivity: any[];
  performanceScore: number;
}

// Vendor API endpoints
export const vendorApi = {
  // Vendors
  async createVendor(data: CreateVendorDto): Promise<Vendor> {
    const response = await apiClient.post<Vendor>('/vendors', data);
    return response.data;
  },

  async getVendors(filters: VendorFilters = {}): Promise<{ vendors: Vendor[]; pagination: any }> {
    const response = await apiClient.get<{ vendors: Vendor[]; pagination: any }>('/vendors', {
      params: filters
    });
    return response.data;
  },

  async getVendorById(id: string): Promise<Vendor> {
    const response = await apiClient.get<Vendor>(`/vendors/${id}`);
    return response.data;
  },

  async updateVendor(id: string, data: UpdateVendorDto): Promise<Vendor> {
    const response = await apiClient.put<Vendor>(`/vendors/${id}`, data);
    return response.data;
  },

  async deleteVendor(id: string): Promise<void> {
    await apiClient.delete(`/vendors/${id}`);
  },

  async getVendorPerformance(id: string): Promise<VendorPerformance> {
    const response = await apiClient.get<VendorPerformance>(`/vendors/${id}/performance`);
    return response.data;
  },

  async getVendorCategories(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/vendors/categories');
    return response.data;
  },

  async getVendorStatistics(): Promise<any> {
    const response = await apiClient.get<any>('/vendors/statistics');
    return response.data;
  },

  // Vendor workflows
  async initiateVendorOnboarding(vendorId: string): Promise<any> {
    const response = await apiClient.post<any>(`/vendors/${vendorId}/onboard`);
    return response.data;
  },

  async approveVendor(vendorId: string, approverId: string): Promise<any> {
    const response = await apiClient.post<any>(`/vendors/${vendorId}/approve`, { approverId });
    return response.data;
  },

  async suspendVendor(vendorId: string, reason: string, suspendedBy: string): Promise<any> {
    const response = await apiClient.post<any>(`/vendors/${vendorId}/suspend`, { reason, suspendedBy });
    return response.data;
  },

  // Proposals
  async createProposal(data: CreateProposalDto): Promise<Proposal> {
    const response = await apiClient.post<Proposal>('/proposals', data);
    return response.data;
  },

  async getProposals(filters: ProposalFilters = {}): Promise<{ proposals: Proposal[]; pagination: any }> {
    const response = await apiClient.get<{ proposals: Proposal[]; pagination: any }>('/proposals', {
      params: filters
    });
    return response.data;
  },

  async getProposalById(id: string): Promise<Proposal> {
    const response = await apiClient.get<Proposal>(`/proposals/${id}`);
    return response.data;
  },

  async updateProposal(id: string, data: UpdateProposalDto): Promise<Proposal> {
    const response = await apiClient.put<Proposal>(`/proposals/${id}`, data);
    return response.data;
  },

  async deleteProposal(id: string): Promise<void> {
    await apiClient.delete(`/proposals/${id}`);
  },

  async submitProposal(id: string): Promise<Proposal> {
    const response = await apiClient.post<Proposal>(`/proposals/${id}/submit`);
    return response.data;
  },

  async approveProposal(id: string, approverId: string): Promise<Proposal> {
    const response = await apiClient.post<Proposal>(`/proposals/${id}/approve`, { approverId });
    return response.data;
  },

  async rejectProposal(id: string, reason: string): Promise<Proposal> {
    const response = await apiClient.post<Proposal>(`/proposals/${id}/reject`, { reason });
    return response.data;
  },

  async negotiateProposal(id: string, notes: string): Promise<Proposal> {
    const response = await apiClient.post<Proposal>(`/proposals/${id}/negotiate`, { notes });
    return response.data;
  },

  // Reviews
  async createReview(data: CreateReviewDto): Promise<Review> {
    const response = await apiClient.post<Review>('/reviews', data);
    return response.data;
  },

  async getReviews(vendorId?: string, proposalId?: string): Promise<Review[]> {
    const response = await apiClient.get<Review[]>('/reviews', {
      params: { vendorId, proposalId }
    });
    return response.data;
  },

  async getReviewById(id: string): Promise<Review> {
    const response = await apiClient.get<Review>(`/reviews/${id}`);
    return response.data;
  },

  async updateReview(id: string, data: Partial<CreateReviewDto>): Promise<Review> {
    const response = await apiClient.put<Review>(`/reviews/${id}`, data);
    return response.data;
  },

  async deleteReview(id: string): Promise<void> {
    await apiClient.delete(`/reviews/${id}`);
  },

  // Analytics
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await apiClient.get<DashboardStats>('/vendors/dashboard-stats');
    return response.data;
  },

  async getVendorAnalytics(timeRange?: string): Promise<any> {
    const response = await apiClient.get<any>('/vendors/analytics', {
      params: { timeRange }
    });
    return response.data;
  },

  async getTopPerformingVendors(limit: number = 10): Promise<any[]> {
    const response = await apiClient.get<any[]>('/vendors/top-performing', {
      params: { limit }
    });
    return response.data;
  },

  async getVendorTrends(vendorId: string, timeRange?: string): Promise<any> {
    const response = await apiClient.get<any>(`/vendors/${vendorId}/trends`, {
      params: { timeRange }
    });
    return response.data;
  },

  // File upload for proposals
  async uploadProposalAttachment(proposalId: string, file: File, onProgress?: (progress: number) => void): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.upload<any>(`/proposals/${proposalId}/attachments`, formData, onProgress);
    return response.data;
  },

  async deleteProposalAttachment(proposalId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`/proposals/${proposalId}/attachments/${attachmentId}`);
  },
};