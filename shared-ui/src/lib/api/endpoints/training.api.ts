import { apiClient, ApiResponse } from '../client';

// Training types
export interface Skill {
  id: string;
  name: string;
  category: string;
  description: string;
  levels: SkillLevel[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SkillLevel {
  level: number;
  name: string;
  description: string;
  competencies: string[];
}

export interface TrainingCourse {
  id: string;
  title: string;
  description: string;
  provider: string;
  type: 'internal' | 'external' | 'online' | 'workshop' | 'certification';
  duration: number;
  cost: number;
  capacity?: number;
  prerequisites: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  enrollments: number;
  status: 'active' | 'inactive' | 'coming-soon';
  skills?: Skill[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TrainingEnrollment {
  id: string;
  userId: string;
  courseId: string;
  enrolledDate: Date;
  startDate?: Date;
  completedDate?: Date;
  status: 'enrolled' | 'in-progress' | 'completed' | 'dropped' | 'failed';
  progress: number;
  score?: number;
  user?: any;
  course?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Assessment {
  id: string;
  title: string;
  description: string;
  departmentId?: string;
  roleId?: string;
  createdBy: string;
  createdDate: Date;
  status: 'draft' | 'active' | 'completed' | 'archived';
  skillsAssessed: string[];
  dueDate?: Date;
  responses?: AssessmentResponse[];
  updatedAt: Date;
}

export interface AssessmentResponse {
  id: string;
  assessmentId: string;
  userId: string;
  completedDate?: Date;
  status: 'not-started' | 'in-progress' | 'completed';
  skillRatings: SkillRating[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SkillRating {
  skillId: string;
  currentLevel: number;
  requiredLevel: number;
  selfRating?: number;
  managerRating?: number;
  peerRating?: number;
}

export interface SkillGap {
  id: string;
  userId: string;
  skillId: string;
  skillName: string;
  currentLevel: number;
  requiredLevel: number;
  gap: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short-term' | 'long-term';
}

export interface TrainingRecommendation {
  id: string;
  userId: string;
  courseId: string;
  skillGapId: string;
  priority: number;
  reasoning: string;
  expectedImpact: 'high' | 'medium' | 'low';
  estimatedCompletion: Date;
  status: 'pending' | 'accepted' | 'declined';
  course?: TrainingCourse;
  skillGap?: SkillGap;
}

export interface LearningPath {
  id: string;
  title: string;
  description: string;
  roleId?: string;
  courses: TrainingCourse[];
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  skillsAcquired: Skill[];
  completionCriteria: string[];
}

// DTOs
export interface CreateTrainingCourseDto {
  title: string;
  description: string;
  provider: string;
  type: 'internal' | 'external' | 'online' | 'workshop' | 'certification';
  duration: number;
  cost: number;
  capacity?: number;
  prerequisites?: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  status?: 'active' | 'inactive' | 'coming-soon';
  skillIds?: string[];
}

export interface UpdateTrainingCourseDto extends Partial<CreateTrainingCourseDto> {
  rating?: number;
}

export interface TrainingFiltersDto {
  search?: string;
  type?: string;
  difficulty?: string;
  provider?: string;
  status?: string;
  minCost?: number;
  maxCost?: number;
  skills?: string[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateEnrollmentDto {
  userId?: string;
  courseId: string;
  startDate?: string;
}

export interface UpdateEnrollmentDto {
  startDate?: string;
  completedDate?: string;
  status?: 'enrolled' | 'in-progress' | 'completed' | 'dropped' | 'failed';
  progress?: number;
  score?: number;
}

export interface TrainingMetrics {
  totalCourses: number;
  totalEnrollments: number;
  completionRate: number;
  averageScore: number;
  departmentMetrics: any[];
}

export interface CourseAnalytics {
  enrollmentCount: number;
  completionRate: number;
  averageScore: number;
  departmentBreakdown: any[];
  progressTrends: any[];
}

export interface UserProgress {
  totalEnrollments: number;
  completedCourses: number;
  inProgressCourses: number;
  averageScore: number;
  skillProgress: any[];
}

// Training API endpoints
export const trainingApi = {
  // Courses
  async createCourse(data: CreateTrainingCourseDto): Promise<TrainingCourse> {
    const response = await apiClient.post<TrainingCourse>('/training/courses', data);
    return response.data;
  },

  async getCourses(filters: TrainingFiltersDto = {}): Promise<{ courses: TrainingCourse[]; pagination: any }> {
    const response = await apiClient.get<{ courses: TrainingCourse[]; pagination: any }>('/training/courses', {
      params: filters
    });
    return response.data;
  },

  async getCourseById(id: string): Promise<TrainingCourse> {
    const response = await apiClient.get<TrainingCourse>(`/training/courses/${id}`);
    return response.data;
  },

  async updateCourse(id: string, data: UpdateTrainingCourseDto): Promise<TrainingCourse> {
    const response = await apiClient.put<TrainingCourse>(`/training/courses/${id}`, data);
    return response.data;
  },

  async deleteCourse(id: string): Promise<void> {
    await apiClient.delete(`/training/courses/${id}`);
  },

  // Enrollments
  async enrollInCourse(data: CreateEnrollmentDto): Promise<TrainingEnrollment> {
    const response = await apiClient.post<TrainingEnrollment>('/training/enrollments', data);
    return response.data;
  },

  async getEnrollments(userId?: string, courseId?: string): Promise<TrainingEnrollment[]> {
    const response = await apiClient.get<TrainingEnrollment[]>('/training/enrollments', {
      params: { userId, courseId }
    });
    return response.data;
  },

  async updateEnrollment(id: string, data: UpdateEnrollmentDto): Promise<TrainingEnrollment> {
    const response = await apiClient.put<TrainingEnrollment>(`/training/enrollments/${id}`, data);
    return response.data;
  },

  // Recommendations
  async getRecommendations(): Promise<TrainingRecommendation[]> {
    const response = await apiClient.get<TrainingRecommendation[]>('/training/recommendations');
    return response.data;
  },

  async generateRecommendations(): Promise<TrainingRecommendation[]> {
    const response = await apiClient.post<TrainingRecommendation[]>('/training/recommendations/generate');
    return response.data;
  },

  async updateRecommendationStatus(id: string, status: 'accepted' | 'declined'): Promise<any> {
    const response = await apiClient.put<any>(`/training/recommendations/${id}/status`, { status });
    return response.data;
  },

  // Skills
  async getSkills(category?: string): Promise<Skill[]> {
    const response = await apiClient.get<Skill[]>('/training/skills', {
      params: { category }
    });
    return response.data;
  },

  async getSkillById(id: string): Promise<Skill> {
    const response = await apiClient.get<Skill>(`/training/skills/${id}`);
    return response.data;
  },

  async getUserSkills(userId?: string): Promise<any[]> {
    const response = await apiClient.get<any[]>('/training/user-skills', {
      params: { userId }
    });
    return response.data;
  },

  async updateUserSkill(skillId: string, level: number): Promise<any> {
    const response = await apiClient.put<any>(`/training/user-skills/${skillId}`, { level });
    return response.data;
  },

  // Assessments
  async getAssessments(filters: any = {}): Promise<{ assessments: Assessment[]; pagination: any }> {
    const response = await apiClient.get<{ assessments: Assessment[]; pagination: any }>('/training/assessments', {
      params: filters
    });
    return response.data;
  },

  async getAssessmentById(id: string): Promise<Assessment> {
    const response = await apiClient.get<Assessment>(`/training/assessments/${id}`);
    return response.data;
  },

  async createAssessment(data: any): Promise<Assessment> {
    const response = await apiClient.post<Assessment>('/training/assessments', data);
    return response.data;
  },

  async submitAssessmentResponse(assessmentId: string, data: any): Promise<AssessmentResponse> {
    const response = await apiClient.post<AssessmentResponse>(`/training/assessments/${assessmentId}/responses`, data);
    return response.data;
  },

  // Skill Gaps
  async getSkillGaps(userId?: string): Promise<SkillGap[]> {
    const response = await apiClient.get<SkillGap[]>('/training/skill-gaps', {
      params: { userId }
    });
    return response.data;
  },

  async analyzeSkillGaps(userId?: string): Promise<SkillGap[]> {
    const response = await apiClient.post<SkillGap[]>('/training/skill-gaps/analyze', { userId });
    return response.data;
  },

  // Learning Paths
  async getLearningPaths(roleId?: string): Promise<LearningPath[]> {
    const response = await apiClient.get<LearningPath[]>('/training/learning-paths', {
      params: { roleId }
    });
    return response.data;
  },

  async getLearningPathById(id: string): Promise<LearningPath> {
    const response = await apiClient.get<LearningPath>(`/training/learning-paths/${id}`);
    return response.data;
  },

  // Analytics
  async getTrainingMetrics(departmentId?: string): Promise<TrainingMetrics> {
    const response = await apiClient.get<TrainingMetrics>('/training/metrics', {
      params: { departmentId }
    });
    return response.data;
  },

  async getCourseAnalytics(courseId: string): Promise<CourseAnalytics> {
    const response = await apiClient.get<CourseAnalytics>(`/training/courses/${courseId}/analytics`);
    return response.data;
  },

  async getUserProgress(userId?: string): Promise<UserProgress> {
    const response = await apiClient.get<UserProgress>(`/training/users/${userId || 'me'}/progress`);
    return response.data;
  },

  async getROIAnalysis(departmentId?: string): Promise<any> {
    const response = await apiClient.get<any>('/training/roi-analysis', {
      params: { departmentId }
    });
    return response.data;
  },
};