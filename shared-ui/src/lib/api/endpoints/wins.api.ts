import { apiClient, ApiResponse } from '../client';

// Wins types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  date: Date;
  impact: string;
  metrics?: string;
}

export interface Recognition {
  id: string;
  type: 'award' | 'certification' | 'commendation' | 'badge';
  title: string;
  issuer: string;
  date: Date;
  description?: string;
  imageUrl?: string;
}

export interface CostInitiative {
  id: string;
  title: string;
  description: string;
  estimatedSavings: number;
  actualSavings?: number;
  implementationDate: Date;
  status: 'proposed' | 'in-progress' | 'completed';
  roi?: number;
}

export interface TrainingIdea {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'soft-skills' | 'leadership' | 'compliance' | 'other';
  targetAudience: string;
  duration: string;
  format: 'online' | 'in-person' | 'hybrid' | 'self-paced';
  estimatedCost?: number;
  priority: 'low' | 'medium' | 'high';
}

export interface Milestone {
  id: string;
  title: string;
  completed: boolean;
  completionDate?: Date;
}

export interface ProgressUpdate {
  id: string;
  goalTitle: string;
  targetDate: Date;
  currentProgress: number;
  milestones: Milestone[];
  status: 'on-track' | 'at-risk' | 'delayed' | 'completed';
  notes?: string;
}

export interface Attachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  uploadDate: Date;
}

export interface WeeklySubmission {
  id: string;
  userId: string;
  weekStartDate: Date;
  weekEndDate: Date;
  submissionDate: Date;
  achievements: Achievement[];
  recognitions: Recognition[];
  costInitiatives: CostInitiative[];
  trainingIdeas: TrainingIdea[];
  progressUpdates: ProgressUpdate[];
  weeklyHighlight?: string;
  challenges?: string;
  nextWeekFocus?: string;
  attachments?: Attachment[];
  status: 'draft' | 'submitted' | 'reviewed';
  user?: {
    id: string;
    name: string;
    email: string;
    department?: {
      id: string;
      name: string;
    };
    role?: {
      id: string;
      title: string;
      level: string;
    };
  };
  metrics?: SubmissionMetrics;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubmissionMetrics {
  achievementCount: number;
  costInitiativeCount: number;
  trainingIdeaCount: number;
  progressUpdateCount: number;
  totalCostSavings: number;
  completedProgress: number;
  overallProgressPercent: number;
  impactScore: number;
}

// DTOs
export interface CreateSubmissionDto {
  userId: string;
  weekStartDate: string;
  weekEndDate: string;
  achievements?: Achievement[];
  recognitions?: Recognition[];
  costInitiatives?: CostInitiative[];
  trainingIdeas?: TrainingIdea[];
  progressUpdates?: ProgressUpdate[];
  weeklyHighlight?: string;
  challenges?: string;
  nextWeekFocus?: string;
  attachments?: Attachment[];
}

export interface UpdateSubmissionDto extends Partial<CreateSubmissionDto> {
  status?: 'draft' | 'submitted' | 'reviewed';
  submissionDate?: string;
}

export interface SubmissionFilters {
  userId?: string;
  departmentId?: string;
  status?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DashboardStats {
  totalSubmissions: number;
  reviewedSubmissions: number;
  totalAchievements: number;
  totalCostSavings: number;
  totalTrainingIdeas: number;
  topPerformers: any[];
  departmentStats: any[];
  weeklyTrends: any[];
}

export interface SubmissionSummary {
  totalAchievements: number;
  totalRecognitions: number;
  totalCostSavings: number;
  trainingIdeasByCategory: Record<string, number>;
  overallProgress: number;
  topPerformers: any[];
}

export interface TeamProgress {
  departmentId: string;
  departmentName: string;
  totalSubmissions: number;
  completionRate: number;
  totalAchievements: number;
  totalCostSavings: number;
  avgImpactScore: number;
  weeklyProgress: any[];
}

// Wins API endpoints
export const winsApi = {
  // Submissions
  async createSubmission(data: CreateSubmissionDto): Promise<WeeklySubmission> {
    const response = await apiClient.post<WeeklySubmission>('/wins/submissions', data);
    return response.data;
  },

  async getSubmissions(filters: SubmissionFilters = {}): Promise<{ submissions: WeeklySubmission[]; pagination: any }> {
    const response = await apiClient.get<{ submissions: WeeklySubmission[]; pagination: any }>('/wins/submissions', {
      params: filters
    });
    return response.data;
  },

  async getSubmissionById(id: string): Promise<WeeklySubmission> {
    const response = await apiClient.get<WeeklySubmission>(`/wins/submissions/${id}`);
    return response.data;
  },

  async updateSubmission(id: string, data: UpdateSubmissionDto): Promise<WeeklySubmission> {
    const response = await apiClient.put<WeeklySubmission>(`/wins/submissions/${id}`, data);
    return response.data;
  },

  async deleteSubmission(id: string): Promise<void> {
    await apiClient.delete(`/wins/submissions/${id}`);
  },

  // Submission workflow
  async submitForReview(id: string): Promise<WeeklySubmission> {
    const response = await apiClient.post<WeeklySubmission>(`/wins/submissions/${id}/submit`);
    return response.data;
  },

  async approveSubmission(id: string, reviewerId: string): Promise<WeeklySubmission> {
    const response = await apiClient.post<WeeklySubmission>(`/wins/submissions/${id}/approve`, { reviewerId });
    return response.data;
  },

  async rejectSubmission(id: string, reason: string): Promise<WeeklySubmission> {
    const response = await apiClient.post<WeeklySubmission>(`/wins/submissions/${id}/reject`, { reason });
    return response.data;
  },

  // Analytics
  async getDashboardStats(userId?: string, departmentId?: string): Promise<DashboardStats> {
    const response = await apiClient.get<DashboardStats>('/wins/dashboard-stats', {
      params: { userId, departmentId }
    });
    return response.data;
  },

  async getSubmissionSummary(timeRange?: string): Promise<SubmissionSummary> {
    const response = await apiClient.get<SubmissionSummary>('/wins/submission-summary', {
      params: { timeRange }
    });
    return response.data;
  },

  async getTopPerformers(limit: number = 10, timeRange?: string): Promise<any[]> {
    const response = await apiClient.get<any[]>('/wins/top-performers', {
      params: { limit, timeRange }
    });
    return response.data;
  },

  async getTeamProgress(departmentId: string, timeRange?: string): Promise<TeamProgress> {
    const response = await apiClient.get<TeamProgress>(`/wins/team-progress/${departmentId}`, {
      params: { timeRange }
    });
    return response.data;
  },

  async getCostSavingsTrends(timeRange?: string): Promise<any> {
    const response = await apiClient.get<any>('/wins/cost-savings-trends', {
      params: { timeRange }
    });
    return response.data;
  },

  // Reports
  async generateReport(type: string, parameters: any): Promise<any> {
    const response = await apiClient.post<any>('/wins/reports/generate', {
      type,
      parameters
    });
    return response.data;
  },

  async getReports(filters: any = {}): Promise<any[]> {
    const response = await apiClient.get<any[]>('/wins/reports', {
      params: filters
    });
    return response.data;
  },

  async downloadReport(reportId: string, format: 'pdf' | 'excel' | 'csv' = 'pdf'): Promise<Blob> {
    const response = await apiClient.get(`/wins/reports/${reportId}/download`, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  },

  // Achievements
  async getAchievementTemplates(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/wins/achievement-templates');
    return response.data;
  },

  async getAchievementLeaderboard(timeRange?: string): Promise<any[]> {
    const response = await apiClient.get<any[]>('/wins/achievement-leaderboard', {
      params: { timeRange }
    });
    return response.data;
  },

  // File upload for attachments
  async uploadAttachment(submissionId: string, file: File, onProgress?: (progress: number) => void): Promise<Attachment> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.upload<Attachment>(`/wins/submissions/${submissionId}/attachments`, formData, onProgress);
    return response.data;
  },

  async deleteAttachment(submissionId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`/wins/submissions/${submissionId}/attachments/${attachmentId}`);
  },

  // Export functionality
  async exportSubmissions(filters: SubmissionFilters, format: 'pdf' | 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await apiClient.get('/wins/submissions/export', {
      params: { ...filters, format },
      responseType: 'blob'
    });
    return response.data;
  },

  async exportDashboard(timeRange?: string, format: 'pdf' | 'pptx' = 'pdf'): Promise<Blob> {
    const response = await apiClient.get('/wins/dashboard/export', {
      params: { timeRange, format },
      responseType: 'blob'
    });
    return response.data;
  },
};