// Export API client
export { apiClient } from './client';
export type { ApiError, ApiResponse, TokenPair, User } from './client';

// Export all API endpoints
export { authApi } from './endpoints/auth.api';
export { trainingApi } from './endpoints/training.api';
export { vendorApi } from './endpoints/vendor.api';
export { winsApi } from './endpoints/wins.api';

// Export types from auth
export type {
  LoginDto,
  RegisterDto,
  LoginResponse,
  RegisterResponse,
  ChangePasswordDto,
  PasswordResetRequestDto,
  PasswordResetConfirmDto,
  VerifyEmailDto,
} from './endpoints/auth.api';

// Export types from training
export type {
  Skill,
  SkillLevel,
  TrainingCourse,
  TrainingEnrollment,
  Assessment,
  AssessmentResponse,
  SkillRating,
  SkillGap,
  TrainingRecommendation,
  LearningPath,
  CreateTrainingCourseDto,
  UpdateTrainingCourseDto,
  TrainingFiltersDto,
  CreateEnrollmentDto,
  UpdateEnrollmentDto,
  TrainingMetrics,
  CourseAnalytics,
  UserProgress,
} from './endpoints/training.api';

// Export types from vendor
export type {
  PhoneNumber,
  Vendor,
  CostItem,
  Proposal,
  Review,
  CreateVendorDto,
  UpdateVendorDto,
  VendorFilters,
  CreateProposalDto,
  UpdateProposalDto,
  ProposalFilters,
  CreateReviewDto,
  DashboardStats as VendorDashboardStats,
  VendorPerformance,
} from './endpoints/vendor.api';

// Export types from wins
export type {
  Achievement,
  Recognition,
  CostInitiative,
  TrainingIdea,
  Milestone,
  ProgressUpdate,
  Attachment,
  WeeklySubmission,
  SubmissionMetrics,
  CreateSubmissionDto,
  UpdateSubmissionDto,
  SubmissionFilters,
  DashboardStats as WinsDashboardStats,
  SubmissionSummary,
  TeamProgress,
} from './endpoints/wins.api';

// Convenience export for all APIs
export const api = {
  auth: authApi,
  training: trainingApi,
  vendor: vendorApi,
  wins: winsApi,
};