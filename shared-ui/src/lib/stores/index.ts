// Export all stores
export * from './auth.store';
export * from './training.store';
export * from './vendor.store';
export * from './wins.store';

// Export combined store hooks for convenience
export { useAuthStore } from './auth.store';
export { useTrainingStore } from './training.store';
export { useVendorStore } from './vendor.store';
export { useWinsStore } from './wins.store';

// Re-export commonly used hooks
export {
  useUser,
  useIsAuthenticated,
  useAuthLoading,
  useAuthActions,
  useHasPermission,
  useHasRole,
  useHasAnyRole
} from './auth.store';

export {
  useCourses,
  useSelectedCourse,
  useEnrollments,
  useRecommendations,
  useSkills,
  useUserSkills,
  useSkillGaps,
  useAssessments,
  useLearningPaths,
  useTrainingMetrics,
  useUserProgress
} from './training.store';

export {
  useVendors,
  useSelectedVendor,
  useProposals,
  useSelectedProposal,
  useReviews,
  useVendorDashboardStats,
  useVendorPerformance,
  useTopVendors
} from './vendor.store';

export {
  useSubmissions,
  useSelectedSubmission,
  useDraftSubmission,
  useWinsDashboardStats,
  useSubmissionSummary,
  useTopPerformers,
  useTeamProgress,
  useCostSavingsTrends,
  useAchievementLeaderboard,
  useSubmissionWorkflow,
  useDraftActions,
  useWinsAnalytics
} from './wins.store';