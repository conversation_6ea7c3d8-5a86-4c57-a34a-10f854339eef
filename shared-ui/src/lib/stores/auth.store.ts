import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { api, User, LoginDto, RegisterDto, LoginResponse } from '../api';
import { toast } from '../toast';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionId: string | null;

  // Actions
  login: (credentials: LoginDto) => Promise<LoginResponse | null>;
  register: (data: RegisterDto) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  clearError: () => void;
  checkAuth: () => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      sessionId: null,

      // Login action
      login: async (credentials: LoginDto) => {
        set({ isLoading: true, error: null });
        try {
          const response = await api.auth.login(credentials);
          
          set({
            user: response.user,
            isAuthenticated: true,
            sessionId: response.sessionId,
            isLoading: false,
            error: null,
          });

          toast.success('Login successful!');
          return response;
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Login failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          return null;
        }
      },

      // Register action
      register: async (data: RegisterDto) => {
        set({ isLoading: true, error: null });
        try {
          const response = await api.auth.register(data);
          set({ isLoading: false, error: null });
          
          if (response.requiresEmailVerification) {
            toast.info('Please check your email to verify your account.');
          } else {
            toast.success('Registration successful! You can now login.');
          }
          
          return true;
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Registration failed';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return false;
        }
      },

      // Logout action
      logout: async () => {
        const { sessionId } = get();
        const refreshToken = api.auth.getRefreshToken();
        
        set({ isLoading: true });
        try {
          await api.auth.logout(refreshToken || undefined, sessionId || undefined);
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear state regardless of API response
          set({
            user: null,
            isAuthenticated: false,
            sessionId: null,
            isLoading: false,
            error: null,
          });
          toast.info('You have been logged out.');
        }
      },

      // Refresh user data
      refreshUser: async () => {
        const { isAuthenticated } = get();
        if (!isAuthenticated) return;

        try {
          const user = await api.auth.getProfile();
          set({ user });
        } catch (error) {
          console.error('Failed to refresh user:', error);
          // If profile fetch fails, user might be logged out
          if ((error as any).response?.status === 401) {
            get().logout();
          }
        }
      },

      // Update user locally
      updateUser: (updates: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({ user: { ...user, ...updates } });
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Check authentication status on app load
      checkAuth: async () => {
        const token = api.auth.getAccessToken();
        if (!token) {
          set({ isAuthenticated: false, user: null });
          return false;
        }

        set({ isLoading: true });
        try {
          const user = await api.auth.getProfile();
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          });
          return true;
        } catch (error) {
          console.error('Auth check failed:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        sessionId: state.sessionId,
      }),
    }
  )
);

// Helper hooks
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthActions = () => {
  const { login, register, logout, refreshUser, checkAuth } = useAuthStore();
  return { login, register, logout, refreshUser, checkAuth };
};

// Permission checking helper
export const useHasPermission = (permission: string): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => 
    role.permissions.includes(permission)
  );
};

// Role checking helper
export const useHasRole = (roleName: string): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => role.name === roleName);
};

// Multiple roles checking helper
export const useHasAnyRole = (roleNames: string[]): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => roleNames.includes(role.name));
};