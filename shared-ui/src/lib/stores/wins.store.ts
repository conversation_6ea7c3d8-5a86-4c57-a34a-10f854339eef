import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import {
  api,
  WeeklySubmission,
  Achievement,
  Recognition,
  CostInitiative,
  TrainingIdea,
  ProgressUpdate,
  Attachment,
  CreateSubmissionDto,
  UpdateSubmissionDto,
  SubmissionFilters,
  WinsDashboardStats,
  SubmissionSummary,
  TeamProgress,
} from '../api';
import { toast } from '../toast';

interface WinsState {
  // Submissions
  submissions: WeeklySubmission[];
  selectedSubmission: WeeklySubmission | null;
  submissionsLoading: boolean;
  submissionsError: string | null;
  submissionsPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  // Current draft
  draftSubmission: Partial<CreateSubmissionDto> | null;

  // Analytics
  dashboardStats: WinsDashboardStats | null;
  submissionSummary: SubmissionSummary | null;
  topPerformers: any[];
  teamProgress: TeamProgress | null;
  costSavingsTrends: any;
  achievementLeaderboard: any[];

  // Reports
  reports: any[];
  reportsLoading: boolean;

  // Actions - Submissions
  fetchSubmissions: (filters?: SubmissionFilters) => Promise<void>;
  fetchSubmissionById: (id: string) => Promise<void>;
  createSubmission: (data: CreateSubmissionDto) => Promise<WeeklySubmission | null>;
  updateSubmission: (id: string, data: UpdateSubmissionDto) => Promise<boolean>;
  deleteSubmission: (id: string) => Promise<boolean>;
  
  // Actions - Submission workflow
  submitForReview: (id: string) => Promise<boolean>;
  approveSubmission: (id: string, reviewerId: string) => Promise<boolean>;
  rejectSubmission: (id: string, reason: string) => Promise<boolean>;

  // Actions - Draft management
  saveDraft: (data: Partial<CreateSubmissionDto>) => void;
  loadDraft: () => Partial<CreateSubmissionDto> | null;
  clearDraft: () => void;

  // Actions - Analytics
  fetchDashboardStats: (userId?: string, departmentId?: string) => Promise<void>;
  fetchSubmissionSummary: (timeRange?: string) => Promise<void>;
  fetchTopPerformers: (limit?: number, timeRange?: string) => Promise<void>;
  fetchTeamProgress: (departmentId: string, timeRange?: string) => Promise<void>;
  fetchCostSavingsTrends: (timeRange?: string) => Promise<void>;
  fetchAchievementLeaderboard: (timeRange?: string) => Promise<void>;

  // Actions - Reports
  generateReport: (type: string, parameters: any) => Promise<any>;
  fetchReports: (filters?: any) => Promise<void>;
  downloadReport: (reportId: string, format?: 'pdf' | 'excel' | 'csv') => Promise<Blob | null>;

  // Actions - Templates
  fetchAchievementTemplates: () => Promise<any[]>;

  // Actions - File management
  uploadAttachment: (submissionId: string, file: File, onProgress?: (progress: number) => void) => Promise<Attachment | null>;
  deleteAttachment: (submissionId: string, attachmentId: string) => Promise<boolean>;

  // Actions - Export
  exportSubmissions: (filters: SubmissionFilters, format?: 'pdf' | 'excel' | 'csv') => Promise<Blob | null>;
  exportDashboard: (timeRange?: string, format?: 'pdf' | 'pptx') => Promise<Blob | null>;

  // Utility actions
  clearSelectedSubmission: () => void;
  resetState: () => void;

  // Helper methods
  addAchievement: (achievement: Achievement) => void;
  removeAchievement: (achievementId: string) => void;
  addCostInitiative: (initiative: CostInitiative) => void;
  removeCostInitiative: (initiativeId: string) => void;
  addTrainingIdea: (idea: TrainingIdea) => void;
  removeTrainingIdea: (ideaId: string) => void;
  addProgressUpdate: (update: ProgressUpdate) => void;
  removeProgressUpdate: (updateId: string) => void;
}

const initialState = {
  submissions: [],
  selectedSubmission: null,
  submissionsLoading: false,
  submissionsError: null,
  submissionsPagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  draftSubmission: null,
  dashboardStats: null,
  submissionSummary: null,
  topPerformers: [],
  teamProgress: null,
  costSavingsTrends: null,
  achievementLeaderboard: [],
  reports: [],
  reportsLoading: false,
};

export const useWinsStore = create<WinsState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // Submission Actions
      fetchSubmissions: async (filters?: SubmissionFilters) => {
        set({ submissionsLoading: true, submissionsError: null });
        try {
          const response = await api.wins.getSubmissions(filters);
          set({
            submissions: response.submissions,
            submissionsPagination: response.pagination,
            submissionsLoading: false,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Failed to fetch submissions';
          set({ submissionsLoading: false, submissionsError: errorMessage });
          toast.error(errorMessage);
        }
      },

      fetchSubmissionById: async (id: string) => {
        set({ submissionsLoading: true });
        try {
          const submission = await api.wins.getSubmissionById(id);
          set({ selectedSubmission: submission, submissionsLoading: false });
        } catch (error: any) {
          set({ submissionsLoading: false });
          toast.error('Failed to fetch submission details');
        }
      },

      createSubmission: async (data: CreateSubmissionDto) => {
        try {
          const submission = await api.wins.createSubmission(data);
          set((state) => ({
            submissions: [submission, ...state.submissions],
            draftSubmission: null, // Clear draft after successful creation
          }));
          toast.success('Weekly submission created successfully');
          return submission;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to create submission');
          return null;
        }
      },

      updateSubmission: async (id: string, data: UpdateSubmissionDto) => {
        try {
          const updatedSubmission = await api.wins.updateSubmission(id, data);
          set((state) => ({
            submissions: state.submissions.map((s) => (s.id === id ? updatedSubmission : s)),
            selectedSubmission: state.selectedSubmission?.id === id ? updatedSubmission : state.selectedSubmission,
          }));
          toast.success('Submission updated successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to update submission');
          return false;
        }
      },

      deleteSubmission: async (id: string) => {
        try {
          await api.wins.deleteSubmission(id);
          set((state) => ({
            submissions: state.submissions.filter((s) => s.id !== id),
            selectedSubmission: state.selectedSubmission?.id === id ? null : state.selectedSubmission,
          }));
          toast.success('Submission deleted successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to delete submission');
          return false;
        }
      },

      // Workflow Actions
      submitForReview: async (id: string) => {
        try {
          const submission = await api.wins.submitForReview(id);
          set((state) => ({
            submissions: state.submissions.map((s) => (s.id === id ? submission : s)),
            selectedSubmission: state.selectedSubmission?.id === id ? submission : state.selectedSubmission,
          }));
          toast.success('Submission sent for review');
          return true;
        } catch (error: any) {
          toast.error('Failed to submit for review');
          return false;
        }
      },

      approveSubmission: async (id: string, reviewerId: string) => {
        try {
          const submission = await api.wins.approveSubmission(id, reviewerId);
          set((state) => ({
            submissions: state.submissions.map((s) => (s.id === id ? submission : s)),
            selectedSubmission: state.selectedSubmission?.id === id ? submission : state.selectedSubmission,
          }));
          toast.success('Submission approved');
          return true;
        } catch (error: any) {
          toast.error('Failed to approve submission');
          return false;
        }
      },

      rejectSubmission: async (id: string, reason: string) => {
        try {
          const submission = await api.wins.rejectSubmission(id, reason);
          set((state) => ({
            submissions: state.submissions.map((s) => (s.id === id ? submission : s)),
            selectedSubmission: state.selectedSubmission?.id === id ? submission : state.selectedSubmission,
          }));
          toast.success('Submission rejected');
          return true;
        } catch (error: any) {
          toast.error('Failed to reject submission');
          return false;
        }
      },

      // Draft Management
      saveDraft: (data: Partial<CreateSubmissionDto>) => {
        set({ draftSubmission: data });
        localStorage.setItem('wins-draft', JSON.stringify(data));
        toast.info('Draft saved');
      },

      loadDraft: () => {
        const savedDraft = localStorage.getItem('wins-draft');
        if (savedDraft) {
          const draft = JSON.parse(savedDraft);
          set({ draftSubmission: draft });
          return draft;
        }
        return null;
      },

      clearDraft: () => {
        set({ draftSubmission: null });
        localStorage.removeItem('wins-draft');
      },

      // Analytics Actions
      fetchDashboardStats: async (userId?: string, departmentId?: string) => {
        try {
          const stats = await api.wins.getDashboardStats(userId, departmentId);
          set({ dashboardStats: stats });
        } catch (error) {
          toast.error('Failed to fetch dashboard stats');
        }
      },

      fetchSubmissionSummary: async (timeRange?: string) => {
        try {
          const summary = await api.wins.getSubmissionSummary(timeRange);
          set({ submissionSummary: summary });
        } catch (error) {
          toast.error('Failed to fetch submission summary');
        }
      },

      fetchTopPerformers: async (limit: number = 10, timeRange?: string) => {
        try {
          const performers = await api.wins.getTopPerformers(limit, timeRange);
          set({ topPerformers: performers });
        } catch (error) {
          toast.error('Failed to fetch top performers');
        }
      },

      fetchTeamProgress: async (departmentId: string, timeRange?: string) => {
        try {
          const progress = await api.wins.getTeamProgress(departmentId, timeRange);
          set({ teamProgress: progress });
        } catch (error) {
          toast.error('Failed to fetch team progress');
        }
      },

      fetchCostSavingsTrends: async (timeRange?: string) => {
        try {
          const trends = await api.wins.getCostSavingsTrends(timeRange);
          set({ costSavingsTrends: trends });
        } catch (error) {
          toast.error('Failed to fetch cost savings trends');
        }
      },

      fetchAchievementLeaderboard: async (timeRange?: string) => {
        try {
          const leaderboard = await api.wins.getAchievementLeaderboard(timeRange);
          set({ achievementLeaderboard: leaderboard });
        } catch (error) {
          toast.error('Failed to fetch achievement leaderboard');
        }
      },

      // Report Actions
      generateReport: async (type: string, parameters: any) => {
        try {
          const report = await api.wins.generateReport(type, parameters);
          toast.success('Report generated successfully');
          return report;
        } catch (error) {
          toast.error('Failed to generate report');
          return null;
        }
      },

      fetchReports: async (filters?: any) => {
        set({ reportsLoading: true });
        try {
          const reports = await api.wins.getReports(filters);
          set({ reports, reportsLoading: false });
        } catch (error) {
          set({ reportsLoading: false });
          toast.error('Failed to fetch reports');
        }
      },

      downloadReport: async (reportId: string, format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
        try {
          const blob = await api.wins.downloadReport(reportId, format);
          return blob;
        } catch (error) {
          toast.error('Failed to download report');
          return null;
        }
      },

      // Template Actions
      fetchAchievementTemplates: async () => {
        try {
          return await api.wins.getAchievementTemplates();
        } catch (error) {
          toast.error('Failed to fetch achievement templates');
          return [];
        }
      },

      // File Management
      uploadAttachment: async (submissionId: string, file: File, onProgress?: (progress: number) => void) => {
        try {
          const attachment = await api.wins.uploadAttachment(submissionId, file, onProgress);
          toast.success('Attachment uploaded successfully');
          return attachment;
        } catch (error) {
          toast.error('Failed to upload attachment');
          return null;
        }
      },

      deleteAttachment: async (submissionId: string, attachmentId: string) => {
        try {
          await api.wins.deleteAttachment(submissionId, attachmentId);
          toast.success('Attachment deleted successfully');
          return true;
        } catch (error) {
          toast.error('Failed to delete attachment');
          return false;
        }
      },

      // Export Actions
      exportSubmissions: async (filters: SubmissionFilters, format: 'pdf' | 'excel' | 'csv' = 'excel') => {
        try {
          const blob = await api.wins.exportSubmissions(filters, format);
          return blob;
        } catch (error) {
          toast.error('Failed to export submissions');
          return null;
        }
      },

      exportDashboard: async (timeRange?: string, format: 'pdf' | 'pptx' = 'pdf') => {
        try {
          const blob = await api.wins.exportDashboard(timeRange, format);
          return blob;
        } catch (error) {
          toast.error('Failed to export dashboard');
          return null;
        }
      },

      // Helper Methods for Draft Management
      addAchievement: (achievement: Achievement) => {
        const { draftSubmission } = get();
        const achievements = draftSubmission?.achievements || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            achievements: [...achievements, achievement],
          },
        });
      },

      removeAchievement: (achievementId: string) => {
        const { draftSubmission } = get();
        const achievements = draftSubmission?.achievements || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            achievements: achievements.filter((a) => a.id !== achievementId),
          },
        });
      },

      addCostInitiative: (initiative: CostInitiative) => {
        const { draftSubmission } = get();
        const costInitiatives = draftSubmission?.costInitiatives || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            costInitiatives: [...costInitiatives, initiative],
          },
        });
      },

      removeCostInitiative: (initiativeId: string) => {
        const { draftSubmission } = get();
        const costInitiatives = draftSubmission?.costInitiatives || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            costInitiatives: costInitiatives.filter((c) => c.id !== initiativeId),
          },
        });
      },

      addTrainingIdea: (idea: TrainingIdea) => {
        const { draftSubmission } = get();
        const trainingIdeas = draftSubmission?.trainingIdeas || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            trainingIdeas: [...trainingIdeas, idea],
          },
        });
      },

      removeTrainingIdea: (ideaId: string) => {
        const { draftSubmission } = get();
        const trainingIdeas = draftSubmission?.trainingIdeas || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            trainingIdeas: trainingIdeas.filter((t) => t.id !== ideaId),
          },
        });
      },

      addProgressUpdate: (update: ProgressUpdate) => {
        const { draftSubmission } = get();
        const progressUpdates = draftSubmission?.progressUpdates || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            progressUpdates: [...progressUpdates, update],
          },
        });
      },

      removeProgressUpdate: (updateId: string) => {
        const { draftSubmission } = get();
        const progressUpdates = draftSubmission?.progressUpdates || [];
        set({
          draftSubmission: {
            ...draftSubmission,
            progressUpdates: progressUpdates.filter((p) => p.id !== updateId),
          },
        });
      },

      // Utility Actions
      clearSelectedSubmission: () => set({ selectedSubmission: null }),
      resetState: () => set(initialState),
    })),
    {
      name: 'wins-store',
    }
  )
);

// Selector hooks
export const useSubmissions = () => useWinsStore((state) => state.submissions);
export const useSelectedSubmission = () => useWinsStore((state) => state.selectedSubmission);
export const useDraftSubmission = () => useWinsStore((state) => state.draftSubmission);
export const useWinsDashboardStats = () => useWinsStore((state) => state.dashboardStats);
export const useSubmissionSummary = () => useWinsStore((state) => state.submissionSummary);
export const useTopPerformers = () => useWinsStore((state) => state.topPerformers);
export const useTeamProgress = () => useWinsStore((state) => state.teamProgress);
export const useCostSavingsTrends = () => useWinsStore((state) => state.costSavingsTrends);
export const useAchievementLeaderboard = () => useWinsStore((state) => state.achievementLeaderboard);

// Workflow helper hooks
export const useSubmissionWorkflow = () => {
  const { submitForReview, approveSubmission, rejectSubmission } = useWinsStore();
  return { submitForReview, approveSubmission, rejectSubmission };
};

// Draft management hooks
export const useDraftActions = () => {
  const { saveDraft, loadDraft, clearDraft } = useWinsStore();
  return { saveDraft, loadDraft, clearDraft };
};

// Analytics actions hook
export const useWinsAnalytics = () => {
  const {
    fetchDashboardStats,
    fetchSubmissionSummary,
    fetchTopPerformers,
    fetchTeamProgress,
    fetchCostSavingsTrends,
    fetchAchievementLeaderboard,
  } = useWinsStore();
  return {
    fetchDashboardStats,
    fetchSubmissionSummary,
    fetchTopPerformers,
    fetchTeamProgress,
    fetchCostSavingsTrends,
    fetchAchievementLeaderboard,
  };
};