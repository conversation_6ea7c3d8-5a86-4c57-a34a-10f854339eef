import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import {
  api,
  Vendor,
  Proposal,
  Review,
  CreateVendorDto,
  UpdateVendorDto,
  VendorFilters,
  CreateProposalDto,
  UpdateProposalDto,
  ProposalFilters,
  CreateReviewDto,
  VendorDashboardStats,
  VendorPerformance,
} from '../api';
import { toast } from '../toast';

interface VendorState {
  // Vendors
  vendors: Vendor[];
  selectedVendor: Vendor | null;
  vendorsLoading: boolean;
  vendorsError: string | null;
  vendorsPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  // Proposals
  proposals: Proposal[];
  selectedProposal: Proposal | null;
  proposalsLoading: boolean;
  proposalsPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  // Reviews
  reviews: Review[];
  reviewsLoading: boolean;

  // Analytics
  dashboardStats: VendorDashboardStats | null;
  vendorPerformance: VendorPerformance | null;
  topVendors: any[];

  // Actions - Vendors
  fetchVendors: (filters?: VendorFilters) => Promise<void>;
  fetchVendorById: (id: string) => Promise<void>;
  createVendor: (data: CreateVendorDto) => Promise<Vendor | null>;
  updateVendor: (id: string, data: UpdateVendorDto) => Promise<boolean>;
  deleteVendor: (id: string) => Promise<boolean>;
  fetchVendorPerformance: (id: string) => Promise<void>;

  // Actions - Proposals
  fetchProposals: (filters?: ProposalFilters) => Promise<void>;
  fetchProposalById: (id: string) => Promise<void>;
  createProposal: (data: CreateProposalDto) => Promise<Proposal | null>;
  updateProposal: (id: string, data: UpdateProposalDto) => Promise<boolean>;
  deleteProposal: (id: string) => Promise<boolean>;
  submitProposal: (id: string) => Promise<boolean>;
  approveProposal: (id: string, approverId: string) => Promise<boolean>;
  rejectProposal: (id: string, reason: string) => Promise<boolean>;
  negotiateProposal: (id: string, notes: string) => Promise<boolean>;

  // Actions - Reviews
  fetchReviews: (vendorId?: string, proposalId?: string) => Promise<void>;
  createReview: (data: CreateReviewDto) => Promise<Review | null>;
  updateReview: (id: string, data: Partial<CreateReviewDto>) => Promise<boolean>;
  deleteReview: (id: string) => Promise<boolean>;

  // Actions - Analytics
  fetchDashboardStats: () => Promise<void>;
  fetchVendorAnalytics: (timeRange?: string) => Promise<any>;
  fetchTopPerformingVendors: (limit?: number) => Promise<void>;

  // Actions - File Upload
  uploadProposalAttachment: (proposalId: string, file: File, onProgress?: (progress: number) => void) => Promise<boolean>;
  deleteProposalAttachment: (proposalId: string, attachmentId: string) => Promise<boolean>;

  // Utility actions
  clearSelectedVendor: () => void;
  clearSelectedProposal: () => void;
  resetState: () => void;
}

const initialState = {
  vendors: [],
  selectedVendor: null,
  vendorsLoading: false,
  vendorsError: null,
  vendorsPagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  proposals: [],
  selectedProposal: null,
  proposalsLoading: false,
  proposalsPagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  reviews: [],
  reviewsLoading: false,
  dashboardStats: null,
  vendorPerformance: null,
  topVendors: [],
};

export const useVendorStore = create<VendorState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // Vendor Actions
      fetchVendors: async (filters?: VendorFilters) => {
        set({ vendorsLoading: true, vendorsError: null });
        try {
          const response = await api.vendor.getVendors(filters);
          set({
            vendors: response.vendors,
            vendorsPagination: response.pagination,
            vendorsLoading: false,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Failed to fetch vendors';
          set({ vendorsLoading: false, vendorsError: errorMessage });
          toast.error(errorMessage);
        }
      },

      fetchVendorById: async (id: string) => {
        set({ vendorsLoading: true });
        try {
          const vendor = await api.vendor.getVendorById(id);
          set({ selectedVendor: vendor, vendorsLoading: false });
        } catch (error: any) {
          set({ vendorsLoading: false });
          toast.error('Failed to fetch vendor details');
        }
      },

      createVendor: async (data: CreateVendorDto) => {
        try {
          const vendor = await api.vendor.createVendor(data);
          set((state) => ({
            vendors: [vendor, ...state.vendors],
          }));
          toast.success('Vendor created successfully');
          return vendor;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to create vendor');
          return null;
        }
      },

      updateVendor: async (id: string, data: UpdateVendorDto) => {
        try {
          const updatedVendor = await api.vendor.updateVendor(id, data);
          set((state) => ({
            vendors: state.vendors.map((v) => (v.id === id ? updatedVendor : v)),
            selectedVendor: state.selectedVendor?.id === id ? updatedVendor : state.selectedVendor,
          }));
          toast.success('Vendor updated successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to update vendor');
          return false;
        }
      },

      deleteVendor: async (id: string) => {
        try {
          await api.vendor.deleteVendor(id);
          set((state) => ({
            vendors: state.vendors.filter((v) => v.id !== id),
            selectedVendor: state.selectedVendor?.id === id ? null : state.selectedVendor,
          }));
          toast.success('Vendor deleted successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to delete vendor');
          return false;
        }
      },

      fetchVendorPerformance: async (id: string) => {
        try {
          const performance = await api.vendor.getVendorPerformance(id);
          set({ vendorPerformance: performance });
        } catch (error) {
          toast.error('Failed to fetch vendor performance');
        }
      },

      // Proposal Actions
      fetchProposals: async (filters?: ProposalFilters) => {
        set({ proposalsLoading: true });
        try {
          const response = await api.vendor.getProposals(filters);
          set({
            proposals: response.proposals,
            proposalsPagination: response.pagination,
            proposalsLoading: false,
          });
        } catch (error: any) {
          set({ proposalsLoading: false });
          toast.error('Failed to fetch proposals');
        }
      },

      fetchProposalById: async (id: string) => {
        set({ proposalsLoading: true });
        try {
          const proposal = await api.vendor.getProposalById(id);
          set({ selectedProposal: proposal, proposalsLoading: false });
        } catch (error: any) {
          set({ proposalsLoading: false });
          toast.error('Failed to fetch proposal details');
        }
      },

      createProposal: async (data: CreateProposalDto) => {
        try {
          const proposal = await api.vendor.createProposal(data);
          set((state) => ({
            proposals: [proposal, ...state.proposals],
          }));
          toast.success('Proposal created successfully');
          return proposal;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to create proposal');
          return null;
        }
      },

      updateProposal: async (id: string, data: UpdateProposalDto) => {
        try {
          const updatedProposal = await api.vendor.updateProposal(id, data);
          set((state) => ({
            proposals: state.proposals.map((p) => (p.id === id ? updatedProposal : p)),
            selectedProposal: state.selectedProposal?.id === id ? updatedProposal : state.selectedProposal,
          }));
          toast.success('Proposal updated successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to update proposal');
          return false;
        }
      },

      deleteProposal: async (id: string) => {
        try {
          await api.vendor.deleteProposal(id);
          set((state) => ({
            proposals: state.proposals.filter((p) => p.id !== id),
            selectedProposal: state.selectedProposal?.id === id ? null : state.selectedProposal,
          }));
          toast.success('Proposal deleted successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to delete proposal');
          return false;
        }
      },

      submitProposal: async (id: string) => {
        try {
          const proposal = await api.vendor.submitProposal(id);
          set((state) => ({
            proposals: state.proposals.map((p) => (p.id === id ? proposal : p)),
            selectedProposal: state.selectedProposal?.id === id ? proposal : state.selectedProposal,
          }));
          toast.success('Proposal submitted successfully');
          return true;
        } catch (error: any) {
          toast.error('Failed to submit proposal');
          return false;
        }
      },

      approveProposal: async (id: string, approverId: string) => {
        try {
          const proposal = await api.vendor.approveProposal(id, approverId);
          set((state) => ({
            proposals: state.proposals.map((p) => (p.id === id ? proposal : p)),
            selectedProposal: state.selectedProposal?.id === id ? proposal : state.selectedProposal,
          }));
          toast.success('Proposal approved');
          return true;
        } catch (error: any) {
          toast.error('Failed to approve proposal');
          return false;
        }
      },

      rejectProposal: async (id: string, reason: string) => {
        try {
          const proposal = await api.vendor.rejectProposal(id, reason);
          set((state) => ({
            proposals: state.proposals.map((p) => (p.id === id ? proposal : p)),
            selectedProposal: state.selectedProposal?.id === id ? proposal : state.selectedProposal,
          }));
          toast.success('Proposal rejected');
          return true;
        } catch (error: any) {
          toast.error('Failed to reject proposal');
          return false;
        }
      },

      negotiateProposal: async (id: string, notes: string) => {
        try {
          const proposal = await api.vendor.negotiateProposal(id, notes);
          set((state) => ({
            proposals: state.proposals.map((p) => (p.id === id ? proposal : p)),
            selectedProposal: state.selectedProposal?.id === id ? proposal : state.selectedProposal,
          }));
          toast.success('Negotiation notes added');
          return true;
        } catch (error: any) {
          toast.error('Failed to add negotiation notes');
          return false;
        }
      },

      // Review Actions
      fetchReviews: async (vendorId?: string, proposalId?: string) => {
        set({ reviewsLoading: true });
        try {
          const reviews = await api.vendor.getReviews(vendorId, proposalId);
          set({ reviews, reviewsLoading: false });
        } catch (error) {
          set({ reviewsLoading: false });
          toast.error('Failed to fetch reviews');
        }
      },

      createReview: async (data: CreateReviewDto) => {
        try {
          const review = await api.vendor.createReview(data);
          set((state) => ({
            reviews: [review, ...state.reviews],
          }));
          toast.success('Review submitted successfully');
          return review;
        } catch (error: any) {
          toast.error('Failed to submit review');
          return null;
        }
      },

      updateReview: async (id: string, data: Partial<CreateReviewDto>) => {
        try {
          const updatedReview = await api.vendor.updateReview(id, data);
          set((state) => ({
            reviews: state.reviews.map((r) => (r.id === id ? updatedReview : r)),
          }));
          toast.success('Review updated successfully');
          return true;
        } catch (error) {
          toast.error('Failed to update review');
          return false;
        }
      },

      deleteReview: async (id: string) => {
        try {
          await api.vendor.deleteReview(id);
          set((state) => ({
            reviews: state.reviews.filter((r) => r.id !== id),
          }));
          toast.success('Review deleted successfully');
          return true;
        } catch (error) {
          toast.error('Failed to delete review');
          return false;
        }
      },

      // Analytics Actions
      fetchDashboardStats: async () => {
        try {
          const stats = await api.vendor.getDashboardStats();
          set({ dashboardStats: stats });
        } catch (error) {
          toast.error('Failed to fetch dashboard stats');
        }
      },

      fetchVendorAnalytics: async (timeRange?: string) => {
        try {
          return await api.vendor.getVendorAnalytics(timeRange);
        } catch (error) {
          toast.error('Failed to fetch vendor analytics');
          return null;
        }
      },

      fetchTopPerformingVendors: async (limit: number = 10) => {
        try {
          const vendors = await api.vendor.getTopPerformingVendors(limit);
          set({ topVendors: vendors });
        } catch (error) {
          toast.error('Failed to fetch top performing vendors');
        }
      },

      // File Upload Actions
      uploadProposalAttachment: async (proposalId: string, file: File, onProgress?: (progress: number) => void) => {
        try {
          await api.vendor.uploadProposalAttachment(proposalId, file, onProgress);
          toast.success('Attachment uploaded successfully');
          return true;
        } catch (error) {
          toast.error('Failed to upload attachment');
          return false;
        }
      },

      deleteProposalAttachment: async (proposalId: string, attachmentId: string) => {
        try {
          await api.vendor.deleteProposalAttachment(proposalId, attachmentId);
          toast.success('Attachment deleted successfully');
          return true;
        } catch (error) {
          toast.error('Failed to delete attachment');
          return false;
        }
      },

      // Utility Actions
      clearSelectedVendor: () => set({ selectedVendor: null }),
      clearSelectedProposal: () => set({ selectedProposal: null }),
      resetState: () => set(initialState),
    })),
    {
      name: 'vendor-store',
    }
  )
);

// Selector hooks
export const useVendors = () => useVendorStore((state) => state.vendors);
export const useSelectedVendor = () => useVendorStore((state) => state.selectedVendor);
export const useProposals = () => useVendorStore((state) => state.proposals);
export const useSelectedProposal = () => useVendorStore((state) => state.selectedProposal);
export const useReviews = () => useVendorStore((state) => state.reviews);
export const useVendorDashboardStats = () => useVendorStore((state) => state.dashboardStats);
export const useVendorPerformance = () => useVendorStore((state) => state.vendorPerformance);
export const useTopVendors = () => useVendorStore((state) => state.topVendors);