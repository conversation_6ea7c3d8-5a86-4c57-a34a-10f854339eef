import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { 
  api, 
  TrainingCourse, 
  TrainingEnrollment, 
  TrainingRecommendation,
  Skill,
  SkillGap,
  Assessment,
  LearningPath,
  TrainingFiltersDto,
  CreateTrainingCourseDto,
  CreateEnrollmentDto,
  TrainingMetrics,
  UserProgress
} from '../api';
import { toast } from '../toast';

interface TrainingState {
  // Courses
  courses: TrainingCourse[];
  selectedCourse: TrainingCourse | null;
  coursesLoading: boolean;
  coursesError: string | null;
  coursesPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  // Enrollments
  enrollments: TrainingEnrollment[];
  enrollmentsLoading: boolean;

  // Recommendations
  recommendations: TrainingRecommendation[];
  recommendationsLoading: boolean;

  // Skills
  skills: Skill[];
  userSkills: any[];
  skillGaps: SkillGap[];

  // Assessments
  assessments: Assessment[];
  selectedAssessment: Assessment | null;

  // Learning Paths
  learningPaths: LearningPath[];

  // Analytics
  metrics: TrainingMetrics | null;
  userProgress: UserProgress | null;

  // Actions - Courses
  fetchCourses: (filters?: TrainingFiltersDto) => Promise<void>;
  fetchCourseById: (id: string) => Promise<void>;
  createCourse: (data: CreateTrainingCourseDto) => Promise<TrainingCourse | null>;
  updateCourse: (id: string, data: Partial<CreateTrainingCourseDto>) => Promise<boolean>;
  deleteCourse: (id: string) => Promise<boolean>;

  // Actions - Enrollments
  fetchEnrollments: (userId?: string, courseId?: string) => Promise<void>;
  enrollInCourse: (data: CreateEnrollmentDto) => Promise<boolean>;
  updateEnrollment: (id: string, data: any) => Promise<boolean>;

  // Actions - Recommendations
  fetchRecommendations: () => Promise<void>;
  generateRecommendations: () => Promise<void>;
  updateRecommendationStatus: (id: string, status: 'accepted' | 'declined') => Promise<boolean>;

  // Actions - Skills
  fetchSkills: (category?: string) => Promise<void>;
  fetchUserSkills: (userId?: string) => Promise<void>;
  fetchSkillGaps: (userId?: string) => Promise<void>;
  analyzeSkillGaps: (userId?: string) => Promise<void>;

  // Actions - Assessments
  fetchAssessments: (filters?: any) => Promise<void>;
  fetchAssessmentById: (id: string) => Promise<void>;
  createAssessment: (data: any) => Promise<Assessment | null>;
  submitAssessmentResponse: (assessmentId: string, data: any) => Promise<boolean>;

  // Actions - Learning Paths
  fetchLearningPaths: (roleId?: string) => Promise<void>;

  // Actions - Analytics
  fetchTrainingMetrics: (departmentId?: string) => Promise<void>;
  fetchUserProgress: (userId?: string) => Promise<void>;
  fetchCourseAnalytics: (courseId: string) => Promise<any>;

  // Utility actions
  clearSelectedCourse: () => void;
  clearSelectedAssessment: () => void;
  resetState: () => void;
}

const initialState = {
  courses: [],
  selectedCourse: null,
  coursesLoading: false,
  coursesError: null,
  coursesPagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  enrollments: [],
  enrollmentsLoading: false,
  recommendations: [],
  recommendationsLoading: false,
  skills: [],
  userSkills: [],
  skillGaps: [],
  assessments: [],
  selectedAssessment: null,
  learningPaths: [],
  metrics: null,
  userProgress: null,
};

export const useTrainingStore = create<TrainingState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // Course Actions
      fetchCourses: async (filters?: TrainingFiltersDto) => {
        set({ coursesLoading: true, coursesError: null });
        try {
          const response = await api.training.getCourses(filters);
          set({
            courses: response.courses,
            coursesPagination: response.pagination,
            coursesLoading: false,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Failed to fetch courses';
          set({ coursesLoading: false, coursesError: errorMessage });
          toast.error(errorMessage);
        }
      },

      fetchCourseById: async (id: string) => {
        set({ coursesLoading: true });
        try {
          const course = await api.training.getCourseById(id);
          set({ selectedCourse: course, coursesLoading: false });
        } catch (error: any) {
          set({ coursesLoading: false });
          toast.error('Failed to fetch course details');
        }
      },

      createCourse: async (data: CreateTrainingCourseDto) => {
        try {
          const course = await api.training.createCourse(data);
          set((state) => ({
            courses: [course, ...state.courses],
          }));
          toast.success('Course created successfully');
          return course;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to create course');
          return null;
        }
      },

      updateCourse: async (id: string, data: Partial<CreateTrainingCourseDto>) => {
        try {
          const updatedCourse = await api.training.updateCourse(id, data);
          set((state) => ({
            courses: state.courses.map((c) => (c.id === id ? updatedCourse : c)),
            selectedCourse: state.selectedCourse?.id === id ? updatedCourse : state.selectedCourse,
          }));
          toast.success('Course updated successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to update course');
          return false;
        }
      },

      deleteCourse: async (id: string) => {
        try {
          await api.training.deleteCourse(id);
          set((state) => ({
            courses: state.courses.filter((c) => c.id !== id),
            selectedCourse: state.selectedCourse?.id === id ? null : state.selectedCourse,
          }));
          toast.success('Course deleted successfully');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to delete course');
          return false;
        }
      },

      // Enrollment Actions
      fetchEnrollments: async (userId?: string, courseId?: string) => {
        set({ enrollmentsLoading: true });
        try {
          const enrollments = await api.training.getEnrollments(userId, courseId);
          set({ enrollments, enrollmentsLoading: false });
        } catch (error: any) {
          set({ enrollmentsLoading: false });
          toast.error('Failed to fetch enrollments');
        }
      },

      enrollInCourse: async (data: CreateEnrollmentDto) => {
        try {
          const enrollment = await api.training.enrollInCourse(data);
          set((state) => ({
            enrollments: [enrollment, ...state.enrollments],
          }));
          toast.success('Successfully enrolled in course');
          return true;
        } catch (error: any) {
          toast.error(error.response?.data?.message || 'Failed to enroll in course');
          return false;
        }
      },

      updateEnrollment: async (id: string, data: any) => {
        try {
          const updated = await api.training.updateEnrollment(id, data);
          set((state) => ({
            enrollments: state.enrollments.map((e) => (e.id === id ? updated : e)),
          }));
          toast.success('Enrollment updated successfully');
          return true;
        } catch (error: any) {
          toast.error('Failed to update enrollment');
          return false;
        }
      },

      // Recommendation Actions
      fetchRecommendations: async () => {
        set({ recommendationsLoading: true });
        try {
          const recommendations = await api.training.getRecommendations();
          set({ recommendations, recommendationsLoading: false });
        } catch (error: any) {
          set({ recommendationsLoading: false });
          toast.error('Failed to fetch recommendations');
        }
      },

      generateRecommendations: async () => {
        set({ recommendationsLoading: true });
        try {
          const recommendations = await api.training.generateRecommendations();
          set({ recommendations, recommendationsLoading: false });
          toast.success('New recommendations generated');
        } catch (error: any) {
          set({ recommendationsLoading: false });
          toast.error('Failed to generate recommendations');
        }
      },

      updateRecommendationStatus: async (id: string, status: 'accepted' | 'declined') => {
        try {
          await api.training.updateRecommendationStatus(id, status);
          set((state) => ({
            recommendations: state.recommendations.map((r) =>
              r.id === id ? { ...r, status } : r
            ),
          }));
          
          if (status === 'accepted') {
            toast.success('Recommendation accepted');
          }
          return true;
        } catch (error: any) {
          toast.error('Failed to update recommendation');
          return false;
        }
      },

      // Skills Actions
      fetchSkills: async (category?: string) => {
        try {
          const skills = await api.training.getSkills(category);
          set({ skills });
        } catch (error) {
          toast.error('Failed to fetch skills');
        }
      },

      fetchUserSkills: async (userId?: string) => {
        try {
          const userSkills = await api.training.getUserSkills(userId);
          set({ userSkills });
        } catch (error) {
          toast.error('Failed to fetch user skills');
        }
      },

      fetchSkillGaps: async (userId?: string) => {
        try {
          const skillGaps = await api.training.getSkillGaps(userId);
          set({ skillGaps });
        } catch (error) {
          toast.error('Failed to fetch skill gaps');
        }
      },

      analyzeSkillGaps: async (userId?: string) => {
        try {
          const skillGaps = await api.training.analyzeSkillGaps(userId);
          set({ skillGaps });
          toast.success('Skill gap analysis completed');
        } catch (error) {
          toast.error('Failed to analyze skill gaps');
        }
      },

      // Assessment Actions
      fetchAssessments: async (filters?: any) => {
        try {
          const response = await api.training.getAssessments(filters);
          set({ assessments: response.assessments });
        } catch (error) {
          toast.error('Failed to fetch assessments');
        }
      },

      fetchAssessmentById: async (id: string) => {
        try {
          const assessment = await api.training.getAssessmentById(id);
          set({ selectedAssessment: assessment });
        } catch (error) {
          toast.error('Failed to fetch assessment details');
        }
      },

      createAssessment: async (data: any) => {
        try {
          const assessment = await api.training.createAssessment(data);
          set((state) => ({
            assessments: [assessment, ...state.assessments],
          }));
          toast.success('Assessment created successfully');
          return assessment;
        } catch (error) {
          toast.error('Failed to create assessment');
          return null;
        }
      },

      submitAssessmentResponse: async (assessmentId: string, data: any) => {
        try {
          await api.training.submitAssessmentResponse(assessmentId, data);
          toast.success('Assessment response submitted');
          return true;
        } catch (error) {
          toast.error('Failed to submit assessment response');
          return false;
        }
      },

      // Learning Path Actions
      fetchLearningPaths: async (roleId?: string) => {
        try {
          const learningPaths = await api.training.getLearningPaths(roleId);
          set({ learningPaths });
        } catch (error) {
          toast.error('Failed to fetch learning paths');
        }
      },

      // Analytics Actions
      fetchTrainingMetrics: async (departmentId?: string) => {
        try {
          const metrics = await api.training.getTrainingMetrics(departmentId);
          set({ metrics });
        } catch (error) {
          toast.error('Failed to fetch training metrics');
        }
      },

      fetchUserProgress: async (userId?: string) => {
        try {
          const userProgress = await api.training.getUserProgress(userId);
          set({ userProgress });
        } catch (error) {
          toast.error('Failed to fetch user progress');
        }
      },

      fetchCourseAnalytics: async (courseId: string) => {
        try {
          return await api.training.getCourseAnalytics(courseId);
        } catch (error) {
          toast.error('Failed to fetch course analytics');
          return null;
        }
      },

      // Utility Actions
      clearSelectedCourse: () => set({ selectedCourse: null }),
      clearSelectedAssessment: () => set({ selectedAssessment: null }),
      resetState: () => set(initialState),
    })),
    {
      name: 'training-store',
    }
  )
);

// Selector hooks
export const useCourses = () => useTrainingStore((state) => state.courses);
export const useSelectedCourse = () => useTrainingStore((state) => state.selectedCourse);
export const useEnrollments = () => useTrainingStore((state) => state.enrollments);
export const useRecommendations = () => useTrainingStore((state) => state.recommendations);
export const useSkills = () => useTrainingStore((state) => state.skills);
export const useUserSkills = () => useTrainingStore((state) => state.userSkills);
export const useSkillGaps = () => useTrainingStore((state) => state.skillGaps);
export const useAssessments = () => useTrainingStore((state) => state.assessments);
export const useLearningPaths = () => useTrainingStore((state) => state.learningPaths);
export const useTrainingMetrics = () => useTrainingStore((state) => state.metrics);
export const useUserProgress = () => useTrainingStore((state) => state.userProgress);