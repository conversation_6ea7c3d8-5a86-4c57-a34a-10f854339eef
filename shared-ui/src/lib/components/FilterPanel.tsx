import React from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import { Checkbox } from './ui/checkbox'
import { RadioGroup, RadioGroupItem } from './ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { X } from 'lucide-react'

interface FilterOption {
  value: string
  label: string
  count?: number
}

interface FilterGroup {
  id: string
  label: string
  type: 'checkbox' | 'radio' | 'select'
  options: FilterOption[]
}

interface FilterPanelProps {
  filters: FilterGroup[]
  values: Record<string, string | string[]>
  onChange: (filterId: string, value: string | string[]) => void
  onReset?: () => void
  className?: string
}

export function FilterPanel({
  filters,
  values,
  onChange,
  onReset,
  className = '',
}: FilterPanelProps) {
  const handleCheckboxChange = (filterId: string, optionValue: string, checked: boolean) => {
    const currentValues = (values[filterId] as string[]) || []
    const newValues = checked
      ? [...currentValues, optionValue]
      : currentValues.filter(v => v !== optionValue)
    onChange(filterId, newValues)
  }

  const renderFilterGroup = (group: FilterGroup) => {
    switch (group.type) {
      case 'checkbox':
        return (
          <div className="space-y-2">
            {group.options.map(option => {
              const currentValues = (values[group.id] as string[]) || []
              const isChecked = currentValues.includes(option.value)
              
              return (
                <label
                  key={option.value}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <Checkbox
                    checked={isChecked}
                    onCheckedChange={(checked) => 
                      handleCheckboxChange(group.id, option.value, checked as boolean)
                    }
                  />
                  <span className="text-sm flex-1">{option.label}</span>
                  {option.count !== undefined && (
                    <span className="text-xs text-muted-foreground">({option.count})</span>
                  )}
                </label>
              )
            })}
          </div>
        )

      case 'radio':
        return (
          <RadioGroup
            value={values[group.id] as string || ''}
            onValueChange={(value) => onChange(group.id, value)}
          >
            {group.options.map(option => (
              <label
                key={option.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <RadioGroupItem value={option.value} />
                <span className="text-sm flex-1">{option.label}</span>
                {option.count !== undefined && (
                  <span className="text-xs text-muted-foreground">({option.count})</span>
                )}
              </label>
            ))}
          </RadioGroup>
        )

      case 'select':
        return (
          <Select
            value={values[group.id] as string || ''}
            onValueChange={(value) => onChange(group.id, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              {group.options.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                  {option.count !== undefined && ` (${option.count})`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
    }
  }

  const hasActiveFilters = Object.values(values).some(value => 
    Array.isArray(value) ? value.length > 0 : !!value
  )

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Filters</CardTitle>
        {hasActiveFilters && onReset && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onReset}
            className="h-8 px-2 lg:px-3"
          >
            Clear all
            <X className="ml-1 h-4 w-4" />
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {filters.map(group => (
          <div key={group.id} className="space-y-3">
            <Label className="text-sm font-medium">{group.label}</Label>
            {renderFilterGroup(group)}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}