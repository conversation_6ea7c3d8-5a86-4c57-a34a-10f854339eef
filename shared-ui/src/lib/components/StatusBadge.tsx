import React from 'react'
import { Badge } from './ui/badge'

type StatusType = 
  | 'draft'
  | 'submitted'
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'completed'
  | 'in_progress'
  | 'active'
  | 'inactive'
  | 'success'
  | 'error'
  | 'warning'

interface StatusBadgeProps {
  status: StatusType | string
  label?: string
  size?: 'sm' | 'md' | 'lg'
}

const statusConfig: Record<StatusType, { color: string; defaultLabel: string }> = {
  draft: { color: 'bg-gray-100 text-gray-800', defaultLabel: 'Draft' },
  submitted: { color: 'bg-blue-100 text-blue-800', defaultLabel: 'Submitted' },
  pending: { color: 'bg-yellow-100 text-yellow-800', defaultLabel: 'Pending' },
  approved: { color: 'bg-green-100 text-green-800', defaultLabel: 'Approved' },
  rejected: { color: 'bg-red-100 text-red-800', defaultLabel: 'Rejected' },
  completed: { color: 'bg-purple-100 text-purple-800', defaultLabel: 'Completed' },
  in_progress: { color: 'bg-orange-100 text-orange-800', defaultLabel: 'In Progress' },
  active: { color: 'bg-emerald-100 text-emerald-800', defaultLabel: 'Active' },
  inactive: { color: 'bg-gray-100 text-gray-800', defaultLabel: 'Inactive' },
  success: { color: 'bg-green-100 text-green-800', defaultLabel: 'Success' },
  error: { color: 'bg-red-100 text-red-800', defaultLabel: 'Error' },
  warning: { color: 'bg-yellow-100 text-yellow-800', defaultLabel: 'Warning' },
}

export function StatusBadge({ status, label, size = 'md' }: StatusBadgeProps) {
  const config = statusConfig[status as StatusType] || {
    color: 'bg-gray-100 text-gray-800',
    defaultLabel: status,
  }

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1',
  }

  return (
    <span
      className={`inline-flex items-center font-medium rounded-full ${config.color} ${sizeClasses[size]}`}
    >
      {label || config.defaultLabel}
    </span>
  )
}