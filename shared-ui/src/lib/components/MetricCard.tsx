import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card'
import { LucideIcon } from 'lucide-react'

interface MetricCardProps {
  title: string
  value: string | number
  subtitle?: string
  trend?: {
    value: number
    label: string
  }
  icon?: LucideIcon
  iconColor?: string
}

export function MetricCard({ 
  title, 
  value, 
  subtitle, 
  trend, 
  icon: Icon,
  iconColor = 'text-muted-foreground'
}: MetricCardProps) {
  const isPositiveTrend = trend && trend.value > 0

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && <Icon className={`h-4 w-4 ${iconColor}`} />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && (
          <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
        )}
        {trend && (
          <p className={`text-xs mt-2 flex items-center ${
            isPositiveTrend ? 'text-green-600' : 'text-red-600'
          }`}>
            <span className="font-medium">
              {isPositiveTrend ? '+' : ''}{trend.value}%
            </span>
            <span className="ml-1 text-muted-foreground">{trend.label}</span>
          </p>
        )}
      </CardContent>
    </Card>
  )
}