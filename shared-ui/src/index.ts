// Main export file for Luminar UI Library

// Export all API functionality
export * from './lib/api';
export { apiClient, api } from './lib/api';

// Export all stores
export * from './lib/stores';

// Export toast functionality
export * from './lib/toast';

// Export utility functions
export * from './lib/utils';

// Export design system utilities
export * from './lib/design-tokens';
export * from './lib/theme-manager';
export * from './lib/glassmorphism';
export * from './lib/glass-utils';
export * from './lib/visual-effects';
export * from './lib/animation-utils';
export * from './lib/micro-interactions';

// Export chart utilities
export * from './lib/chart-utils';

// Export AI-powered features
export * from './lib/neural-color-intelligence';
export * from './lib/predictive-ui-engine';
export * from './lib/ai-component-generator';
export * from './lib/component-factory';

// Export performance utilities
export * from './lib/build-performance';
export * from './lib/build-optimizer';
export { PerformanceMonitor } from './lib/performance-monitor';

// Export live editor for development
export { LiveEditor } from './lib/live-editor';

// Export React components
export * from './components/ui';
export * from './components/auth';
export * from './components/glass';

// Export shared components
export * from './lib/components/PageHeader';
export * from './lib/components/MetricCard';
export * from './lib/components/DataTable';
export * from './lib/components/StatusBadge';
export * from './lib/components/EmptyState';
export * from './lib/components/LoadingSpinner';
export * from './lib/components/SearchInput';
export * from './lib/components/ConfirmDialog';
export * from './lib/components/DateRangePicker';
export * from './lib/components/FilterPanel';

// Export hooks
export { useTheme } from './hooks/use-theme';
export { useAIChat } from './hooks/use-ai-chat';
export { useResearchState } from './hooks/use-research-state';
export { useAMNAState } from './hooks/useAMNAState';

// Export providers
export { ThemeProvider } from './providers/theme-provider';
export { AIProvider } from './providers/ai-provider';

// Export types
export * from './types/ai';
export * from './types/research';

// Export design system configuration
export { designSystem } from './design-system';